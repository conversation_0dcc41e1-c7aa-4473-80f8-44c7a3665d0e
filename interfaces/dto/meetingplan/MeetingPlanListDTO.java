package cn.july.orch.meeting.interfaces.dto.meetingplan;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划列表DTO
 * @date 2025-01-24
 */
@Data
public class MeetingPlanListDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会议标题
     */
    private String title;

    /**
     * 计划开始时间
     */
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime plannedEndTime;

    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 会议类型
     */
    private String meetingType;

    /**
     * 参会人员数量
     */
    private Integer attendeeCount;

    /**
     * 主持人
     */
    private String host;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
