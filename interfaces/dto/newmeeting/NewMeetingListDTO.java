package cn.july.orch.meeting.interfaces.dto.newmeeting;

import cn.july.orch.meeting.interfaces.enums.NewMeetingStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 新会议列表DTO
 * @date 2025-01-24
 */
@Data
public class NewMeetingListDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会议标题
     */
    private String title;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 会议类型
     */
    private String meetingType;

    /**
     * 会议状态
     */
    private NewMeetingStatusEnum status;

    /**
     * 主持人
     */
    private String host;

    /**
     * 参会人员数量
     */
    private Integer attendeeCount;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
