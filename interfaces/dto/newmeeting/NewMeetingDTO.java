package cn.july.orch.meeting.interfaces.dto.newmeeting;

import cn.july.orch.meeting.interfaces.enums.NewMeetingStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议详情DTO
 * @date 2025-01-24
 */
@Data
public class NewMeetingDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会议标题
     */
    private String title;

    /**
     * 会议描述
     */
    private String description;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 会议类型
     */
    private String meetingType;

    /**
     * 会议状态
     */
    private NewMeetingStatusEnum status;

    /**
     * 参会人员
     */
    private List<String> attendees;

    /**
     * 主持人
     */
    private String host;

    /**
     * 记录人
     */
    private String recorder;

    /**
     * 飞书日程事件ID
     */
    private String fsCalendarEventId;

    /**
     * 飞书会议ID
     */
    private String fsMeetingId;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
