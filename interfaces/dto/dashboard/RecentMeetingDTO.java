package cn.july.orch.meeting.interfaces.dto.dashboard;

import cn.july.orch.meeting.interfaces.dto.user.FSUserInfoDTO;
import cn.july.orch.meeting.interfaces.enums.NewMeetingStatusEnum;
import cn.july.orch.meeting.interfaces.enums.PriorityLevelEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 近期会议数据传输对象
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecentMeetingDTO {

    @ApiModelProperty("会议ID")
    private Long id;

    @ApiModelProperty("会议名称")
    private String meetingName;

    @ApiModelProperty("会议描述")
    private String meetingDescription;

    @ApiModelProperty("会议规划ID")
    private Long meetingPlanId;

    @ApiModelProperty("会议标准ID")
    private Long meetingStandardId;

    @ApiModelProperty("会议编号")
    private String meetingNo;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    @ApiModelProperty("会议状态")
    private NewMeetingStatusEnum status;

    @ApiModelProperty("优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty("会议地点")
    private String meetingLocation;

    @ApiModelProperty("参会人员列表")
    private List<String> attendees;

    @ApiModelProperty("参会人员详细信息列表")
    private List<FSUserInfoDTO> attendeeDetails;

    @ApiModelProperty("参与人数")
    private Integer attendeeCount;

    @ApiModelProperty("主持人ID")
    private String hostUserId;

    @ApiModelProperty("记录员ID")
    private String recorderUserId;

    @ApiModelProperty("飞书日程事件ID")
    private String fsCalendarEventId;

    @ApiModelProperty("飞书会议ID")
    private String fsMeetingId;

    @ApiModelProperty("会议链接")
    private String meetingUrl;

    @ApiModelProperty("妙计链接")
    private String minuteUrl;

    @ApiModelProperty("是否有纪要")
    private Boolean hasMinutes;

    @ApiModelProperty("任务数量")
    private Integer taskCount;

    @ApiModelProperty("是否有议程")
    private Boolean hasAgenda;

    @ApiModelProperty("是否有资料")
    private Boolean hasMaterials;

    @ApiModelProperty("创建人ID")
    private String createUserId;

    @ApiModelProperty("创建人姓名")
    private String createUserName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
}
