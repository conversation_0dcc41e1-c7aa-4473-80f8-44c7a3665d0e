package cn.july.orch.meeting.interfaces.dto.meetingstandard;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准详情DTO
 * @date 2025-01-24
 */
@Data
public class MeetingStandardDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会议标准名称
     */
    private String name;

    /**
     * 会议标准描述
     */
    private String description;

    /**
     * 会议类型
     */
    private String meetingType;

    /**
     * 建议参会人数
     */
    private Integer suggestedAttendeeCount;

    /**
     * 最小参会人数
     */
    private Integer minAttendeeCount;

    /**
     * 最大参会人数
     */
    private Integer maxAttendeeCount;

    /**
     * 建议会议时长（分钟）
     */
    private Integer suggestedDuration;

    /**
     * 必需角色
     */
    private List<String> requiredRoles;

    /**
     * 可选角色
     */
    private List<String> optionalRoles;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
