package cn.july.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 时间单位枚举
 * @date 2025-01-24
 */
@Getter
@AllArgsConstructor
public enum TimeUnitEnum {

    DAY("DAY", "天"),
    WEEK("WEEK", "周"),
    MONTH("MONTH", "月"),
    YEAR("YEAR", "年");

    @EnumValue
    @JsonValue
    private final String code;
    private final String name;
}
