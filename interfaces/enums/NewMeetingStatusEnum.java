package cn.july.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 新会议状态枚举
 * @date 2025-01-24
 */
@Getter
@AllArgsConstructor
public enum NewMeetingStatusEnum {

    DRAFT("DRAFT", "草稿"),
    SCHEDULED("SCHEDULED", "已安排"),
    IN_PROGRESS("IN_PROGRESS", "进行中"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELLED("CANCELLED", "已取消");

    @EnumValue
    @JsonValue
    private final String code;
    private final String name;
}
