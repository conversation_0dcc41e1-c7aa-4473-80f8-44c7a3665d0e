package cn.july.orch.meeting.interfaces.query.newmeeting;

import cn.july.core.model.page.PageSortQuery;
import cn.july.orch.meeting.interfaces.enums.NewMeetingStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 新会议查询
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class NewMeetingQuery extends PageSortQuery {

    /**
     * 会议标题
     */
    private String title;

    /**
     * 会议状态
     */
    private NewMeetingStatusEnum status;

    /**
     * 会议类型
     */
    private String meetingType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 参会人ID
     */
    private Long attendeeId;

    /**
     * 部门ID
     */
    private Long departmentId;
}
