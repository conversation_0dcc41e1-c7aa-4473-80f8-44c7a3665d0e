package cn.july.orch.meeting.interfaces.query;

import cn.july.orch.meeting.interfaces.enums.TimeUnitEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 仪表板趋势查询
 * @date 2025-01-24
 */
@Data
public class DashboardTrendQuery {

    /**
     * 时间单位
     */
    private TimeUnitEnum timeUnit;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 趋势类型
     */
    private String trendType;
}
