package cn.july.orch.meeting.interfaces.query.meetingstandard;

import cn.july.core.model.page.PageSortQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description 会议标准查询
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MeetingStandardQuery extends PageSortQuery {

    /**
     * 会议标准名称
     */
    private String name;

    /**
     * 会议类型
     */
    private String meetingType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 部门ID
     */
    private Long departmentId;
}
