package cn.july.orch.meeting.interfaces.query;

import cn.july.core.model.page.PageSortQuery;
import cn.july.orch.meeting.interfaces.enums.TaskPriorityEnum;
import cn.july.orch.meeting.interfaces.enums.TaskStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 任务列表查询
 * @date 2025-01-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskListQuery extends PageSortQuery {

    /**
     * 任务标题
     */
    private String title;

    /**
     * 任务状态
     */
    private TaskStatusEnum status;

    /**
     * 任务优先级
     */
    private TaskPriorityEnum priority;

    /**
     * 负责人ID
     */
    private Long assigneeId;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 部门ID
     */
    private Long departmentId;
}
