package cn.july.orch.meeting.interfaces.query;

import cn.july.core.model.page.PageSortQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 近期会议查询
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecentMeetingQuery extends PageSortQuery {

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会议状态
     */
    private String status;
}
