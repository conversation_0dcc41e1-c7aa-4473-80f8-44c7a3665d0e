package cn.july.feishu.util;

import cn.july.core.utils.http.OkHttpUtil;
import cn.july.core.utils.http.model.HttpResponse;
import cn.july.core.utils.jackson.JsonUtils;
import cn.july.feishu.exception.FeishuErrorCode;
import cn.july.feishu.exception.ThirdException;
import cn.july.feishu.function.FeishuMsgExtractor;
import cn.july.feishu.function.FeishuNoReqExecutor;
import cn.july.feishu.function.FeishuRequestExecutor;
import cn.july.feishu.function.FeishuRequestExecutorWithOptions;
import cn.july.feishu.model.common.FeishuV2Response;
import com.lark.oapi.core.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 飞书调用工具
 * @date 2024-12-25
 */
@Slf4j
public class FeishuInvokeUtil {

    private static <R> R handleResponse(BaseResponse<R> response, FeishuErrorCode errorCode, long startTime) {
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        if (!response.success()) {
            log.error("Feishu Request failed, code: {}, msg: {}", response.getCode(), response.getMsg());
            throw new ThirdException(errorCode.buildCode(), response.getMsg());
        }
        log.info("Feishu Response data: {}, took {} ms.", JsonUtils.toJson(response.getData()), executionTime);
        return response.getData();
    }

    public static <T, R> R executeRequest(T request, FeishuRequestExecutor<T, R> executor, FeishuErrorCode errorCode) {
        log.info("Feishu Request: {}", JsonUtils.toJson(request));
        try {
            long startTime = System.currentTimeMillis();
            BaseResponse<R> response = executor.execute(request);
            return handleResponse(response, errorCode, startTime);
        } catch (Exception e) {
            log.error("Feishu Request error: {}", e.getMessage(), e);
            throw new ThirdException(errorCode);
        }
    }

    public static <T, U, R> R executeRequest(T request, U options, FeishuRequestExecutorWithOptions<T, U, R> executor, FeishuErrorCode errorCode) {
        log.info("Feishu Request: {}", JsonUtils.toJson(request));
        try {
            long startTime = System.currentTimeMillis();
            BaseResponse<R> response = executor.execute(request, options);
            return handleResponse(response, errorCode, startTime);
        } catch (Exception e) {
            log.error("Feishu Request error: {}", e.getMessage(), e);
            throw new ThirdException(errorCode);
        }
    }

    public static <R> R executeRequest(FeishuNoReqExecutor<R> executor, FeishuErrorCode errorCode) {
        try {
            long startTime = System.currentTimeMillis();
            BaseResponse<R> response = executor.execute();
            return handleResponse(response, errorCode, startTime);
        } catch (Exception e) {
            log.error("Feishu Request error: {}", e.getMessage(), e);
            throw new ThirdException(errorCode);
        }
    }

    /**
     * 适用okhttp调用方式
     * @param url 请求地址
     * @param headers 请求头
     * @param requestModel 请求参数
     * @param responseType 响应类型
     * @param errorCode 异常code
     * @param msgExtractor 异常返回消息字段
     */
    public static <T extends FeishuV2Response> T executeRequest(String url, Map<String, String> headers, Object requestModel,
                                                                Class<T> responseType, FeishuErrorCode errorCode,
                                                                FeishuMsgExtractor<T> msgExtractor) {
        log.info("Feishu url:{},RequestModel: {}",url, JsonUtils.toJson(requestModel));
        try (HttpResponse httpResponse = OkHttpUtil.getInstance().postJson(url,headers, JsonUtils.toJson(requestModel))) {
            if (httpResponse.getCode() != 200) {
                log.error("Feishu request failed with status code: {}", httpResponse.getCode());
                throw new ThirdException(errorCode.buildCode(), "HTTP request failed");
            }
            T resp = httpResponse.toObj(responseType);
            if (resp != null && resp.getCode() != 0) {
                String msg = msgExtractor.execute(resp);
                log.error("Feishu request fail, code: {}, msg: {}", resp.getCode(), msg);
                throw new ThirdException(errorCode.buildCode(), msg);
            }
            log.info("Feishu request token: {}", JsonUtils.toJson(resp));
            return resp;
        } catch (IOException e) {
            log.error("Feishu request error", e);
            throw new ThirdException(errorCode);
        }
    }

}
