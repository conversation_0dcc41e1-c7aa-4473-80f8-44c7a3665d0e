package cn.july.feishu.config;

import com.lark.oapi.Client;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppConfig {

    /**
     * 小程序appId
     */
    private String appId;

    /**
     * 小程序appSecret
     */
    private String appSecret;

    private Client feishuClient;

    private StringRedisTemplate redisTemplate;

}
