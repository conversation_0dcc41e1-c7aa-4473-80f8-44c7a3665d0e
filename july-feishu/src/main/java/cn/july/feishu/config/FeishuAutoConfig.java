package cn.july.feishu.config;

import cn.hutool.core.util.ObjUtil;
import cn.july.feishu.FeishuAppClient;
import cn.july.feishu.exception.ThirdException;
import cn.july.feishu.properties.FeishuProperties;
import com.lark.oapi.Client;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;

@Configuration
@ConditionalOnClass({Client.class, FeishuAppClient.class})
@ConditionalOnProperty(prefix = "feishu.mini", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(FeishuProperties.class)
public class FeishuAutoConfig {

    @Bean
    @ConditionalOnMissingBean
    public Client feishuClient(FeishuProperties properties){
        if (properties.isEnabled()) {
            return Client.newBuilder(properties.getAppId(), properties.getAppSecret())
                    .requestTimeout(3, TimeUnit.SECONDS) // 设置httpclient 超时时间，默认永不超时
                    .build();
        }
        throw new ThirdException("飞书配置异常!");
    }

    @Bean
    @ConditionalOnMissingBean
    public FeishuAppClient feishuAppClient(FeishuProperties properties, Client feishuClient, StringRedisTemplate redisTemplate) {
        if(ObjUtil.isNotNull(feishuClient)){
            return new FeishuAppClient
                    .Builder(properties,feishuClient,redisTemplate)
                    .build();
        }
        throw new ThirdException("飞书配置异常!!");
    }
}
