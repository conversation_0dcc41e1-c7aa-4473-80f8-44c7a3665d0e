package cn.july.feishu.config;

import cn.july.core.utils.jackson.JsonUtils;
import cn.july.core.utils.thread.BaseThreadDecorator;
import cn.july.core.utils.thread.ThreadDecorator;

/**
 * 线程装饰器,用于多线程传递飞书信息
 */
public class FeishuAppDecorator extends BaseThreadDecorator {
    public FeishuAppDecorator() {
        super();
    }

    public FeishuAppDecorator(ThreadDecorator threadDecorator) {
        super(threadDecorator);
    }
    @Override
    protected Object beforeExecOnCurrThread() {
        return FeishuAppContext.get();
    }

    @Override
    protected void doOnNewThread(Object object) {
        if (object instanceof FeishuAppContext) {
            FeishuAppContext.set((FeishuAppContext) JsonUtils.clone(object, FeishuAppContext.class));
        }
    }

    @Override
    protected void afterExecOnNewThread() {
        FeishuAppContext.remove();
    }
}
