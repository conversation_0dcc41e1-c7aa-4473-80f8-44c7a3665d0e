package cn.july.feishu.common;

public class FeishuConstants {

    /**
     * 基础url
     */
    private static final String BASE_URL = "https://open.feishu.cn/open-apis";

    /**
     * 自建运营获取tenant_token
     */
    private static final String AUTH_TENANT_TOKEN_URL = "/auth/v3/tenant_access_token/internal";

    /**
     * 获取或刷新user_access_token
     */
    private static final String AUTH_USER_ACCESS_TOKEN_URL = "/authen/v2/oauth/token";

    /**
     * 模糊搜索用户
     */
    private static final String CONTACT_SEARCH_USER_URL = "/search/v1/user";

    /**
     * 获取JSAPI临时授权凭证
     */
    private static final String JS_SDK_TICKET_GET = "/jssdk/ticket/get";

    /**
     * 飞书审核上传文件
     */
    private static final String APPROVAL_UPLOAD_URL = "https://www.feishu.cn/approval/openapi/v2/file/upload";

    public static String getAuthTenantTokenUrl() {
        return BASE_URL + AUTH_TENANT_TOKEN_URL;
    }

    public static String getAuthUserAccessTokenUrl() {
        return BASE_URL + AUTH_USER_ACCESS_TOKEN_URL;
    }

    public static String getContactSearchUserUrl() {
        return BASE_URL + CONTACT_SEARCH_USER_URL;
    }

    public static String getApprovalUploadUrl() {
        return APPROVAL_UPLOAD_URL;
    }

    public static String getJsSdkTicketGetUrl() {
        return BASE_URL + JS_SDK_TICKET_GET;
    }


}
