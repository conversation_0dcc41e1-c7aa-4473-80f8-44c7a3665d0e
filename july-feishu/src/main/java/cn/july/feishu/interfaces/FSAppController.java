package cn.july.feishu.interfaces;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.feishu.FeishuAppClient;
import cn.july.feishu.model.command.DepartmentCommand;
import cn.july.feishu.properties.FeishuProperties;
import com.lark.oapi.service.contact.v3.model.Department;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 飞书应用信息
 *
 * <AUTHOR>
 */
@Api(tags = "飞书应用信息")
@RestController
@RequestMapping("/app")
@RequiredArgsConstructor
public class FSAppController {

    private final FeishuProperties feishuProperties;

    @Resource
    private FeishuAppClient feishuAppClient;

    @PostMapping("/id")
    @ApiOperation(value = "获取飞书appId")
    public String id() {
        return feishuProperties.getAppId();
    }

    @PostMapping("/getDepartmentByIds")
    @ApiOperation(value = "批量查询飞书部门信息")
    public List<Department> getDepartmentByIds(@RequestBody DepartmentCommand command) {
        return feishuAppClient.getContactService().getDepartmentBatch(command.getDepartmentIds());
    }

    @PostMapping("/getChildDepartments")
    @ApiOperation(value = "获取子部门信息")
    public List<Department> getChildDepartments(@RequestParam(value = "departmentId", required = false) String departmentId) {
        if (StrUtil.isBlank(departmentId)) {
            List<String> contactScope = feishuAppClient.getContactService().getContactScope();
            return feishuAppClient.getContactService().getDepartmentBatch(contactScope);
        }
        return feishuAppClient.getContactService().getDepartmentChildren(departmentId);
    }

    @PostMapping("/getParentDepartments")
    @ApiOperation(value = "获取父部门信息,子部门在前")
    public Map<String, List<Department>> getParentDepartments(@RequestBody List<String> departmentIds) {
        Map<String, List<Department>> result = new HashMap<>();
        for (String departmentId : departmentIds) {
            List<Department> departmentBatch = feishuAppClient.getContactService().getDepartmentBatch(Collections.singletonList(departmentId));
            if(CollUtil.isEmpty(departmentBatch)){
                result.put(departmentId, new ArrayList<>());
                continue;
            }
            List<Department> parentDepartment = new ArrayList<>(feishuAppClient.getContactService().getParentDepartment(departmentId));
            parentDepartment.add(departmentBatch.get(0));
            result.put(departmentId, parentDepartment);
        }
        return result;
    }
}
