package cn.july.feishu.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FeishuTaskStatusEnum {

    PENDING("PENDING","审批中"),
    APPROVED("APPROVED","通过"),
    REJECTED("REJECTED","拒绝"),
    TRANSFERRED("TRANSFERRED","已转交"),
    DONE("DONE","完成"),
    ;

    /**
     * code来源与飞书的事件配置
     */
    @JsonValue
    private final String code;

    private final String description;
}
