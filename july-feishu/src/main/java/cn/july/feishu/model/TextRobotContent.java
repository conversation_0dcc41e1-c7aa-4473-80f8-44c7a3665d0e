package cn.july.feishu.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 普通文本消息内容
 * @see cn.july.third.feishu.robot.enums.MessageType#TEXT
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TextRobotContent extends RobotContent {

    /**
     * 文本内容
     */
    private String text;

    public TextRobotContent(String text) {
        this.text = text;
    }

    public void append(String text) {
        this.text += text;
    }

}
