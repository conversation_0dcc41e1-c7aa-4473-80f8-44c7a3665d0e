package cn.july.feishu.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetCalendarEventInstancesModel {

    // 日历ID
    private String calendarId;

    // 日程ID
    private String eventId;

    // 日程开始时间
    private LocalDateTime startTime;

    // 日程结束时间
    private LocalDateTime endTime;

    // 一次请求要求返回的最大日程数量
    private Integer pageSize = 500;

    // 分页标记
    private String pageToken;
}
