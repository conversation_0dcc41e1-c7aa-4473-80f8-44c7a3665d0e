package cn.july.feishu.model;



import cn.july.feishu.model.enums.LangType;
import cn.july.feishu.model.enums.MessageType;
import cn.july.feishu.model.post.tag.*;
import cn.july.feishu.model.post.Lang;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class RobotRequestBuilder {

    public static class Text {

        private final RobotRequest robotRequest;

        public Text() {
            robotRequest = new RobotRequest();
            robotRequest.setMsgType(MessageType.TEXT);
            robotRequest.setContent(new TextRobotContent(""));
        }

        public static Text create() {
            return new Text();
        }

        public Text content(String text) {
            ((TextRobotContent) robotRequest.getContent()).append(text);
            return this;
        }

        public Text at(String... atOpenIds) {
            if (atOpenIds != null && atOpenIds.length > 0) {
                StringBuilder at = new StringBuilder();
                for (String atOpenId : atOpenIds) {
                    at.append("<at user_id=\"").append(atOpenId).append("\"></at>");
                }
                ((TextRobotContent) robotRequest.getContent()).append(at.toString());
            }
            return this;
        }

        public Text atAll() {
            ((TextRobotContent) robotRequest.getContent()).append("<at user_id=\"all\"/>所有人</at>");
            return this;
        }

        public RobotRequest build() {
            return robotRequest;
        }
    }

    public static class Post {

        private final RobotRequest robotRequest;
        private Lang defaultLang;
        private List<PostTag> lastTags;

        /**
         * 默认初始化中文富文本
         */
        public Post(LangType langType) {
            robotRequest = new RobotRequest();
            robotRequest.setMsgType(MessageType.POST);
            PostRobotContent robotContent = new PostRobotContent();
            cn.july.feishu.model.post.Post post = new cn.july.feishu.model.post.Post();
            defaultLang = new Lang();
            if (langType == LangType.ZH_CN) {
                post.setZhCn(defaultLang);
            } else {
                post.setEnUs(defaultLang);
            }
            this.lastTags = new ArrayList<>();
            List<List<PostTag>> content = new ArrayList<>();
            content.add(lastTags);
            defaultLang.setContent(content);
            robotContent.setPost(post);
            robotRequest.setContent(robotContent);
        }

        public static Post create(LangType langType) {
            return new Post(langType);
        }

        public Post title(String title) {
            defaultLang.setTitle(title);
            return this;
        }

        public Post text(String text) {
            lastTags.add(new TextTag(text));
            return this;
        }

        public Post text(String text, boolean unEscape) {
            lastTags.add(new TextTag(text, unEscape));
            return this;
        }

        public Post at(String openid) {
            lastTags.add(new AtTag(openid));
            return this;
        }

        public Post atAll() {
            lastTags.add(new AtTag("all"));
            return this;
        }

        public Post at(String openid, String userName) {
            lastTags.add(new AtTag(openid, userName));
            return this;
        }

        public Post img(String imgKey) {
            lastTags.add(new ImgTag(imgKey));
            return this;
        }

        public Post href(String text, String href) {
            lastTags.add(new ATag(text, href));
            return this;
        }

        /**
         * 开启一个新的内容块
         *
         * @return
         */
        public Post newParagraph() {
            lastTags = new ArrayList<>();
            List<List<PostTag>> zhCnContent = defaultLang.getContent();
            zhCnContent.add(lastTags);
            return this;
        }

        /**
         * 新增语言
         * 注意,新增之后,所有的设置项根据最后一项语言设置
         */
        public Post newLang(LangType langType) {
            defaultLang = new Lang();
            if (langType == LangType.ZH_CN) {
                (((PostRobotContent) robotRequest.getContent()).getPost()).setZhCn(defaultLang);
            } else {
                (((PostRobotContent) robotRequest.getContent()).getPost()).setEnUs(defaultLang);
            }
            this.lastTags = new ArrayList<>();
            List<List<PostTag>> content = new ArrayList<>();
            content.add(lastTags);
            defaultLang.setContent(content);
            return this;
        }

        public RobotRequest build() {
            return robotRequest;
        }

    }

    public static class ShareChat {

        private final RobotRequest robotRequest;

        public ShareChat() {
            robotRequest = new RobotRequest();
            robotRequest.setMsgType(MessageType.SHARE_CHAT);
            robotRequest.setContent(new ShareChatRobotContent());
        }

        public static ShareChat create() {
            return new ShareChat();
        }

        public ShareChat shareChatId(String shareChatId) {
            ((ShareChatRobotContent) robotRequest.getContent()).setShareChatId(shareChatId);
            return this;
        }

        public RobotRequest build() {
            return robotRequest;
        }
    }

    public static class Image {

        private final RobotRequest robotRequest;

        public Image() {
            robotRequest = new RobotRequest();
            robotRequest.setMsgType(MessageType.IMAGE);
            robotRequest.setContent(new ImageRobotContent());
        }

        public static Image create() {
            return new Image();
        }

        public Image imageKey(String imageKey) {
            ((ImageRobotContent) robotRequest.getContent()).setImageKey(imageKey);
            return this;
        }

        public RobotRequest build() {
            return robotRequest;
        }
    }

    public static class CardTemplate {

        private final RobotRequest robotRequest;
        private final CardTemplateData cardTemplateData;

        public CardTemplate() {
            robotRequest = new RobotRequest();
            robotRequest.setMsgType(MessageType.INTERACTIVE);
            InteractiveTemplateRobotContent robotContent = new InteractiveTemplateRobotContent();
            cardTemplateData = new CardTemplateData();
            cardTemplateData.setTemplateVariable(new HashMap<>());
            robotContent.setData(cardTemplateData);
            robotRequest.setCard(robotContent);
        }

        public static CardTemplate create() {
            return new CardTemplate();
        }

        public CardTemplate template(String templateId) {
            cardTemplateData.setTemplateId(templateId);
            return this;
        }

        public CardTemplate variable(String key, Object value) {
            cardTemplateData.getTemplateVariable().put(key, value);
            return this;
        }


        public CardTemplate variable(Map<String, Object> variable) {
            cardTemplateData.getTemplateVariable().putAll(variable);
            return this;
        }

        public RobotRequest build() {
            return robotRequest;
        }
    }
}