package cn.july.feishu.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeleteCalendarEventAttendeesModel {

    // 日历ID
    private String calendarId;

    // 日程ID
    private String eventId;

    // 参会人ID 集合
    private List<String> attendeeIds;

    // 用户ID 集合
    private List<String> userIds;
}
