package cn.july.feishu.model.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 请求表单类型,更多类型请看
 * https://open.feishu.cn/document/server-docs/approval-v4/instance/create?appId=cli_a78f67b43193d00c
 */
@Getter
@AllArgsConstructor
public enum InstanceFormTypeEnum {

    INPUT("input","单行文本"),
    TEXTAREA("textarea","多行文本"),
    ATTACHMENTV2("attachmentV2","附件"),
    IMAGE("image","图片"),
    ;

    @JsonValue
    private final String code;

    private final String description;
}
