package cn.july.feishu.model.post.tag;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TextTag extends PostTag {

    private String text;

    @JsonProperty("un_escape")
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private boolean unEscape;

    @Override
    public String getTag() {
        return "text";
    }

    public TextTag(String text) {
        this.text = text;
    }

    public TextTag(String text, boolean unEscape) {
        this.text = text;
        this.unEscape = unEscape;
    }
}
