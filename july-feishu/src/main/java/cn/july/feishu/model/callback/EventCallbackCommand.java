package cn.july.feishu.model.callback;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 飞书事件回调请求对象
 * @param <T>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EventCallbackCommand<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private String challenge;

    private String type;

    private String token;

    private String schema;

    private EventCallbackHeaderCommand header;

    private T event;
}
