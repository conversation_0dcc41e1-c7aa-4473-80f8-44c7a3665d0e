package cn.july.feishu.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetCalendarEventAttendeesModel {

    // 日历ID
    private String calendarId;

    // 日程ID
    private String eventId;

    // 一次请求返回的最大日程参与人数量
    private Integer pageSize = 100;

    // 分页标记
    private String pageToken;
}
