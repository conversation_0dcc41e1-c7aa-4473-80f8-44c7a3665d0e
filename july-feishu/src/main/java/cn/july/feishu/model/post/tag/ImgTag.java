package cn.july.feishu.model.post.tag;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ImgTag extends PostTag {

    /**
     * 图片的唯一标识。可通过 上传图片 接口获取 image_key。
     *
     * @see <a href="https://open.larksuite.com/document/server-docs/im-v1/image/create">上传图片</a>
     */
    @JsonProperty("image_key")
    private String imageKey;

    @Override
    public String getTag() {
        return "img";
    }

    public ImgTag(String imageKey) {
        this.imageKey = imageKey;
    }
}
