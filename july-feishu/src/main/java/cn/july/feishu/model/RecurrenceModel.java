package cn.july.feishu.model;

import cn.july.feishu.model.enums.CycleEnum;
import com.lark.oapi.service.sheets.v3.model.DateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecurrenceModel {

    private CycleEnum cycleEnum;

    private Integer interval;

    private DateTime until;

    private Integer count;
}
