package cn.july.feishu.model;

import com.lark.oapi.service.approval.v4.model.NodeApprover;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApprovalInstanceModel {

    /**
     * 发起人
     */
    private String openId;

    /**
     * 审核定义code
     */
    private String approvalCode;

    /**
     * 表单数据,必填字段请遵守[控件值说明]
     * https://open.feishu.cn/document/server-docs/approval-v4/instance/create?appId=cli_a78f67b43193d00c
     */
    private List<InstanceFormModel> formList;

    /**
     * 自定义审核人
     */
    private List<NodeApprover> nodeApproverOpenIdList;
}
