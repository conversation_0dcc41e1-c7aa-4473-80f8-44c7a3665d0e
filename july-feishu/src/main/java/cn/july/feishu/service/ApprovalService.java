package cn.july.feishu.service;

import cn.july.core.exception.BusinessException;
import cn.july.core.utils.http.OkHttpUtil;
import cn.july.core.utils.http.model.HttpResponse;
import cn.july.core.utils.jackson.JsonUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.feishu.common.FeishuConstants;
import cn.july.feishu.config.AppConfig;
import cn.july.feishu.exception.FeishuErrorCode;
import cn.july.feishu.exception.ThirdException;
import cn.july.feishu.model.*;
import cn.july.feishu.util.AssertUtil;
import cn.july.feishu.util.FeishuInvokeUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lark.oapi.Client;
import com.lark.oapi.service.approval.v4.model.*;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserRespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;

/**
 * 审批
 */
@Slf4j
public class ApprovalService {

    private final Client feishuClient;
    private final ContactService contactService;
    private final AuthService authService;

    public ApprovalService(AppConfig appConfig, AuthService authService, ContactService contactService) {
        feishuClient = appConfig.getFeishuClient();
        this.contactService = contactService;
        this.authService = authService;
    }

    /**
     * 获取飞书审核定义
     */
    public GetApprovalRespBody getApproval(String approvalCode) {
        if (StrUtil.isBlank(approvalCode)) {
            throw new ThirdException(FeishuErrorCode.APPROVAL_CODE_NOT_EXIST);
        }
        GetApprovalReq req = GetApprovalReq.newBuilder()
                .approvalCode(approvalCode)
                .withAdminId(false)
                .userIdType("open_id")
                .build();
        return FeishuInvokeUtil.executeRequest(req, feishuClient.approval().v4().approval()::get, FeishuErrorCode.GET_APPROVAL_FAIL);
    }

    /**
     * 获取审批实例详情
     */
    public GetInstanceRespBody instanceInfo(String instanceCode) {
        if (StrUtil.isBlank(instanceCode)) {
            return null;
        }
        GetInstanceReq req = GetInstanceReq.newBuilder()
                .instanceId(instanceCode)
                .build();
        return FeishuInvokeUtil.executeRequest(req, feishuClient.approval().v4().instance()::get, FeishuErrorCode.APPROVAL_INSTANCE_INFO_FAIL);
    }

    /**
     * 订阅审批定义事件,每个新建的审批定义都需要订阅才会收到回调事件
     */
    public void subscribeInstance(String approvalCode) {
        if (StrUtil.isBlank(approvalCode)) {
            return;
        }
        SubscribeApprovalReq req = SubscribeApprovalReq.newBuilder()
                .approvalCode(approvalCode)
                .build();
        log.info("subscribe instance req:{}", JsonUtils.toJson(req));
        try {
            SubscribeApprovalResp resp = feishuClient.approval().v4().approval().subscribe(req);
            // 1390007表示已经订阅了.
            if (!resp.success() && resp.getCode() != 1390007) {
                log.error("subscribe instance fail, code: {}, msg: {}", resp.getCode(), resp.getMsg());
                throw new ThirdException(FeishuErrorCode.APPROVAL_SUBSCRIBE_EVENT_FAIL.buildCode(), resp.getMsg());
            }
        } catch (Exception e) {
            log.error("subscribe instance error", e);
            throw new ThirdException(FeishuErrorCode.APPROVAL_SUBSCRIBE_EVENT_FAIL);
        }
    }

    /**
     * 审核流程上传文件
     */
    public UploadResponse approvalUpload(ApprovalUploadModel model) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + authService.authInternalTenantToken());

        byte[] content = getFileContent(model.getFileUrl());
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("file", model.getFileUrl(),
                        RequestBody.create(okhttp3.MediaType.parse("application/octet-stream"), content))
                .addFormDataPart("name", model.getFileName())
                .addFormDataPart("type", "attachment")
                .build();
        log.info("approval upload file body: {} ", JsonUtils.toJson(body));
        try (HttpResponse httpResponse = OkHttpUtil.getInstance().post(FeishuConstants.getApprovalUploadUrl(), headers, null, MediaType.MULTIPART_FORM_DATA, body)) {
            if (httpResponse.getCode() != 200) {
                throw new ThirdException(FeishuErrorCode.APPROVAL_UPLOAD_FAIL);
            }
            FeishuResponse<UploadResponse> resp = httpResponse.toObj(FeishuResponse.class, UploadResponse.class);
            if (resp.getCode() != 0) {
                log.error("approval upload file fail, code: {}, msg: {}", resp.getCode(), resp.getMsg());
                throw new ThirdException(FeishuErrorCode.APPROVAL_UPLOAD_FAIL.buildCode(), resp.getMsg());
            }
            log.info("approval upload file resp.data: {}", JsonUtils.toJson(resp.getData()));
            return resp.getData();
        } catch (IOException e) {
            log.error("approval upload file  error", e);
            throw new ThirdException(FeishuErrorCode.APPROVAL_UPLOAD_FAIL);
        }
    }

    /**
     * 创建审核实例
     *
     * @param approvalCode 审核定义Code
     * @param openId       飞书用户
     * @param form         表单String
     * @return
     */
    public CreateInstanceRespBody createApprovalInstanceResp(String approvalCode, String openId, String form, List<NodeApprover> nodeApproverOpenIdList) {
        CreateInstanceReq req = CreateInstanceReq.newBuilder()
                .instanceCreate(InstanceCreate.newBuilder()
                        .approvalCode(approvalCode)
                        .openId(openId)
                        .form(form)
                        .nodeApproverOpenIdList(CollUtil.isEmpty(nodeApproverOpenIdList) ? null : nodeApproverOpenIdList.toArray(new NodeApprover[0]))
                        .build())
                .build();
        return FeishuInvokeUtil.executeRequest(req, feishuClient.approval().v4().instance()::create, FeishuErrorCode.CREATE_APPROVAL_INSTANCE_FAIL);
    }

    /**
     * 飞书创建审核实例
     *
     * @param model
     * @return 飞书的审核实例Code
     */
    public String createApprovalInstance(ApprovalInstanceModel model) {
        log.info("feishu approval instance model: {}", JsonUtils.toJson(model));
        AssertUtil.checkApprovalInstanceModel(model);
        // 生成form
        List<ApprovalInstanceFormModel> form = arrangeForm(model);
        // 发起人
        CreateInstanceRespBody body = createApprovalInstanceResp(model.getApprovalCode(), model.getOpenId(), JsonUtils.toJson(form),model.getNodeApproverOpenIdList());
        return body.getInstanceCode();
    }


    private List<ApprovalInstanceFormModel> arrangeForm(ApprovalInstanceModel model) {
        GetApprovalRespBody body = getApproval(model.getApprovalCode());
        String form = body.getForm();
        if (StrUtil.isBlank(form)) {
            throw new ThirdException(FeishuErrorCode.APPROVAL_FORM_NOT_EXIST);
        }
        List<InstanceFormModel> formList = model.getFormList();
        List<ApprovalInstanceFormModel> modelList = JsonUtils.parse(form, new TypeReference<List<ApprovalInstanceFormModel>>() {
        });
        // 数量和格式校验
        checkFormData(formList, modelList);
        List<ApprovalInstanceFormModel> result = new ArrayList<>();
        for (int i = 0; i < modelList.size(); i++) {
            ApprovalInstanceFormModel formModel = modelList.get(i);
            if (ObjUtil.isNotNull(formList.get(i).getType())) {
                formModel.setValue(formList.get(i).getValue());
                result.add(formModel);
            }
        }
        return result;
    }

    private void checkFormData(List<InstanceFormModel> formList, List<ApprovalInstanceFormModel> modelList) {
        if (formList.size() != modelList.size()) {
            log.error("formList: {}", JsonUtils.toJson(formList));
            log.error("modelList: {}", JsonUtils.toJson(modelList));
            throw new ThirdException(FeishuErrorCode.APPROVAL_FORM_CHECK_ERROR);
        }
        for (int i = 0; i < modelList.size(); i++) {
            InstanceFormModel instanceFormModel = formList.get(i);
            InstanceFormModel model = modelList.get(i);
            if (!model.isRequired()) {
                if (ObjUtil.isNull(instanceFormModel.getType())) {
                    continue;
                }
            }
            if (!instanceFormModel.getType().getCode().equals(model.getType().getCode())) {
                log.error("formList: {}", JsonUtils.toJson(formList));
                log.error("modelList: {}", JsonUtils.toJson(modelList));
                throw new ThirdException(FeishuErrorCode.APPROVAL_FORM_CHECK_ERROR);
            }
        }
    }

    private String arrangeOpenId(String telephone) {
        BatchGetIdUserRespBody body = contactService.getOpenId(telephone);
        if (body.getUserList() == null || body.getUserList().length == 0 || Arrays.asList(body.getUserList()).get(0).getUserId() == null) {
            log.error("飞书未通过手机号查到对应用户openId,telephone: {}", telephone);
            throw new ThirdException(FeishuErrorCode.OPEN_ID_NOT_EXIST);
        }
        return Arrays.asList(body.getUserList()).get(0).getUserId();
    }

    private byte[] getFileContent(String fileUrl) {
        try {
            // 创建 URL 对象
            URL url = new URL(fileUrl);
            // 打开连接
            URLConnection connection = url.openConnection();
            // 获取输入流
            try (InputStream inputStream = connection.getInputStream();
                 // 创建 ByteArrayOutputStream 用于存储文件内容
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                // 缓冲区大小
                byte[] buffer = new byte[4096];
                int bytesRead;
                // 读取文件内容
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                // 返回文件内容的字节数组
                return outputStream.toByteArray();
            }
        } catch (Exception e) {
            // 记录错误日志
            log.error("Error fetching file content from URL: {}", fileUrl, e);
            // 抛出自定义异常
            throw new BusinessException("Failed to fetch file content");
        }
    }
}
