package cn.july.feishu.service;


import cn.july.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.ObjUtil;
import cn.july.feishu.common.FeishuCacheConstants;
import cn.july.feishu.config.AppConfig;
import cn.july.feishu.exception.FeishuErrorCode;
import cn.july.feishu.util.FeishuInvokeUtil;
import com.lark.oapi.Client;
import com.lark.oapi.service.tenant.v2.model.QueryTenantRespBody;
import com.lark.oapi.service.tenant.v2.model.Tenant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;

@Slf4j
public class TenantService {

    private final Client feishuClient;
    private final StringRedisTemplate redisTemplate;

    public TenantService(AppConfig appConfig) {
        feishuClient = appConfig.getFeishuClient();
        redisTemplate = appConfig.getRedisTemplate();
    }

    /**
     * 获取企业信息
     */
    public Tenant getTenantInfo(){
        String cacheTenantInfo = FeishuCacheConstants.CACHE_TENANT_INFO;
        if (Boolean.TRUE.equals(redisTemplate.hasKey(cacheTenantInfo))) {
            String tenantInfo = redisTemplate.opsForValue().get(cacheTenantInfo);
            return JsonUtils.parse(tenantInfo, Tenant.class);
        }
        QueryTenantRespBody queryTenantRespBody = queryTenantInfo();
        if(ObjUtil.isNotNull(queryTenantRespBody)){
            redisTemplate.opsForValue().set(cacheTenantInfo, JsonUtils.toJson(queryTenantRespBody.getTenant()), 2, TimeUnit.HOURS);
        }
        return queryTenantRespBody.getTenant();
    }


    //-------------以下是飞书SDK简单包装-------------------

    /**
     * 获取企业信息
     *
     * @return
     */
    protected QueryTenantRespBody queryTenantInfo() {
        return FeishuInvokeUtil.executeRequest(feishuClient.tenant().v2().tenant()::query, FeishuErrorCode.TENANT_INFO_FAIL);
    }
}
