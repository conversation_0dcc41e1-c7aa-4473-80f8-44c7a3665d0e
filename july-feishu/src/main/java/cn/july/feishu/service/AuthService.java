package cn.july.feishu.service;

import cn.july.core.utils.http.OkHttpUtil;
import cn.july.core.utils.http.model.HttpResponse;
import cn.july.core.utils.jackson.JsonUtils;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.july.feishu.common.FeishuCacheConstants;
import cn.july.feishu.common.FeishuConstants;
import cn.july.feishu.config.AppConfig;
import cn.july.feishu.exception.FeishuErrorCode;
import cn.july.feishu.exception.ThirdException;
import cn.july.feishu.model.*;
import cn.july.feishu.util.FeishuInvokeUtil;
import com.lark.oapi.Client;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.service.authen.v1.model.GetUserInfoRespBody;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 认证及授权
 */
@Slf4j
public class AuthService {

    private final String appId;
    private final String appSecret;
    private final Client feishuClient;
    private final StringRedisTemplate redisTemplate;


    public AuthService(AppConfig appConfig) {
        appId = appConfig.getAppId();
        appSecret = appConfig.getAppSecret();
        feishuClient = appConfig.getFeishuClient();
        redisTemplate = appConfig.getRedisTemplate();
    }

    /**
     * 自建应用获取tenant_access_token
     */
    public String authInternalTenantToken() {
        String cacheTenantToken = FeishuCacheConstants.CACHE_TENANT_TOKEN;
        Long time = redisTemplate.getExpire(cacheTenantToken, TimeUnit.SECONDS);
        if (ObjUtil.isNotNull(time) && time > 30) {
            return redisTemplate.opsForValue().get(cacheTenantToken);
        }
        AuthTenantTokenResponse resp = this.getTenantAccessToken();
        String token = resp.getTenantAccessToken();
        redisTemplate.opsForValue().set(cacheTenantToken, token, resp.getExpire(), TimeUnit.SECONDS);
        return token;
    }

    /**
     * 待鉴权url生成签名
     * @param url
     * @return
     */
    public SignatureModel generateSignature(String url){
        String ticket = getJsSdkTicket();
        String noncestr = IdUtil.fastSimpleUUID().substring(16);
        noncestr = noncestr.substring(0, Math.min(16, noncestr.length()));
        long timestamp = System.currentTimeMillis();
        String verifyStr = StrUtil.format("jsapi_ticket={}&noncestr={}&timestamp={}&url={}", ticket, noncestr, timestamp, url);

        String signature = this.sha1(verifyStr);
        return SignatureModel.builder()
                .appId(appId)
                .timestamp(timestamp)
                .nonceStr(noncestr)
                .signature(signature)
                .build();
    }

    /**
     * 获取JSSDKTicket
     * @return
     */
    private String getJsSdkTicket(){
        String key = FeishuCacheConstants.CACHE_JS_SDK_TICKET;
        Long time = redisTemplate.getExpire(key, TimeUnit.SECONDS);
        if (ObjUtil.isNotNull(time) && time > 30) {
            return redisTemplate.opsForValue().get(key);
        }
        JSSDKTicketResponse resp = this.queryJsSdkTicket();
        String jsSdkTicket = resp.getTicket();
        redisTemplate.opsForValue().set(key, jsSdkTicket, resp.getExpireIn(), TimeUnit.SECONDS);
        return jsSdkTicket;
    }

    /**
     * 免登过程,根据授权码code获取user_access_token
     * 授权码code获取过程请自行实现:<a href="https://open.feishu.cn/document/common-capabilities/sso/api/obtain-oauth-code">...</a>
     * 这里只保存授权码供其他接口使用
     */
    public AuthUserAccessTokenModel authUserAccessToken(String code, String redirectUri) {
        AuthUserAccessTokenResponse resp = this.getUserAccessTokenByCode(code,redirectUri);
        this.refreshUserToken(resp);
        return AuthUserAccessTokenModel.builder().userAccessToken(resp.getAccessToken()).ExpiresIn(resp.getExpiresIn()).build();
    }

    /**
     * 刷新user_access_token
     * 使用该接口需用户授权离线访问权限;
     * 请自新避免并发问题
     *
     * @return
     */
    public AuthUserAccessTokenModel getUserAccessToken(String userAccessToken) {
        String cacheUserToken = FeishuCacheConstants.getCacheUserAccessCode(userAccessToken);
        Long time = redisTemplate.getExpire(cacheUserToken, TimeUnit.SECONDS);
        if (ObjUtil.isNull(time) || time == -2L) {
            log.warn("应用需用户重新免登授权!!! userAccessToken:{}", userAccessToken);
            throw new ThirdException(FeishuErrorCode.REFRESH_USER_TOKEN_OVERTIME);
        } else if (time > 30 * 60) {
            return AuthUserAccessTokenModel.builder().userAccessToken(userAccessToken).ExpiresIn(time).build();
        }
        // refresh_token校验
        String refreshToken = redisTemplate.opsForValue().get(cacheUserToken);
        if(StrUtil.isBlank(refreshToken)){
            log.warn("应用需用户重新免登授权!!! userAccessToken:{}", userAccessToken);
            throw new ThirdException(FeishuErrorCode.REFRESH_USER_TOKEN_OVERTIME);
        }
        String cacheRefreshUserToken = FeishuCacheConstants.getCacheRefreshUserAccessCode(refreshToken);
        Long refreshTime = redisTemplate.getExpire(cacheRefreshUserToken, TimeUnit.SECONDS);
        if (ObjUtil.isNull(refreshTime) || refreshTime < 60L * 60) {
            log.warn("应用需用户重新免登授权!!! userAccessToken:{}", userAccessToken);
            throw new ThirdException(FeishuErrorCode.REFRESH_USER_TOKEN_OVERTIME);
        }
        // 刷新user_token
        AuthUserAccessTokenResponse resp = this.refreshUserAccessToken(refreshToken);
        this.refreshUserToken(resp);
        return AuthUserAccessTokenModel.builder().userAccessToken(resp.getAccessToken()).ExpiresIn(resp.getExpiresIn()).build();
    }

    /**
     * 更新refresh_user_access_token
     * @param resp
     */
    private void refreshUserToken(AuthUserAccessTokenResponse resp){
        String userToken = resp.getAccessToken();
        String refreshToken = resp.getRefreshToken();
        redisTemplate.opsForValue().set(FeishuCacheConstants.getCacheUserAccessCode(userToken), refreshToken, resp.getExpiresIn(), TimeUnit.SECONDS);
        redisTemplate.opsForValue().set(FeishuCacheConstants.getCacheRefreshUserAccessCode(refreshToken), LocalDateTime.now().toString(), resp.getRefreshTokenExpiresIn(), TimeUnit.SECONDS);
    }

    //-------------以下是飞书SDK简单包装-------------------


    /**
     * 获取登录用户信息
     * @param userAccessToken
     * @return
     */
    public GetUserInfoRespBody getUserInfo(String userAccessToken){
        RequestOptions options = new RequestOptions();
        options.setUserAccessToken(userAccessToken);
        return FeishuInvokeUtil.executeRequest(options,
                feishuClient.authen().v1().userInfo()::get,
                FeishuErrorCode.LOGIN_USER_INFO_FAIL);
    }
    /**
     * 自建应用获取tenant_access_token
     */
    protected AuthTenantTokenResponse getTenantAccessToken() {
        AuthTenantTokenDTO model = AuthTenantTokenDTO.builder().appId(appId).appSecret(appSecret).build();
        return FeishuInvokeUtil.executeRequest(FeishuConstants.getAuthTenantTokenUrl(),
                new HashMap<>(),
                model,
                AuthTenantTokenResponse.class,
                FeishuErrorCode.TENANT_TOKEN_FAIL,
                AuthTenantTokenResponse::getMsg);
    }

    /**
     * 获取user_access_token
     */
    @SneakyThrows
    protected AuthUserAccessTokenResponse getUserAccessTokenByCode(String code,String redirectUri) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + this.authInternalTenantToken());
        AuthUserAccessTokenDTO model = AuthUserAccessTokenDTO.builder()
                .grantType("authorization_code")
                .clientId(appId)
                .clientSecret(appSecret)
                .code(code)
                .redirectUri(URLEncoder.encode(redirectUri, "UTF-8")).build();
        return FeishuInvokeUtil.executeRequest(FeishuConstants.getAuthUserAccessTokenUrl(),
                headers,
                model,
                AuthUserAccessTokenResponse.class,
                FeishuErrorCode.USER_TOKEN_FAIL,
                AuthUserAccessTokenResponse::getErrorDescription);
    }

    /**
     * 刷新user_access_token
     */
    protected AuthUserAccessTokenResponse refreshUserAccessToken(String refreshToken) {
        AuthUserAccessTokenDTO model = AuthUserAccessTokenDTO.builder()
                .grantType("refresh_token")
                .clientId(appId)
                .clientSecret(appSecret)
                .refreshToken(refreshToken).build();
        return FeishuInvokeUtil.executeRequest(FeishuConstants.getAuthUserAccessTokenUrl(),
                null,
                model,
                AuthUserAccessTokenResponse.class,
                FeishuErrorCode.REFRESH_USER_TOKEN_FAIL,
                AuthUserAccessTokenResponse::getErrorDescription);
    }

    /**
     * 获取JSAPI临时授权凭证
     */
    protected JSSDKTicketResponse queryJsSdkTicket(){
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + this.authInternalTenantToken());

        try (HttpResponse httpResponse = OkHttpUtil.getInstance().postJson(FeishuConstants.getJsSdkTicketGetUrl(), headers, null,"{}")) {
            if (httpResponse.getCode() != 200) {
                log.error("search JsSdkTicket fail, code: {}, msg: {}", httpResponse.getCode(), httpResponse.string());
                throw new ThirdException(FeishuErrorCode.GET_JS_SDK_TICKET_FAIL);
            }
            FeishuResponse<JSSDKTicketResponse> resp = httpResponse.toObj(FeishuResponse.class,JSSDKTicketResponse.class);
            if (resp.getCode() != 0) {
                log.error("search JsSdkTicket fail, code: {}, msg: {}", resp.getCode(), resp.getMsg());
                throw new ThirdException(FeishuErrorCode.GET_JS_SDK_TICKET_FAIL.buildCode(), resp.getMsg());
            }
            log.info("search JsSdkTicket file resp.data: {}", JsonUtils.toJson(resp.getData()));
            return resp.getData();
        } catch (IOException e) {
            log.error("search JsSdkTicket file  error", e);
            throw new ThirdException(FeishuErrorCode.GET_JS_SDK_TICKET_FAIL);
        }
    }

    /**
     * 生成SHA-1哈希值
     * @param input 输入字符串
     * @return SHA-1哈希值
     */
    private static String sha1(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] messageDigest = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA-1算法不可用", e);
            throw new RuntimeException(e);
        }
    }

}
