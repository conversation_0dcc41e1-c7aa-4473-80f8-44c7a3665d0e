package cn.july.feishu.exception;

import cn.july.core.exception.BaseException;
import cn.july.core.exception.CommonCode;
import cn.july.core.exception.MessageCodeWrap;

/**
 * <AUTHOR>
 */
public class ThirdException extends BaseException {

    private static final long serialVersionUID = 7867195690978913580L;

    public ThirdException() {
        super(CommonCode.FAIL);
    }

    public ThirdException(String message) {
        super(CommonCode.FAIL.buildCode(), message);
    }

    public ThirdException(String code, String message) {
        super(code, message);
    }

    public ThirdException(String code, String message, Throwable throwable) {
        super(code, message, throwable);
    }

    public ThirdException(MessageCodeWrap messageCode, Object... args) {
        super(messageCode, args);
    }
}
