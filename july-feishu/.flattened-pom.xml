<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.july.app</groupId>
    <artifactId>july-orch-meeting</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>cn.july.app</groupId>
  <artifactId>july-feishu</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>cn.july.boot</groupId>
      <artifactId>july-spring-boot-starter-web</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
          <groupId>com.alibaba.cloud</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
          <groupId>com.alibaba.cloud</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>cn.july.boot</groupId>
      <artifactId>july-spring-boot-starter-database</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.july.boot</groupId>
      <artifactId>july-spring-boot-starter-swagger</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.july.boot</groupId>
      <artifactId>july-spring-boot-starter-event-spring</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.july.boot</groupId>
      <artifactId>july-spring-boot-starter-cache</artifactId>
    </dependency>
    <dependency>
      <groupId>com.larksuite.oapi</groupId>
      <artifactId>oapi-sdk</artifactId>
      <version>2.4.12</version>
    </dependency>
  </dependencies>
</project>
