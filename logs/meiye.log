2025-08-09 20:59:34.297 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-09 20:59:34.319 [main] INFO  cn.july.orch.meeting.Application - Starting Application using Java 1.8.0_401 on guoxiaoleideMacBook-Pro.local with PID 41676 (/Users/<USER>/IdeaProjects/july/july-orch-meeting/july-orch-meeting-service/target/classes started by gxl in /Users/<USER>/IdeaProjects/july/july-orch-meeting)
2025-08-09 20:59:34.319 [main] INFO  cn.july.orch.meeting.Application - The following 1 profile is active: "local"
2025-08-09 20:59:35.149 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-09 20:59:35.151 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-09 20:59:35.166 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-08-09 20:59:35.300 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[cn.july.orch.meeting]' package. Please check your configuration.
2025-08-09 20:59:35.357 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=6936f659-03c9-343d-af44-69cbde93e8d8
2025-08-09 20:59:35.815 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8102 (http)
2025-08-09 20:59:35.820 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8102"]
2025-08-09 20:59:35.821 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-09 20:59:35.821 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.75]
2025-08-09 20:59:35.916 [main] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring embedded WebApplicationContext
2025-08-09 20:59:35.916 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1564 ms
2025-08-09 20:59:36.455 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-09 20:59:36.530 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-09 20:59:36.794 [main] INFO  org.redisson.Version - Redisson 3.17.6
2025-08-09 20:59:36.809 [main] WARN  i.n.r.d.DnsServerAddressStreamProviders - Can not find io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider in the classpath, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'
2025-08-09 20:59:36.911 [redisson-netty-2-8] INFO  o.r.c.p.MasterPubSubConnectionPool - 10 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-09 20:59:36.911 [redisson-netty-2-9] INFO  o.r.c.pool.MasterConnectionPool - 10 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-09 20:59:37.023 [main] INFO  c.j.c.c.c.CaffeineMultiCacheManager - Create CaffeineCacheManager with names=[july:cache:caffeine_manager], cacheSpec='maximumSize=10000,expireAfterWrite=300s', ttl=300
2025-08-09 20:59:37.297 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-09 20:59:37.365 [main] WARN  o.d.x.f.s.s.FileStorageAutoConfiguration - 没有找到 FileRecorder 的实现类，文件上传之外的部分功能无法正常使用，必须实现该接口才能使用完整功能！
2025-08-09 20:59:37.380 [main] INFO  o.d.x.f.s.c.FileStorageServiceBuilder - 加载 MinIO 存储平台：minio-1
2025-08-09 20:59:37.936 [main] INFO  s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-08-09 20:59:38.438 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8102"]
2025-08-09 20:59:38.444 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8102 (http) with context path '/api/meeting'
2025-08-09 20:59:38.446 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Documentation plugins bootstrapped
2025-08-09 20:59:38.449 [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2025-08-09 20:59:38.460 [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2025-08-09 20:59:38.516 [main] INFO  cn.july.orch.meeting.Application - Started Application in 4.555 seconds (JVM running for 5.194)
2025-08-09 20:59:39.030 [RMI TCP Connection(4)-127.0.0.1] INFO  o.a.c.c.C.[.[.[/api/meeting] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-09 20:59:39.030 [RMI TCP Connection(4)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-09 20:59:39.032 [RMI TCP Connection(4)-127.0.0.1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-09 21:02:21.469 [SpringApplicationShutdownHook] INFO  o.d.x.f.s.core.FileStorageService - 销毁存储平台 minio-1 成功
