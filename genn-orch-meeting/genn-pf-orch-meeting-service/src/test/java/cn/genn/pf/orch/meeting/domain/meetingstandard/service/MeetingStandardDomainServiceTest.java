package cn.genn.pf.orch.meeting.domain.meetingstandard.service;

import cn.genn.core.exception.BusinessException;
import cn.genn.pf.orch.meeting.domain.meetingstandard.model.entity.MeetingStandard;
import cn.genn.pf.orch.meeting.domain.meetingstandard.model.vo.AttendeeStatistics;
import cn.genn.pf.orch.meeting.domain.meetingstandard.repository.IMeetingStandardRepository;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.PriorityLevelEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.TimeUnitEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @description 会议标准领域服务测试
 * @date 2025-01-24
 */
@ExtendWith(MockitoExtension.class)
class MeetingStandardDomainServiceTest {

    @Mock
    private IMeetingStandardRepository meetingStandardRepository;

    @InjectMocks
    private MeetingStandardDomainService meetingStandardDomainService;

    private MeetingStandard validMeetingStandard;

    @BeforeEach
    void setUp() {
        validMeetingStandard = MeetingStandard.builder()
            .standardName("测试会议标准")
            .description("测试描述")
            .defaultDuration(120)
            .advanceNoticeValue(1440)
            .advanceNoticeUnit(TimeUnitEnum.MINUTE)
            .priorityLevel(PriorityLevelEnum.MEDIUM)
            .defaultLocation("会议室A")
            .lateToleranceMinutes(15)
            .requiredRoles(Arrays.asList(MeetingRoleEnum.HOST, MeetingRoleEnum.RECORDER))
            .minAttendees(3)
            .maxAttendees(15)
            .meetingPoints(Arrays.asList("议题1", "议题2"))
            .isEnabled(1)
            .build();
    }

    @Test
    void testCreateMeetingStandard_Success() {
        // 准备
        when(meetingStandardRepository.existsByName(anyString(), isNull())).thenReturn(false);

        // 执行
        assertDoesNotThrow(() -> meetingStandardDomainService.createMeetingStandard(validMeetingStandard));

        // 验证
        verify(meetingStandardRepository).existsByName(validMeetingStandard.getStandardName(), null);
        verify(meetingStandardRepository).save(validMeetingStandard);
    }

    @Test
    void testCreateMeetingStandard_NameExists() {
        // 准备
        when(meetingStandardRepository.existsByName(anyString(), isNull())).thenReturn(true);

        // 执行和验证
        BusinessException exception = assertThrows(BusinessException.class,
            () -> meetingStandardDomainService.createMeetingStandard(validMeetingStandard));
        assertEquals("标准名称已存在", exception.getMessage());

        verify(meetingStandardRepository).existsByName(validMeetingStandard.getStandardName(), null);
        verify(meetingStandardRepository, never()).save(any());
    }

    @Test
    void testCreateMeetingStandard_EmptyName() {
        // 准备
        validMeetingStandard.setStandardName("");

        // 执行和验证
        BusinessException exception = assertThrows(BusinessException.class,
            () -> meetingStandardDomainService.createMeetingStandard(validMeetingStandard));
        assertEquals("标准名称不能为空", exception.getMessage());

        verify(meetingStandardRepository, never()).save(any());
    }

    @Test
    void testCreateMeetingStandard_InvalidDuration() {
        // 准备
        validMeetingStandard.setDefaultDuration(0);

        // 执行和验证
        BusinessException exception = assertThrows(BusinessException.class,
            () -> meetingStandardDomainService.createMeetingStandard(validMeetingStandard));
        assertEquals("默认持续时长必须大于0", exception.getMessage());

        verify(meetingStandardRepository, never()).save(any());
    }

    @Test
    void testCreateMeetingStandard_InvalidAttendees() {
        // 准备
        validMeetingStandard.setMinAttendees(10);
        validMeetingStandard.setMaxAttendees(5);

        // 执行和验证
        BusinessException exception = assertThrows(BusinessException.class,
            () -> meetingStandardDomainService.createMeetingStandard(validMeetingStandard));
        assertEquals("最少参会人数不能大于最多参会人数", exception.getMessage());

        verify(meetingStandardRepository, never()).save(any());
    }

    @Test
    void testUpdateMeetingStandard_Success() {
        // 准备
        validMeetingStandard.setId(1L);
        when(meetingStandardRepository.findById(1L)).thenReturn(validMeetingStandard);
        when(meetingStandardRepository.existsByName(anyString(), eq(1L))).thenReturn(false);

        // 执行
        assertDoesNotThrow(() -> meetingStandardDomainService.updateMeetingStandard(validMeetingStandard));

        // 验证
        verify(meetingStandardRepository).findById(1L);
        verify(meetingStandardRepository).existsByName(validMeetingStandard.getStandardName(), 1L);
        verify(meetingStandardRepository).update(validMeetingStandard);
    }

    @Test
    void testUpdateMeetingStandard_NotExists() {
        // 准备
        validMeetingStandard.setId(1L);
        when(meetingStandardRepository.findById(1L)).thenReturn(null);

        // 执行和验证
        BusinessException exception = assertThrows(BusinessException.class,
            () -> meetingStandardDomainService.updateMeetingStandard(validMeetingStandard));
        assertEquals("会议标准不存在", exception.getMessage());

        verify(meetingStandardRepository).findById(1L);
        verify(meetingStandardRepository, never()).update(any());
    }

    @Test
    void testUpdateMeetingStandard_AttendeeLimitsConflict() {
        // 准备
        validMeetingStandard.setId(1L);
        MeetingStandard existing = MeetingStandard.builder()
            .id(1L)
            .standardName("测试会议标准")
            .minAttendees(2)
            .maxAttendees(10)
            .build();
        
        validMeetingStandard.setMinAttendees(5); // 增加最少参会人数
        
        AttendeeStatistics statistics = AttendeeStatistics.builder()
            .totalPlans(3)
            .minAttendees(2) // 现有会议最小2人
            .maxAttendees(8)
            .avgAttendees(5.0)
            .distribution(AttendeeStatistics.AttendeeDistribution.builder()
                .smallMeetings(2)
                .mediumMeetings(1)
                .largeMeetings(0)
                .extraLargeMeetings(0)
                .build())
            .build();
        
        when(meetingStandardRepository.findById(1L)).thenReturn(existing);
        when(meetingStandardRepository.existsByName(anyString(), eq(1L))).thenReturn(false);
        when(meetingStandardRepository.getAttendeeStatisticsByStandardId(1L)).thenReturn(statistics);

        // 执行和验证
        BusinessException exception = assertThrows(BusinessException.class,
            () -> meetingStandardDomainService.updateMeetingStandard(validMeetingStandard));
        assertTrue(exception.getMessage().contains("无法设置最少参会人数为5人，现有会议规划中有2人的会议"));

        verify(meetingStandardRepository).findById(1L);
        verify(meetingStandardRepository).existsByName(validMeetingStandard.getStandardName(), 1L);
        verify(meetingStandardRepository).getAttendeeStatisticsByStandardId(1L);
        verify(meetingStandardRepository, never()).update(any());
    }

    @Test
    void testUpdateMeetingStandard_NoAttendeeConflict() {
        // 准备
        validMeetingStandard.setId(1L);
        MeetingStandard existing = MeetingStandard.builder()
            .id(1L)
            .standardName("测试会议标准")
            .minAttendees(2)
            .maxAttendees(10)
            .build();
        
        validMeetingStandard.setMinAttendees(1); // 减少最少参会人数，不会冲突
        validMeetingStandard.setMaxAttendees(20); // 增加最多参会人数，不会冲突
        
        AttendeeStatistics statistics = AttendeeStatistics.builder()
            .totalPlans(3)
            .minAttendees(2)
            .maxAttendees(8)
            .avgAttendees(5.0)
            .distribution(AttendeeStatistics.AttendeeDistribution.builder()
                .smallMeetings(2)
                .mediumMeetings(1)
                .largeMeetings(0)
                .extraLargeMeetings(0)
                .build())
            .build();
        
        when(meetingStandardRepository.findById(1L)).thenReturn(existing);
        when(meetingStandardRepository.existsByName(anyString(), eq(1L))).thenReturn(false);
        when(meetingStandardRepository.getAttendeeStatisticsByStandardId(1L)).thenReturn(statistics);

        // 执行
        assertDoesNotThrow(() -> meetingStandardDomainService.updateMeetingStandard(validMeetingStandard));

        // 验证
        verify(meetingStandardRepository).findById(1L);
        verify(meetingStandardRepository).existsByName(validMeetingStandard.getStandardName(), 1L);
        verify(meetingStandardRepository).getAttendeeStatisticsByStandardId(1L);
        verify(meetingStandardRepository).update(validMeetingStandard);
    }

    @Test
    void testDeleteMeetingStandard_Success() {
        // 准备
        when(meetingStandardRepository.findById(1L)).thenReturn(validMeetingStandard);
        when(meetingStandardRepository.isUsedByMeetingPlan(1L)).thenReturn(false);

        // 执行
        assertDoesNotThrow(() -> meetingStandardDomainService.deleteMeetingStandard(1L));

        // 验证
        verify(meetingStandardRepository).findById(1L);
        verify(meetingStandardRepository).isUsedByMeetingPlan(1L);
        verify(meetingStandardRepository).deleteById(1L);
    }

    @Test
    void testDeleteMeetingStandard_NotExists() {
        // 准备
        when(meetingStandardRepository.findById(1L)).thenReturn(null);

        // 执行和验证
        BusinessException exception = assertThrows(BusinessException.class,
            () -> meetingStandardDomainService.deleteMeetingStandard(1L));
        assertEquals("会议标准不存在", exception.getMessage());

        verify(meetingStandardRepository).findById(1L);
        verify(meetingStandardRepository, never()).deleteById(any());
    }

    @Test
    void testDeleteMeetingStandard_IsUsed() {
        // 准备
        when(meetingStandardRepository.findById(1L)).thenReturn(validMeetingStandard);
        when(meetingStandardRepository.isUsedByMeetingPlan(1L)).thenReturn(true);

        // 执行和验证
        BusinessException exception = assertThrows(BusinessException.class,
            () -> meetingStandardDomainService.deleteMeetingStandard(1L));
        assertEquals("该会议标准已被会议规划使用，无法删除", exception.getMessage());

        verify(meetingStandardRepository).findById(1L);
        verify(meetingStandardRepository).isUsedByMeetingPlan(1L);
        verify(meetingStandardRepository, never()).deleteById(any());
    }

    @Test
    void testGetAttendeeStatistics_Success() {
        // 准备
        validMeetingStandard.setId(1L);
        AttendeeStatistics expectedStatistics = AttendeeStatistics.builder()
            .totalPlans(5)
            .minAttendees(3)
            .maxAttendees(20)
            .avgAttendees(10.5)
            .distribution(AttendeeStatistics.AttendeeDistribution.builder()
                .smallMeetings(2)
                .mediumMeetings(2)
                .largeMeetings(1)
                .extraLargeMeetings(0)
                .build())
            .build();
        
        when(meetingStandardRepository.findById(1L)).thenReturn(validMeetingStandard);
        when(meetingStandardRepository.getAttendeeStatisticsByStandardId(1L)).thenReturn(expectedStatistics);

        // 执行
        AttendeeStatistics result = meetingStandardDomainService.getAttendeeStatistics(1L);

        // 验证
        assertNotNull(result);
        assertEquals(5, result.getTotalPlans());
        assertEquals(3, result.getMinAttendees());
        assertEquals(20, result.getMaxAttendees());
        assertEquals(10.5, result.getAvgAttendees());
        assertEquals(2, result.getDistribution().getSmallMeetings());
        assertEquals(2, result.getDistribution().getMediumMeetings());
        assertEquals(1, result.getDistribution().getLargeMeetings());
        assertEquals(0, result.getDistribution().getExtraLargeMeetings());
    }

    @Test
    void testGetAttendeeStatistics_StandardNotExists() {
        // 准备
        when(meetingStandardRepository.findById(1L)).thenReturn(null);

        // 执行和验证
        BusinessException exception = assertThrows(BusinessException.class,
            () -> meetingStandardDomainService.getAttendeeStatistics(1L));
        assertEquals("会议标准不存在", exception.getMessage());

        verify(meetingStandardRepository).findById(1L);
        verify(meetingStandardRepository, never()).getAttendeeStatisticsByStandardId(any());
    }

    @Test
    void testGetAttendeeSuggestion_Success() {
        // 准备
        validMeetingStandard.setId(1L);
        validMeetingStandard.setMinAttendees(3);
        validMeetingStandard.setMaxAttendees(15);
        
        AttendeeStatistics statistics = AttendeeStatistics.builder()
            .totalPlans(5)
            .minAttendees(2)
            .maxAttendees(20)
            .avgAttendees(10.5)
            .distribution(AttendeeStatistics.AttendeeDistribution.builder()
                .smallMeetings(2)
                .mediumMeetings(2)
                .largeMeetings(1)
                .extraLargeMeetings(0)
                .build())
            .build();
        
        when(meetingStandardRepository.findById(1L)).thenReturn(validMeetingStandard);
        when(meetingStandardRepository.getAttendeeStatisticsByStandardId(1L)).thenReturn(statistics);

        // 执行
        MeetingStandardDomainService.AttendeeSuggestion result = meetingStandardDomainService.getAttendeeSuggestion(1L);

        // 验证
        assertNotNull(result);
        assertEquals(1L, result.getStandardId());
        assertEquals("测试会议标准", result.getStandardName());
        assertEquals(3, result.getCurrentMinAttendees());
        assertEquals(15, result.getCurrentMaxAttendees());
        assertEquals(2, result.getSuggestedMinAttendees());
        assertEquals(20, result.getSuggestedMaxAttendees());
        assertEquals(statistics, result.getStatistics());
    }
} 