//package cn.genn.pf.orch.meeting.infrastructure.service;
//
//import cn.genn.pf.orch.meeting.interfaces.dto.KbRepoFileDTO;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.test.context.ActiveProfiles;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR> Assistant
// * @description 外部文件上传服务测试
// */
//@SpringBootTest
//@ActiveProfiles("test")
//public class ExternalFileUploadServiceTest {
//
//    @Resource
//    private ExternalFileUploadService externalFileUploadService;
//    @Resource
//    private ObjectMapper objectMapper;
//
//    @Test
//    public void testParseUploadResponse() throws Exception {
//        // 测试解析不同格式的响应
//
//        // 格式1：直接返回KbRepoFileDTO
//        String response1 = """
//            {
//              "repoId": 0,
//              "collectionId": 0,
//              "filePlatform": "test-platform",
//              "externalFileId": "/test/file.pdf",
//              "externalFileUrl": "https://example.com/file.pdf",
//              "length": 12345,
//              "fileName": "test.pdf",
//              "contentType": "application/pdf",
//              "metadata": null
//            }
//            """;
//
//        // 格式2：包装在data字段中
//        String response2 = """
//            {
//              "code": 200,
//              "message": "success",
//              "data": {
//                "repoId": 0,
//                "collectionId": 0,
//                "filePlatform": "test-platform",
//                "externalFileId": "/test/file.pdf",
//                "externalFileUrl": "https://example.com/file.pdf",
//                "length": 12345,
//                "fileName": "test.pdf",
//                "contentType": "application/pdf",
//                "metadata": null
//              }
//            }
//            """;
//
//        // 格式3：包装在result字段中
//        String response3 = """
//            {
//              "success": true,
//              "result": {
//                "repoId": 0,
//                "collectionId": 0,
//                "filePlatform": "test-platform",
//                "externalFileId": "/test/file.pdf",
//                "externalFileUrl": "https://example.com/file.pdf",
//                "length": 12345,
//                "fileName": "test.pdf",
//                "contentType": "application/pdf",
//                "metadata": null
//              }
//            }
//            """;
//
//        // 使用反射调用私有方法进行测试
//        java.lang.reflect.Method parseMethod = ExternalFileUploadService.class
//                .getDeclaredMethod("parseUploadResponse", String.class);
//        parseMethod.setAccessible(true);
//
//        // 测试格式1
//        KbRepoFileDTO result1 = (KbRepoFileDTO) parseMethod.invoke(externalFileUploadService, response1);
//        System.out.println("格式1解析结果：" + result1);
//        assert result1 != null;
//        assert "test.pdf".equals(result1.getFileName());
//        assert "application/pdf".equals(result1.getContentType());
//
//        // 测试格式2
//        KbRepoFileDTO result2 = (KbRepoFileDTO) parseMethod.invoke(externalFileUploadService, response2);
//        System.out.println("格式2解析结果：" + result2);
//        assert result2 != null;
//        assert "test.pdf".equals(result2.getFileName());
//
//        // 测试格式3
//        KbRepoFileDTO result3 = (KbRepoFileDTO) parseMethod.invoke(externalFileUploadService, response3);
//        System.out.println("格式3解析结果：" + result3);
//        assert result3 != null;
//        assert "test.pdf".equals(result3.getFileName());
//    }
//
//    @Test
//    public void testUploadFile() {
//        // 创建模拟文件
//        MockMultipartFile mockFile = new MockMultipartFile(
//                "file",
//                "test-document.txt",
//                "text/plain",
//                "这是测试文件内容".getBytes()
//        );
//
//        try {
//            // 尝试上传文件（需要外部服务可用）
//            KbRepoFileDTO result = externalFileUploadService.uploadFile(mockFile);
//
//            if (result != null) {
//                System.out.println("文件上传成功：" + result);
//                assert result.getFileName() != null;
//                assert result.getExternalFileUrl() != null;
//                assert result.getContentType() != null;
//            } else {
//                System.out.println("文件上传失败，可能是外部服务不可用");
//            }
//        } catch (Exception e) {
//            System.out.println("文件上传测试需要外部服务可用：" + e.getMessage());
//        }
//    }
//}
