package cn.genn.pf.orch.meeting.application.service;

import cn.genn.pf.orch.meeting.application.service.action.TaskSupervisionService;
import cn.genn.pf.orch.meeting.application.service.query.TaskQueryService;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskStatisticsDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskSupervisionDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务统计和督办测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class TaskStatisticsTest {

    @Resource
    private TaskQueryService taskQueryService;
    @Resource
    private TaskSupervisionService taskSupervisionService;

    @Test
    public void testGetTaskStatistics() {
        System.out.println("=== 测试任务统计功能 ===");

        try {
            TaskStatisticsDTO statistics = taskQueryService.getTaskStatistics();

            System.out.println("任务统计结果：");
            System.out.println("总任务数：" + statistics.getTotalTasks());
            System.out.println("未开始任务数：" + statistics.getNotStartedTasks());
            System.out.println("进行中任务数：" + statistics.getInProgressTasks());
            System.out.println("已完成任务数：" + statistics.getCompletedTasks());
            System.out.println("已超期任务数：" + statistics.getOverdueTasks());
            System.out.println("今日到期任务数：" + statistics.getTodayDueTasks());
            System.out.println("本周到期任务数：" + statistics.getWeekDueTasks());
            System.out.println("高优先级任务数：" + statistics.getHighPriorityTasks());
            System.out.println("任务完成率：" + statistics.getCompletionRate() + "%");
            System.out.println("任务超期率：" + statistics.getOverdueRate() + "%");

            System.out.println("\n状态统计详情：");
            if (statistics.getStatusStatistics() != null) {
                for (TaskStatisticsDTO.TaskStatusStatistics status : statistics.getStatusStatistics()) {
                    System.out.println("  " + status.getStatusName() + ": " + status.getCount() +
                                     " (" + status.getPercentage() + "%)");
                }
            }

            System.out.println("\n优先级统计详情：");
            if (statistics.getPriorityStatistics() != null) {
                for (TaskStatisticsDTO.TaskPriorityStatistics priority : statistics.getPriorityStatistics()) {
                    System.out.println("  " + priority.getPriorityName() + ": " + priority.getCount() +
                                     " (" + priority.getPercentage() + "%)");
                }
            }

            System.out.println("✅ 任务统计测试成功");

        } catch (Exception e) {
            System.err.println("❌ 任务统计测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testGetTasksForSupervision() {
        System.out.println("=== 测试督办任务查询功能 ===");

        try {
            List<TaskSupervisionDTO> supervisionTasks = taskQueryService.getTasksForSupervision();

            System.out.println("需要督办的任务数量：" + supervisionTasks.size());

            for (TaskSupervisionDTO task : supervisionTasks) {
                System.out.println("\n督办任务信息：");
                System.out.println("  任务ID：" + task.getTaskId());
                System.out.println("  任务标题：" + task.getTitle());
                System.out.println("  负责人：" + task.getOwnerName());
                System.out.println("  优先级：" + task.getPriorityName());
                System.out.println("  状态：" + task.getStatusName());
                System.out.println("  截止时间：" + task.getDueDate());
                System.out.println("  剩余时间：" + task.getRemainingHours() + " 小时");
                System.out.println("  是否紧急：" + (task.getIsUrgent() ? "是" : "否"));
            }

            System.out.println("✅ 督办任务查询测试成功");

        } catch (Exception e) {
            System.err.println("❌ 督办任务查询测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testAutoSuperviseUrgentTasks() {
        System.out.println("=== 测试自动督办功能 ===");

        try {
            taskSupervisionService.autoSuperviseUrgentTasks();
            System.out.println("✅ 自动督办测试成功");

        } catch (Exception e) {
            System.err.println("❌ 自动督办测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testManualSuperviseTask() {
        System.out.println("=== 测试手动督办功能 ===");

        try {
            // 这里需要一个真实存在的任务ID进行测试
            Long testTaskId = 1L;

            taskSupervisionService.manualSuperviseTask(testTaskId);
            System.out.println("✅ 手动督办测试成功，任务ID：" + testTaskId);

        } catch (Exception e) {
            System.err.println("❌ 手动督办测试失败：" + e.getMessage());
            // 如果是因为任务不存在，这是正常的
            if (e.getMessage().contains("任务不存在")) {
                System.out.println("ℹ️ 测试任务不存在，这是正常的测试结果");
            } else {
                e.printStackTrace();
            }
        }
    }

    @Test
    public void testSupervisionDemo() {
        System.out.println("=== 测试督办演示功能 ===");

        try {
            // 使用测试用的 Open ID（请替换为真实的 Open ID）
            String testOpenId = "ou_test_user_id";

            System.out.println("发送测试督办消息到: " + testOpenId);

            // 测试多次发送，体验不同的模拟场景
            for (int i = 1; i <= 3; i++) {
                System.out.println("\n--- 第 " + i + " 次测试 ---");
                taskSupervisionService.testSupervision(testOpenId);
                System.out.println("✅ 第 " + i + " 次测试督办消息发送成功");

                // 间隔一秒，避免发送过快
                Thread.sleep(1000);
            }

            System.out.println("\n✅ 督办演示测试完成，共发送了3条不同场景的督办消息");

        } catch (Exception e) {
            System.err.println("❌ 督办演示测试失败：" + e.getMessage());
            if (e.getMessage().contains("飞书")) {
                System.out.println("ℹ️ 可能是飞书配置问题或网络问题，请检查配置");
            } else {
                e.printStackTrace();
            }
        }
    }

    @Test
    public void testSupervisionScenarios() {
        System.out.println("=== 测试不同督办场景 ===");

        String testOpenId = "ou_test_user_id";

        try {
            // 测试多次以获得不同的随机场景
            System.out.println("连续发送10次测试消息，观察不同场景的分布：");

            for (int i = 1; i <= 10; i++) {
                try {
                    taskSupervisionService.testSupervision(testOpenId);
                    System.out.println("第 " + i + " 次发送成功");
                    Thread.sleep(500); // 间隔0.5秒
                } catch (Exception e) {
                    System.out.println("第 " + i + " 次发送失败: " + e.getMessage());
                }
            }

            System.out.println("✅ 场景测试完成");

        } catch (Exception e) {
            System.err.println("❌ 场景测试失败：" + e.getMessage());
        }
    }
}
