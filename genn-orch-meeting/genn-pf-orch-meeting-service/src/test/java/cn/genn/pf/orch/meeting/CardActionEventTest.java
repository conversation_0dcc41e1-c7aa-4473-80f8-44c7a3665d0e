package cn.genn.pf.orch.meeting;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.interfaces.command.CardActionEventCommand;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR> Assistant
 * @description 卡片事件数据解析测试
 */
public class CardActionEventTest {

    @Test
    public void testParseCardActionEvent() {
        // 实际的回调数据
        String eventJson = "{\n" +
                "  \"operator\": {\n" +
                "    \"tenant_key\": \"16b9dc66b398175f\",\n" +
                "    \"open_id\": \"ou_084933bab58e92219318aaf50a4abf35\",\n" +
                "    \"union_id\": \"on_07caade28bf502bd0a18393f967ed5e6\"\n" +
                "  },\n" +
                "  \"token\": \"c-2dc2ff107805d26058c25b680a6fd40c07ad42ba\",\n" +
                "  \"action\": {\n" +
                "    \"value\": {\n" +
                "      \"action\": \"test_action_1\",\n" +
                "      \"data\": \"test_data_1\"\n" +
                "    },\n" +
                "    \"tag\": \"button\"\n" +
                "  },\n" +
                "  \"host\": \"im_message\",\n" +
                "  \"context\": {\n" +
                "    \"open_message_id\": \"om_x100b477f7fa7ace80eced7a45aa15a2\",\n" +
                "    \"open_chat_id\": \"oc_2e52ce913192477795f0950c2944ae31\"\n" +
                "  }\n" +
                "}";

        try {
            CardActionEventCommand cardEvent = JsonUtils.parse(eventJson, CardActionEventCommand.class);

            System.out.println("✅ 解析成功！");
            System.out.println("用户ID: " + cardEvent.getOperator().getOpenId());
            System.out.println("消息ID: " + cardEvent.getContext().getOpenMessageId());
            System.out.println("操作类型: " + cardEvent.getAction().getValue().getAction());
            System.out.println("操作数据: " + cardEvent.getAction().getValue().getData());
            System.out.println("租户Key: " + cardEvent.getOperator().getTenantKey());
            System.out.println("UnionID: " + cardEvent.getOperator().getUnionId());
            System.out.println("Token: " + cardEvent.getToken());
            System.out.println("Host: " + cardEvent.getHost());
            System.out.println("ChatID: " + cardEvent.getContext().getOpenChatId());

            // 验证关键字段
            assert "ou_084933bab58e92219318aaf50a4abf35".equals(cardEvent.getOperator().getOpenId());
            assert "test_action_1".equals(cardEvent.getAction().getValue().getAction());
            assert "test_data_1".equals(cardEvent.getAction().getValue().getData());
            assert "om_x100b477f7fa7ace80eced7a45aa15a2".equals(cardEvent.getContext().getOpenMessageId());

            System.out.println("✅ 所有字段验证通过！");

        } catch (Exception e) {
            System.err.println("❌ 解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
