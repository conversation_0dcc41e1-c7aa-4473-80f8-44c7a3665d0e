package cn.genn.pf.orch.meeting.application.service.query;

import cn.genn.pf.orch.meeting.interfaces.dto.schedule.BusinessMeetingLevelDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
class BusinessMeetingQueryServiceTest {

    @Resource
    private BusinessMeetingQueryService businessMeetingQueryService;

    @Test
    void level() {
        BusinessMeetingLevelDTO level = businessMeetingQueryService.level();

        System.out.println("level = " + level);
    }
}
