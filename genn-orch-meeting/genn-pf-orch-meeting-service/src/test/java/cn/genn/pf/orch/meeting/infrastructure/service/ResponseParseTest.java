//package cn.genn.pf.orch.meeting.infrastructure.service;
//
//import cn.genn.pf.orch.meeting.interfaces.dto.KbRepoFileDTO;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import javax.annotation.Resource;
//import java.lang.reflect.Method;
//
///**
// * <AUTHOR> Assistant
// * @description 响应解析测试
// */
//@SpringBootTest
//@ActiveProfiles("test")
//public class ResponseParseTest {
//
//    @Resource
//    private ExternalFileUploadService externalFileUploadService;
//    @Resource
//    private ObjectMapper objectMapper;
//
//    @Test
//    public void testParseActualResponse() throws Exception {
//        // 您提供的实际响应
//        String actualResponse = """
//            {
//              "success": true,
//              "traceid": "5a1fad6840d14fc48deedf3e71504638.90.17534367523131575",
//              "code": "000000200",
//              "msg": "success",
//              "err": null,
//              "data": {
//                "repoId": 0,
//                "collectionId": 0,
//                "filePlatform": "pro-genn-ai-algorithm",
//                "externalFileId": "/userUpload/e59747f5-c19d-4465-acfb-c772f91904b7/煤业生产：分析汇报（7月22日）.docx",
//                "externalFileUrl": "https://pro-genn-ai-algorithm.tos-cn-beijing.volces.com/%2FuserUpload%2Fe59747f5-c19d-4465-acfb-c772f91904b7%2F%E7%2585%25A4%25E4%25B8%259A%25E7%2594%259F%25E4%25BA%25A7%25EF%25BC%259A%25E5%2588%2586%25E6%259E%2590%25E6%25B1%2587%25E6%258A%25A5%25EF%25BC%25887%25E6%259C%258822%25E6%2597%25A5%25EF%25BC%2589.docx?X-Tos-Algorithm=TOS4-HMAC-SHA256&response-content-disposition=attachment%3Bfilename%3D%25E7%2585%25A4%25E4%25B8%259A%25E7%2594%259F%25E4%25BA%25A7%25EF%25BC%259A%25E5%2588%2586%25E6%259E%2590%25E6%25B1%2587%25E6%258A%25A5%25EF%25BC%25887%25E6%259C%258822%25E6%2597%25A5%25EF%25BC%2589.docx&X-Tos-Credential=AKLTYmIyZGNhMGNhM2NkNGU5MTlmNmQxYjA4N2E3MjM4MTc%2F20250725%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Expires=15552000&X-Tos-Date=20250725T094552Z&X-Tos-Signature=38dcd00a9cf4abe68ed88e3643eff919cf5f5aa7f17a3651f078cfcb15e8ced4&X-Tos-SignedHeaders=host",
//                "length": 54752,
//                "fileName": "煤业生产：分析汇报（7月22日）.docx",
//                "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
//                "metadata": null
//              },
//              "timestamp": 1753436752410
//            }
//            """;
//
//        // 使用反射调用私有方法
//        Method parseMethod = ExternalFileUploadService.class
//                .getDeclaredMethod("parseUploadResponse", String.class);
//        parseMethod.setAccessible(true);
//
//        KbRepoFileDTO result = (KbRepoFileDTO) parseMethod.invoke(externalFileUploadService, actualResponse);
//
//        System.out.println("=== 解析结果 ===");
//        System.out.println("result: " + result);
//
//        if (result != null) {
//            System.out.println("repoId: " + result.getRepoId());
//            System.out.println("collectionId: " + result.getCollectionId());
//            System.out.println("filePlatform: " + result.getFilePlatform());
//            System.out.println("externalFileId: " + result.getExternalFileId());
//            System.out.println("externalFileUrl: " + result.getExternalFileUrl());
//            System.out.println("length: " + result.getLength());
//            System.out.println("fileName: " + result.getFileName());
//            System.out.println("contentType: " + result.getContentType());
//            System.out.println("metadata: " + result.getMetadata());
//
//            // 验证关键字段
//            assert result.getExternalFileUrl() != null : "externalFileUrl不能为null";
//            assert result.getFileName() != null : "fileName不能为null";
//            assert result.getContentType() != null : "contentType不能为null";
//            assert result.getLength() != null : "length不能为null";
//            assert result.getFilePlatform() != null : "filePlatform不能为null";
//            assert result.getExternalFileId() != null : "externalFileId不能为null";
//
//            System.out.println("✅ 所有关键字段验证通过！");
//        } else {
//            System.out.println("❌ 解析结果为null");
//            assert false : "解析结果不应该为null";
//        }
//    }
//
//    @Test
//    public void testDirectDataParse() throws Exception {
//        // 直接解析data部分
//        String dataJson = """
//            {
//              "repoId": 0,
//              "collectionId": 0,
//              "filePlatform": "pro-genn-ai-algorithm",
//              "externalFileId": "/userUpload/e59747f5-c19d-4465-acfb-c772f91904b7/煤业生产：分析汇报（7月22日）.docx",
//              "externalFileUrl": "https://pro-genn-ai-algorithm.tos-cn-beijing.volces.com/test.docx",
//              "length": 54752,
//              "fileName": "煤业生产：分析汇报（7月22日）.docx",
//              "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
//              "metadata": null
//            }
//            """;
//
//        KbRepoFileDTO result = objectMapper.readValue(dataJson, KbRepoFileDTO.class);
//
//        System.out.println("=== 直接解析data部分结果 ===");
//        System.out.println("result: " + result);
//
//        assert result != null;
//        assert result.getFileName() != null;
//        assert result.getExternalFileUrl() != null;
//        assert result.getContentType() != null;
//
//        System.out.println("✅ 直接解析data部分成功！");
//    }
//
//    @Test
//    public void testJsonFieldMapping() throws Exception {
//        // 测试字段映射是否正确
//        String testJson = """
//            {
//              "repoId": 123,
//              "collectionId": 456,
//              "filePlatform": "test-platform",
//              "externalFileId": "/test/file.txt",
//              "externalFileUrl": "https://test.com/file.txt",
//              "length": 1024,
//              "fileName": "test.txt",
//              "contentType": "text/plain",
//              "metadata": "test metadata"
//            }
//            """;
//
//        KbRepoFileDTO result = objectMapper.readValue(testJson, KbRepoFileDTO.class);
//
//        System.out.println("=== 字段映射测试结果 ===");
//        System.out.println("repoId: " + result.getRepoId() + " (期望: 123)");
//        System.out.println("collectionId: " + result.getCollectionId() + " (期望: 456)");
//        System.out.println("filePlatform: " + result.getFilePlatform() + " (期望: test-platform)");
//        System.out.println("externalFileId: " + result.getExternalFileId() + " (期望: /test/file.txt)");
//        System.out.println("externalFileUrl: " + result.getExternalFileUrl() + " (期望: https://test.com/file.txt)");
//        System.out.println("length: " + result.getLength() + " (期望: 1024)");
//        System.out.println("fileName: " + result.getFileName() + " (期望: test.txt)");
//        System.out.println("contentType: " + result.getContentType() + " (期望: text/plain)");
//        System.out.println("metadata: " + result.getMetadata() + " (期望: test metadata)");
//
//        // 验证所有字段
//        assert Long.valueOf(123).equals(result.getRepoId());
//        assert Long.valueOf(456).equals(result.getCollectionId());
//        assert "test-platform".equals(result.getFilePlatform());
//        assert "/test/file.txt".equals(result.getExternalFileId());
//        assert "https://test.com/file.txt".equals(result.getExternalFileUrl());
//        assert Integer.valueOf(1024).equals(result.getLength());
//        assert "test.txt".equals(result.getFileName());
//        assert "text/plain".equals(result.getContentType());
//        assert "test metadata".equals(result.getMetadata());
//
//        System.out.println("✅ 所有字段映射正确！");
//    }
//}
