package cn.genn.pf.orch.meeting.domain.meetingplan.service;

import cn.genn.core.exception.BusinessException;
import cn.genn.pf.orch.meeting.domain.meetingplan.model.entity.MeetingPlan;
import cn.genn.pf.orch.meeting.domain.meetingplan.repository.IMeetingPlanRepository;
import cn.genn.pf.orch.meeting.domain.meetingstandard.model.entity.MeetingStandard;
import cn.genn.pf.orch.meeting.domain.meetingstandard.repository.IMeetingStandardRepository;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingPlanStatusEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.PriorityLevelEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @description 会议规划冲突检测测试
 * @date 2025-01-24
 */
@ExtendWith(MockitoExtension.class)
class MeetingPlanConflictTest {

    @Mock
    private IMeetingPlanRepository meetingPlanRepository;

    @Mock
    private IMeetingStandardRepository meetingStandardRepository;

    @InjectMocks
    private MeetingPlanDomainService meetingPlanDomainService;

    private MeetingStandard mockStandard;
    private MeetingPlan newMeetingPlan;

    @BeforeEach
    void setUp() {
        mockStandard = MeetingStandard.builder()
            .id(1L)
            .standardName("部门例会")
            .defaultDuration(120)
            .priorityLevel(PriorityLevelEnum.MEDIUM)
            .defaultLocation("会议室A")
            .build();

        newMeetingPlan = MeetingPlan.builder()
            .planName("新会议")
            .plannedStartTime(LocalDateTime.of(2024, 4, 15, 10, 0))
            .plannedEndTime(LocalDateTime.of(2024, 4, 15, 12, 0))
            .meetingStandardId(1L)
            .meetingLocation("会议室A")
            .attendees(Arrays.asList("张三", "李四"))
            .build();
    }

    @Test
    void testNoConflict() {
        // 准备数据
        when(meetingStandardRepository.findById(1L)).thenReturn(mockStandard);
        when(meetingPlanRepository.findConflictPlans(any(), any(), any(), any(), any()))
            .thenReturn(Collections.emptyList());

        // 执行测试
        assertDoesNotThrow(() -> meetingPlanDomainService.createMeetingPlan(newMeetingPlan));

        // 验证
        verify(meetingPlanRepository).save(newMeetingPlan);
    }

    @Test
    void testLocationConflict() {
        // 准备冲突数据
        MeetingPlan conflictPlan = MeetingPlan.builder()
            .id(2L)
            .planName("已有会议")
            .plannedStartTime(LocalDateTime.of(2024, 4, 15, 9, 0))
            .plannedEndTime(LocalDateTime.of(2024, 4, 15, 11, 0))
            .meetingLocation("会议室A")
            .attendees(Arrays.asList("王五", "赵六"))
            .build();

        when(meetingStandardRepository.findById(1L)).thenReturn(mockStandard);
        when(meetingPlanRepository.findConflictPlans(any(), any(), any(), any(), any()))
            .thenReturn(Arrays.asList(conflictPlan));

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> meetingPlanDomainService.createMeetingPlan(newMeetingPlan));

        assertTrue(exception.getMessage().contains("会议室冲突"));
        assertTrue(exception.getMessage().contains("会议室A"));
    }

    @Test
    void testAttendeeConflict() {
        // 准备冲突数据
        MeetingPlan conflictPlan = MeetingPlan.builder()
            .id(2L)
            .planName("已有会议")
            .plannedStartTime(LocalDateTime.of(2024, 4, 15, 9, 0))
            .plannedEndTime(LocalDateTime.of(2024, 4, 15, 11, 0))
            .meetingLocation("会议室B")
            .attendees(Arrays.asList("李四", "王五"))
            .build();

        when(meetingStandardRepository.findById(1L)).thenReturn(mockStandard);
        when(meetingPlanRepository.findConflictPlans(any(), any(), any(), any(), any()))
            .thenReturn(Arrays.asList(conflictPlan));

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> meetingPlanDomainService.createMeetingPlan(newMeetingPlan));

        assertTrue(exception.getMessage().contains("人员冲突"));
    }

    @Test
    void testBothConflicts() {
        // 准备冲突数据
        MeetingPlan conflictPlan = MeetingPlan.builder()
            .id(2L)
            .planName("已有会议")
            .plannedStartTime(LocalDateTime.of(2024, 4, 15, 9, 0))
            .plannedEndTime(LocalDateTime.of(2024, 4, 15, 11, 0))
            .meetingLocation("会议室A")
            .attendees(Arrays.asList("李四", "王五"))
            .build();

        when(meetingStandardRepository.findById(1L)).thenReturn(mockStandard);
        when(meetingPlanRepository.findConflictPlans(any(), any(), any(), any(), any()))
            .thenReturn(Arrays.asList(conflictPlan));

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> meetingPlanDomainService.createMeetingPlan(newMeetingPlan));

        assertTrue(exception.getMessage().contains("会议室冲突+人员冲突"));
    }

    @Test
    void testStartTimeValidation() {
        // 设置过去的时间
        newMeetingPlan.setPlannedStartTime(LocalDateTime.now().minusHours(1));
        
        when(meetingStandardRepository.findById(1L)).thenReturn(mockStandard);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
            () -> meetingPlanDomainService.createMeetingPlan(newMeetingPlan));

        assertTrue(exception.getMessage().contains("会议开始时间必须在当前时间之后"));
    }
}
