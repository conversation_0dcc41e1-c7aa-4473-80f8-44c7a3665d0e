package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.Application;
import cn.genn.third.feishu.app.FeishuAppClient;
import com.lark.oapi.service.calendar.v4.model.UserCalendar;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = Application.class)
@Slf4j
public class CalendarTest {

    @Resource
    private FeishuAppClient feishuAppClient;

    @Test
    public void Test(){
        UserCalendar userMainInfo = feishuAppClient.getCalendarService().getUserMainInfo();
        log.info(JsonUtils.toJson(userMainInfo));
    }
}
