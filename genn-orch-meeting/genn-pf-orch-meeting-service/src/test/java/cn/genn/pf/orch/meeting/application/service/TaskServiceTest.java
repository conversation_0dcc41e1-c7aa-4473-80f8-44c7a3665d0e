package cn.genn.pf.orch.meeting.application.service;

import cn.genn.core.model.ddd.IdQuery;
import cn.genn.pf.orch.meeting.application.service.action.TaskActionService;
import cn.genn.pf.orch.meeting.application.service.query.TaskQueryService;
import cn.genn.pf.orch.meeting.interfaces.command.TaskCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.TaskPriorityEnum;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class TaskServiceTest {

    @Resource
    private TaskActionService taskActionService;
    @Resource
    private TaskQueryService taskQueryService;

    @Test
    public void testTaskCRUD() {
        // 创建任务
        TaskCommand createCommand = new TaskCommand();
        createCommand.setTitle("测试任务");
        createCommand.setDescription("这是一个测试任务");
        createCommand.setOwnerOpenId("test_user_001");
        createCommand.setOwnerName("测试用户");
        createCommand.setPriority(TaskPriorityEnum.HIGH);
        createCommand.setDueDate(LocalDateTime.now().plusDays(7));
        createCommand.setMeetingId(1L);

        Long taskId = taskActionService.create(createCommand);
        System.out.println("创建任务成功，ID: " + taskId);

        // 查询任务详情
        IdQuery idQuery = new IdQuery();
        idQuery.setId(taskId);
        TaskDTO taskDTO = taskQueryService.detail(idQuery);
        System.out.println("查询任务详情: " + taskDTO);

        // 更新任务
        TaskCommand updateCommand = new TaskCommand();
        updateCommand.setId(taskId);
        updateCommand.setTitle("更新后的测试任务");
        updateCommand.setDescription("这是一个更新后的测试任务");
        updateCommand.setOwnerOpenId("test_user_001");
        updateCommand.setOwnerName("测试用户");
        updateCommand.setPriority(TaskPriorityEnum.MEDIUM);
        updateCommand.setDueDate(LocalDateTime.now().plusDays(10));
        updateCommand.setMeetingId(1L);

        taskActionService.update(updateCommand);
        System.out.println("更新任务成功");

        // 开始任务
        taskActionService.start(idQuery);
        System.out.println("开始任务成功");

        // 完成任务
        taskActionService.complete(idQuery);
        System.out.println("完成任务成功");

        // 再次查询任务详情
        TaskDTO updatedTaskDTO = taskQueryService.detail(idQuery);
        System.out.println("更新后的任务详情: " + updatedTaskDTO);

        // 删除任务
        taskActionService.delete(idQuery);
        System.out.println("删除任务成功");
    }

    @Test
    public void testTaskOverdue() {
        // 创建一个已经超期的任务
        TaskCommand createCommand = new TaskCommand();
        createCommand.setTitle("超期测试任务");
        createCommand.setDescription("这是一个超期测试任务");
        createCommand.setOwnerOpenId("test_user_002");
        createCommand.setOwnerName("测试用户2");
        createCommand.setPriority(TaskPriorityEnum.HIGH);
        createCommand.setDueDate(LocalDateTime.now().minusDays(1)); // 设置为昨天
        createCommand.setMeetingId(2L);

        Long taskId = taskActionService.create(createCommand);
        System.out.println("创建超期任务成功，ID: " + taskId);

        // 处理超期任务
        taskActionService.processOverdueTasks();
        System.out.println("处理超期任务完成");

        // 查询任务状态
        IdQuery idQuery = new IdQuery();
        idQuery.setId(taskId);
        TaskDTO taskDTO = taskQueryService.detail(idQuery);
        System.out.println("超期任务状态: " + taskDTO.getStatus());

        // 清理测试数据
        taskActionService.delete(idQuery);
    }
}
