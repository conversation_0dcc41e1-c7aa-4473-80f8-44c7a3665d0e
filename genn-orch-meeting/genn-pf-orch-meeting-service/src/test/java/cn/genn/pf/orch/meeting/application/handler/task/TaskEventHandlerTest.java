package cn.genn.pf.orch.meeting.application.handler.task;

import cn.genn.pf.orch.meeting.application.service.action.TaskSyncService;
import cn.genn.pf.orch.meeting.domain.task.model.aggregates.TaskAgg;
import cn.genn.pf.orch.meeting.domain.task.service.TaskDomainService;
import cn.genn.pf.orch.meeting.interfaces.enums.TaskPriorityEnum;
import com.lark.oapi.service.task.v1.model.P2TaskUpdatedV1Data;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务事件处理器测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class TaskEventHandlerTest {

    @Resource
    private TaskSyncService taskSyncService;
    @Resource
    private TaskDomainService taskDomainService;
    @Resource
    private TaskNotificationService taskNotificationService;

    @Test
    public void testTaskCompletedEvent() {
        // 先创建一个任务
        String feishuTaskId = "test_feishu_task_001";
        Long taskId = taskSyncService.createFromFeishuTask(
                feishuTaskId,
                "测试任务",
                "这是一个测试任务",
                "test_user_001",
                "测试用户",
                TaskPriorityEnum.HIGH,
                LocalDateTime.now().plusDays(7),
                1L,
                false
        );
        System.out.println("创建测试任务，ID: " + taskId);

        // 模拟任务完成事件
        P2TaskUpdatedV1Data notification = new P2TaskUpdatedV1Data();
        notification.setTaskId(feishuTaskId);
        notification.setObjType(5); // TASK_COMPLETED

        taskNotificationService.processNotification(notification);
        System.out.println("处理任务完成事件");

        // 验证任务状态
        TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        System.out.println("任务状态: " + taskAgg.getInfo().getStatus());
        System.out.println("完成时间: " + taskAgg.getInfo().getCompletedAt());

        // 清理测试数据
        taskDomainService.deleteByFeishuTaskId(feishuTaskId);
    }

    @Test
    public void testTaskCancelCompletedEvent() {
        // 先创建一个已完成的任务
        String feishuTaskId = "test_feishu_task_002";
        Long taskId = taskSyncService.createFromFeishuTask(
                feishuTaskId,
                "测试任务2",
                "这是一个测试任务2",
                "test_user_002",
                "测试用户2",
                TaskPriorityEnum.MEDIUM,
                LocalDateTime.now().plusDays(5),
                2L,
                true // 已完成
        );
        System.out.println("创建已完成的测试任务，ID: " + taskId);

        // 模拟任务取消完成事件
        P2TaskUpdatedV1Data notification = new P2TaskUpdatedV1Data();
        notification.setTaskId(feishuTaskId);
        notification.setObjType(6); // TASK_UNCOMPLETED

        taskNotificationService.processNotification(notification);
        System.out.println("处理任务取消完成事件");

        // 验证任务状态
        TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        System.out.println("任务状态: " + taskAgg.getInfo().getStatus());

        // 清理测试数据
        taskDomainService.deleteByFeishuTaskId(feishuTaskId);
    }

    @Test
    public void testTaskDeletedEvent() {
        // 先创建一个任务
        String feishuTaskId = "test_feishu_task_003";
        Long taskId = taskSyncService.createFromFeishuTask(
                feishuTaskId,
                "测试任务3",
                "这是一个测试任务3",
                "test_user_003",
                "测试用户3",
                TaskPriorityEnum.LOW,
                LocalDateTime.now().plusDays(3),
                3L,
                false
        );
        System.out.println("创建测试任务，ID: " + taskId);

        // 验证任务存在
        boolean exists = taskSyncService.existsByFeishuTaskId(feishuTaskId);
        System.out.println("任务是否存在: " + exists);

        // 模拟任务删除事件
        P2TaskUpdatedV1Data notification = new P2TaskUpdatedV1Data();
        notification.setTaskId(feishuTaskId);
        notification.setObjType(7); // TASK_DELETED

        taskNotificationService.processNotification(notification);
        System.out.println("处理任务删除事件");

        // 验证任务已删除
        boolean existsAfterDelete = taskSyncService.existsByFeishuTaskId(feishuTaskId);
        System.out.println("删除后任务是否存在: " + existsAfterDelete);
    }

    @Test
    public void testTaskDetailChangedEvent() {
        // 先创建一个任务
        String feishuTaskId = "test_feishu_task_004";
        Long taskId = taskSyncService.createFromFeishuTask(
                feishuTaskId,
                "原始任务标题",
                "原始任务描述",
                "test_user_004",
                "原始用户",
                TaskPriorityEnum.MEDIUM,
                LocalDateTime.now().plusDays(7),
                4L,
                false
        );
        System.out.println("创建测试任务，ID: " + taskId);

        // 模拟任务详情变化事件
        P2TaskUpdatedV1Data notification = new P2TaskUpdatedV1Data();
        notification.setTaskId(feishuTaskId);
        notification.setObjType(1); // TASK_DETAIL_CHANGED

        taskNotificationService.processNotification(notification);
        System.out.println("处理任务详情变化事件");

        // 手动同步任务详情（在实际场景中，这会从飞书API获取最新信息）
        taskSyncService.syncTaskDetails(
                feishuTaskId,
                "更新后的任务标题",
                "更新后的任务描述",
                "test_user_005",
                "更新后的用户",
                TaskPriorityEnum.HIGH,
                LocalDateTime.now().plusDays(10)
        );

        // 验证任务详情
        TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        System.out.println("更新后的任务标题: " + taskAgg.getInfo().getTitle());
        System.out.println("更新后的任务描述: " + taskAgg.getInfo().getDescription());
        System.out.println("更新后的负责人: " + taskAgg.getInfo().getOwnerName());
        System.out.println("更新后的优先级: " + taskAgg.getInfo().getPriority());

        // 清理测试数据
        taskDomainService.deleteByFeishuTaskId(feishuTaskId);
    }

    @Test
    public void testTaskSyncService() {
        String feishuTaskId = "test_sync_task_001";

        // 测试创建
        Long taskId = taskSyncService.createFromFeishuTask(
                feishuTaskId,
                "同步测试任务",
                "测试任务同步功能",
                "sync_user_001",
                "同步测试用户",
                TaskPriorityEnum.HIGH,
                LocalDateTime.now().plusDays(5),
                100L,
                false
        );
        System.out.println("创建同步测试任务，ID: " + taskId);

        // 测试状态同步
        taskSyncService.syncTaskStatus(feishuTaskId, true);
        TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        System.out.println("同步完成状态后: " + taskAgg.getInfo().getStatus());

        taskSyncService.syncTaskStatus(feishuTaskId, false);
        taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        System.out.println("同步未完成状态后: " + taskAgg.getInfo().getStatus());

        // 测试详情同步
        taskSyncService.syncTaskDetails(
                feishuTaskId,
                "同步后的标题",
                "同步后的描述",
                "sync_user_002",
                "同步后的用户",
                TaskPriorityEnum.LOW,
                LocalDateTime.now().plusDays(15)
        );

        taskAgg = taskDomainService.findByFeishuTaskId(feishuTaskId);
        System.out.println("同步详情后的标题: " + taskAgg.getInfo().getTitle());
        System.out.println("同步详情后的负责人: " + taskAgg.getInfo().getOwnerName());

        // 测试删除
        taskSyncService.deleteByFeishuTaskId(feishuTaskId);
        boolean exists = taskSyncService.existsByFeishuTaskId(feishuTaskId);
        System.out.println("删除后是否存在: " + exists);
    }
}
