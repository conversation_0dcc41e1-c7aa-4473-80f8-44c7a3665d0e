//package cn.genn.pf.orch.meeting.infrastructure.service;
//
//import cn.genn.pf.orch.meeting.interfaces.dto.KbRepoFileDTO;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import javax.annotation.Resource;
//import java.lang.reflect.Method;
//
///**
// * <AUTHOR> Assistant
// * @description 解析响应调试测试
// */
//@SpringBootTest
//@ActiveProfiles("test")
//public class ParseResponseDebugTest {
//
//    @Resource
//    private ExternalFileUploadService externalFileUploadService;
//    @Resource
//    private ObjectMapper objectMapper;
//
//    @Test
//    public void testParseYourActualResponse() throws Exception {
//        // 您提供的实际响应数据
//        String actualResponse = """
//            {
//                "success": true,
//                "traceid": "5a1fad6840d14fc48deedf3e71504638.86.17535144789095899",
//                "code": "000000200",
//                "msg": "success",
//                "err": null,
//                "data": {
//                    "repoId": 0,
//                    "collectionId": 0,
//                    "filePlatform": "pro-genn-ai-algorithm",
//                    "externalFileId": "/userUpload/19317457-ccc9-467f-9698-0e5ef2141d5d/煤业生产：分析汇报（7月22日）.docx",
//                    "externalFileUrl": "https://pro-genn-ai-algorithm.tos-cn-beijing.volces.com/%2FuserUpload%2F19317457-ccc9-467f-9698-0e5ef2141d5d%2F%E7%85%A4%E4%B8%9A%E7%94%9F%E4%BA%A7%EF%BC%9A%E5%88%86%E6%9E%90%E6%B1%87%E6%8A%A5%EF%BC%887%E6%9C%8822%E6%97%A5%EF%BC%89.docx?X-Tos-Algorithm=TOS4-HMAC-SHA256&response-content-disposition=attachment%3Bfilename%3D%25E7%2585%25A4%25E4%25B8%259A%25E7%2594%259F%25E4%25BA%25A7%25EF%25BC%259A%25E5%2588%2586%25E6%259E%2590%25E6%25B1%2587%25E6%258A%25A5%25EF%25BC%25887%25E6%259C%258822%25E6%2597%25A5%25EF%25BC%2589.docx&X-Tos-Credential=AKLTYmIyZGNhMGNhM2NkNGU5MTlmNmQxYjA4N2E3MjM4MTc%2F20250726%2Fcn-beijing%2Ftos%2Frequest&X-Tos-Expires=15552000&X-Tos-Date=20250726T072119Z&X-Tos-Signature=d46c99fd43042fa3980c45aa423a46dbb0bc882c833efc7b62000e49acfebd08&X-Tos-SignedHeaders=host",
//                    "length": 54752,
//                    "fileName": "煤业生产：分析汇报（7月22日）.docx",
//                    "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
//                    "metadata": null
//                },
//                "timestamp": 1753514479086
//            }
//            """;
//
//        System.out.println("=== 开始测试解析您的实际响应 ===");
//        System.out.println("响应长度：" + actualResponse.length());
//
//        // 使用反射调用私有方法
//        Method parseMethod = ExternalFileUploadService.class
//                .getDeclaredMethod("parseUploadResponse", String.class);
//        parseMethod.setAccessible(true);
//
//        KbRepoFileDTO result = (KbRepoFileDTO) parseMethod.invoke(externalFileUploadService, actualResponse);
//
//        System.out.println("=== 解析结果 ===");
//        if (result != null) {
//            System.out.println("✅ 解析成功！");
//            System.out.println("repoId: " + result.getRepoId());
//            System.out.println("collectionId: " + result.getCollectionId());
//            System.out.println("filePlatform: " + result.getFilePlatform());
//            System.out.println("externalFileId: " + result.getExternalFileId());
//            System.out.println("externalFileUrl: " + result.getExternalFileUrl());
//            System.out.println("length: " + result.getLength());
//            System.out.println("fileName: " + result.getFileName());
//            System.out.println("contentType: " + result.getContentType());
//            System.out.println("metadata: " + result.getMetadata());
//
//            // 验证关键字段
//            assert result.getRepoId() != null : "repoId不应该为null";
//            assert result.getCollectionId() != null : "collectionId不应该为null";
//            assert result.getFilePlatform() != null : "filePlatform不应该为null";
//            assert result.getExternalFileId() != null : "externalFileId不应该为null";
//            assert result.getExternalFileUrl() != null : "externalFileUrl不应该为null";
//            assert result.getLength() != null : "length不应该为null";
//            assert result.getFileName() != null : "fileName不应该为null";
//            assert result.getContentType() != null : "contentType不应该为null";
//
//            // 验证具体值
//            assert Long.valueOf(0).equals(result.getRepoId()) : "repoId应该为0";
//            assert Long.valueOf(0).equals(result.getCollectionId()) : "collectionId应该为0";
//            assert "pro-genn-ai-algorithm".equals(result.getFilePlatform()) : "filePlatform不匹配";
//            assert result.getExternalFileId().contains("煤业生产：分析汇报（7月22日）.docx") : "externalFileId应该包含文件名";
//            assert result.getExternalFileUrl().contains("pro-genn-ai-algorithm.tos-cn-beijing.volces.com") : "externalFileUrl域名不匹配";
//            assert Integer.valueOf(54752).equals(result.getLength()) : "length应该为54752";
//            assert "煤业生产：分析汇报（7月22日）.docx".equals(result.getFileName()) : "fileName不匹配";
//            assert "application/vnd.openxmlformats-officedocument.wordprocessingml.document".equals(result.getContentType()) : "contentType不匹配";
//
//            System.out.println("✅ 所有字段验证通过！");
//
//        } else {
//            System.out.println("❌ 解析失败，结果为null");
//            assert false : "解析不应该失败";
//        }
//    }
//
//    @Test
//    public void testDirectDataParse() throws Exception {
//        // 直接测试data部分的解析
//        String dataJson = """
//            {
//                "repoId": 0,
//                "collectionId": 0,
//                "filePlatform": "pro-genn-ai-algorithm",
//                "externalFileId": "/userUpload/19317457-ccc9-467f-9698-0e5ef2141d5d/煤业生产：分析汇报（7月22日）.docx",
//                "externalFileUrl": "https://pro-genn-ai-algorithm.tos-cn-beijing.volces.com/test.docx",
//                "length": 54752,
//                "fileName": "煤业生产：分析汇报（7月22日）.docx",
//                "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
//                "metadata": null
//            }
//            """;
//
//        System.out.println("=== 测试直接解析data部分 ===");
//
//        KbRepoFileDTO result = objectMapper.readValue(dataJson, KbRepoFileDTO.class);
//
//        System.out.println("=== 直接解析结果 ===");
//        if (result != null) {
//            System.out.println("✅ 直接解析成功！");
//            System.out.println("repoId: " + result.getRepoId());
//            System.out.println("collectionId: " + result.getCollectionId());
//            System.out.println("filePlatform: " + result.getFilePlatform());
//            System.out.println("externalFileId: " + result.getExternalFileId());
//            System.out.println("externalFileUrl: " + result.getExternalFileUrl());
//            System.out.println("length: " + result.getLength());
//            System.out.println("fileName: " + result.getFileName());
//            System.out.println("contentType: " + result.getContentType());
//            System.out.println("metadata: " + result.getMetadata());
//
//            assert result.getFileName() != null;
//            assert result.getExternalFileUrl() != null;
//            assert result.getContentType() != null;
//
//            System.out.println("✅ 直接解析验证通过！");
//        } else {
//            System.out.println("❌ 直接解析失败");
//            assert false : "直接解析不应该失败";
//        }
//    }
//
//    @Test
//    public void testJsonNodeParsing() throws Exception {
//        // 测试JsonNode解析
//        String actualResponse = """
//            {
//                "success": true,
//                "traceid": "5a1fad6840d14fc48deedf3e71504638.86.17535144789095899",
//                "code": "000000200",
//                "msg": "success",
//                "err": null,
//                "data": {
//                    "repoId": 0,
//                    "collectionId": 0,
//                    "filePlatform": "pro-genn-ai-algorithm",
//                    "externalFileId": "/userUpload/test/test.docx",
//                    "externalFileUrl": "https://pro-genn-ai-algorithm.tos-cn-beijing.volces.com/test.docx",
//                    "length": 54752,
//                    "fileName": "煤业生产：分析汇报（7月22日）.docx",
//                    "contentType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
//                    "metadata": null
//                },
//                "timestamp": 1753514479086
//            }
//            """;
//
//        System.out.println("=== 测试JsonNode解析步骤 ===");
//
//        // 步骤1：解析为JsonNode
//        com.fasterxml.jackson.databind.JsonNode rootNode = objectMapper.readTree(actualResponse);
//        System.out.println("步骤1 - 根节点解析成功");
//
//        // 步骤2：检查success字段
//        boolean isSuccess = rootNode.has("success") && rootNode.get("success").asBoolean();
//        System.out.println("步骤2 - success字段：" + isSuccess);
//        assert isSuccess : "success应该为true";
//
//        // 步骤3：检查data字段
//        assert rootNode.has("data") : "应该有data字段";
//        com.fasterxml.jackson.databind.JsonNode dataNode = rootNode.get("data");
//        assert !dataNode.isNull() : "data字段不应该为null";
//        System.out.println("步骤3 - data字段存在且不为null");
//
//        // 步骤4：检查data中的字段
//        System.out.println("步骤4 - 检查data中的字段：");
//        System.out.println("  repoId存在: " + dataNode.has("repoId"));
//        System.out.println("  collectionId存在: " + dataNode.has("collectionId"));
//        System.out.println("  filePlatform存在: " + dataNode.has("filePlatform"));
//        System.out.println("  externalFileId存在: " + dataNode.has("externalFileId"));
//        System.out.println("  externalFileUrl存在: " + dataNode.has("externalFileUrl"));
//        System.out.println("  length存在: " + dataNode.has("length"));
//        System.out.println("  fileName存在: " + dataNode.has("fileName"));
//        System.out.println("  contentType存在: " + dataNode.has("contentType"));
//
//        // 步骤5：转换为DTO
//        KbRepoFileDTO result = objectMapper.treeToValue(dataNode, KbRepoFileDTO.class);
//        System.out.println("步骤5 - 转换为DTO成功");
//
//        assert result != null : "转换结果不应该为null";
//        assert result.getFileName() != null : "fileName不应该为null";
//        assert result.getExternalFileUrl() != null : "externalFileUrl不应该为null";
//
//        System.out.println("✅ 所有步骤验证通过！");
//    }
//}
