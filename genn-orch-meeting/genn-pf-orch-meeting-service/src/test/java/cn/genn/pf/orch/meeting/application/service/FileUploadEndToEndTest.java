//package cn.genn.pf.orch.meeting.application.service;
//
//import cn.genn.pf.orch.meeting.application.service.action.AiEmpowermentService;
//import cn.genn.pf.orch.meeting.infrastructure.ai.agent.AgentInvokeService;
//import cn.genn.pf.orch.meeting.infrastructure.service.ExternalFileUploadService;
//import cn.genn.pf.orch.meeting.interfaces.command.FileUploadSummaryCommand;
//import cn.genn.pf.orch.meeting.interfaces.dto.KbRepoFileDTO;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.mock.web.MockMultipartFile;
//import org.springframework.test.context.ActiveProfiles;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR> Assistant
// * @description 文件上传端到端测试
// */
//@SpringBootTest
//@ActiveProfiles("test")
//public class FileUploadEndToEndTest {
//
//    @Resource
//    private AiEmpowermentService aiEmpowermentService;
//    @Resource
//    private ExternalFileUploadService externalFileUploadService;
//    @Resource
//    private AgentInvokeService agentInvokeService;
//    @Resource
//    private ObjectMapper objectMapper;
//
//    @Test
//    public void testKbRepoFileDTOCreation() {
//        // 测试手动创建KbRepoFileDTO
//        KbRepoFileDTO dto = new KbRepoFileDTO();
//        dto.setRepoId(0L);
//        dto.setCollectionId(0L);
//        dto.setFilePlatform("pro-genn-ai-algorithm");
//        dto.setExternalFileId("/userUpload/test/test.docx");
//        dto.setExternalFileUrl("https://pro-genn-ai-algorithm.tos-cn-beijing.volces.com/test.docx");
//        dto.setLength(54752);
//        dto.setFileName("煤业生产：分析汇报（7月22日）.docx");
//        dto.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
//        dto.setMetadata(null);
//
//        System.out.println("=== 手动创建的KbRepoFileDTO ===");
//        System.out.println("repoId: " + dto.getRepoId());
//        System.out.println("collectionId: " + dto.getCollectionId());
//        System.out.println("filePlatform: " + dto.getFilePlatform());
//        System.out.println("externalFileId: " + dto.getExternalFileId());
//        System.out.println("externalFileUrl: " + dto.getExternalFileUrl());
//        System.out.println("length: " + dto.getLength());
//        System.out.println("fileName: " + dto.getFileName());
//        System.out.println("contentType: " + dto.getContentType());
//        System.out.println("metadata: " + dto.getMetadata());
//
//        // 验证所有字段都有值
//        assert dto.getRepoId() != null;
//        assert dto.getCollectionId() != null;
//        assert dto.getFilePlatform() != null;
//        assert dto.getExternalFileId() != null;
//        assert dto.getExternalFileUrl() != null;
//        assert dto.getLength() != null;
//        assert dto.getFileName() != null;
//        assert dto.getContentType() != null;
//
//        System.out.println("✅ KbRepoFileDTO创建和验证成功！");
//    }
//
//    @Test
//    public void testRawDataBuilding() {
//        // 创建测试用的KbRepoFileDTO
//        KbRepoFileDTO dto = new KbRepoFileDTO();
//        dto.setRepoId(0L);
//        dto.setCollectionId(0L);
//        dto.setFilePlatform("pro-genn-ai-algorithm");
//        dto.setExternalFileId("/userUpload/test/test.docx");
//        dto.setExternalFileUrl("https://pro-genn-ai-algorithm.tos-cn-beijing.volces.com/test.docx");
//        dto.setLength(54752);
//        dto.setFileName("煤业生产：分析汇报（7月22日）.docx");
//        dto.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
//        dto.setMetadata(null);
//
//        // 使用反射调用buildRawDataFromKbRepoFileDTO方法
//        try {
//            java.lang.reflect.Method buildMethod = AiEmpowermentService.class
//                    .getDeclaredMethod("buildRawDataFromKbRepoFileDTO", KbRepoFileDTO.class, String.class);
//            buildMethod.setAccessible(true);
//
//            AgentInvokeService.RawData rawData = (AgentInvokeService.RawData) buildMethod
//                    .invoke(aiEmpowermentService, dto, "test-platform");
//
//            System.out.println("=== 构建的RawData ===");
//            System.out.println("repoId: " + rawData.getRepoId());
//            System.out.println("collectionId: " + rawData.getCollectionId());
//            System.out.println("filePlatform: " + rawData.getFilePlatform());
//            System.out.println("externalFileId: " + rawData.getExternalFileId());
//            System.out.println("externalFileUrl: " + rawData.getExternalFileUrl());
//            System.out.println("length: " + rawData.getLength());
//            System.out.println("fileName: " + rawData.getFileName());
//            System.out.println("contentType: " + rawData.getContentType());
//            System.out.println("metadata: " + rawData.getMetadata());
//
//            // 验证RawData字段
//            assert rawData.getRepoId() != null;
//            assert rawData.getCollectionId() != null;
//            assert rawData.getFilePlatform() != null;
//            assert rawData.getExternalFileId() != null;
//            assert rawData.getExternalFileUrl() != null;
//            assert rawData.getLength() != null;
//            assert rawData.getFileName() != null;
//            assert rawData.getContentType() != null;
//
//            System.out.println("✅ RawData构建和验证成功！");
//
//        } catch (Exception e) {
//            System.err.println("❌ RawData构建失败：" + e.getMessage());
//            e.printStackTrace();
//            assert false : "RawData构建不应该失败";
//        }
//    }
//
//    @Test
//    public void testAgentRequestBuilding() {
//        // 创建测试用的RawData
//        AgentInvokeService.RawData rawData = AgentInvokeService.RawData.builder()
//                .repoId(0)
//                .collectionId(0)
//                .filePlatform("pro-genn-ai-algorithm")
//                .externalFileId("/userUpload/test/test.docx")
//                .externalFileUrl("https://pro-genn-ai-algorithm.tos-cn-beijing.volces.com/test.docx")
//                .length(54752L)
//                .fileName("煤业生产：分析汇报（7月22日）.docx")
//                .contentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document")
//                .metadata(null)
//                .build();
//
//        // 使用反射调用buildFileWithRawDataAgentRequest方法
//        try {
//            java.lang.reflect.Method buildMethod = AgentInvokeService.class
//                    .getDeclaredMethod("buildFileWithRawDataAgentRequest",
//                            String.class, String.class, String.class, String.class, AgentInvokeService.RawData.class);
//            buildMethod.setAccessible(true);
//
//            cn.genn.pf.orch.meeting.infrastructure.ai.agent.request.AgentRequest request =
//                    (cn.genn.pf.orch.meeting.infrastructure.ai.agent.request.AgentRequest) buildMethod
//                    .invoke(agentInvokeService,
//                            "test-app-id",
//                            "https://pro-genn-ai-algorithm.tos-cn-beijing.volces.com/test.docx",
//                            "煤业生产：分析汇报（7月22日）.docx",
//                            "请分析这个文档",
//                            rawData);
//
//            System.out.println("=== 构建的AgentRequest ===");
//            String requestJson = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(request);
//            System.out.println(requestJson);
//
//            // 验证关键字段
//            assert request.getMessages() != null && !request.getMessages().isEmpty();
//
//            cn.genn.pf.orch.meeting.infrastructure.ai.agent.request.Message message = request.getMessages().get(0);
//            assert message.getContent() != null && !message.getContent().isEmpty();
//
//            cn.genn.pf.orch.meeting.infrastructure.ai.agent.request.Content fileContent = message.getContent().get(0);
//            assert "file_url".equals(fileContent.getType());
//            assert fileContent.getName() != null;
//            assert fileContent.getUrl() != null;
//            assert fileContent.getRawData() != null;
//
//            System.out.println("✅ AgentRequest构建和验证成功！");
//
//        } catch (Exception e) {
//            System.err.println("❌ AgentRequest构建失败：" + e.getMessage());
//            e.printStackTrace();
//            assert false : "AgentRequest构建不应该失败";
//        }
//    }
//
//    @Test
//    public void testMockFileUploadSummary() {
//        // 创建模拟文件
//        MockMultipartFile mockFile = new MockMultipartFile(
//                "file",
//                "煤业生产：分析汇报（7月22日）.docx",
//                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
//                "这是测试文件内容，模拟Word文档".getBytes()
//        );
//
//        // 创建文件上传汇总命令
//        FileUploadSummaryCommand command = new FileUploadSummaryCommand();
//        command.setFile(mockFile);
//        command.setAppId("test-app-id");
//        command.setCustomPrompt("请对这个文档进行详细汇总，重点关注关键信息和要点。");
//        command.setFilePlatform("test-platform");
//
//        System.out.println("=== 开始测试文件上传汇总 ===");
//        System.out.println("文件名：" + mockFile.getOriginalFilename());
//        System.out.println("文件类型：" + mockFile.getContentType());
//        System.out.println("文件大小：" + mockFile.getSize());
//
//        try {
//            // 执行文件上传汇总（这会调用外部服务，可能失败）
//            String result = aiEmpowermentService.fileUploadSummary(command);
//
//            System.out.println("=== 文件上传汇总结果 ===");
//            System.out.println(result);
//
//            // 如果成功，验证结果
//            if (result != null && !result.contains("失败")) {
//                System.out.println("✅ 文件上传汇总成功！");
//            } else {
//                System.out.println("⚠️ 文件上传汇总返回了错误信息，可能是外部服务不可用");
//            }
//
//        } catch (Exception e) {
//            System.out.println("⚠️ 文件上传汇总测试需要外部服务可用");
//            System.out.println("错误信息：" + e.getMessage());
//            // 不让测试失败，因为这依赖外部服务
//        }
//    }
//}
