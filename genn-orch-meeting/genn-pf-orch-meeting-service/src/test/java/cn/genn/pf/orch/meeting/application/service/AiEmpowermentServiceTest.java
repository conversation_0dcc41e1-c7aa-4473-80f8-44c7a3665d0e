package cn.genn.pf.orch.meeting.application.service;

import cn.genn.pf.orch.meeting.application.service.action.AiEmpowermentService;
import cn.genn.pf.orch.meeting.interfaces.command.FileSummaryCommand;
import cn.genn.pf.orch.meeting.interfaces.command.FileUploadSummaryCommand;
import cn.genn.pf.orch.meeting.interfaces.command.MixedContentAnalysisCommand;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR> Assistant
 * @description AI赋能服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class AiEmpowermentServiceTest {

    @Resource
    private AiEmpowermentService aiEmpowermentService;

    @Test
    public void testFileSummary() {
        // 创建文件汇总命令
        FileSummaryCommand command = new FileSummaryCommand();
        command.setFileUrl("https://example.com/test-document.pdf");
        command.setFileName("测试文档.pdf");
        command.setAppId("file_summary_agent");
        command.setCustomPrompt("请对这个文档进行详细的汇总，重点关注关键信息和要点。");

        // 执行文件汇总
        String result = aiEmpowermentService.fileSummary(command);

        System.out.println("文件汇总结果：");
        System.out.println(result);

        // 验证结果不为空
        assert result != null && !result.trim().isEmpty();
    }

    @Test
    public void testFileSummaryWithoutCustomPrompt() {
        // 创建文件汇总命令（不包含自定义提示词）
        FileSummaryCommand command = new FileSummaryCommand();
        command.setFileUrl("https://example.com/meeting-minutes.docx");
        command.setFileName("会议纪要.docx");

        // 执行文件汇总
        String result = aiEmpowermentService.fileSummary(command);

        System.out.println("文件汇总结果（无自定义提示词）：");
        System.out.println(result);

        // 验证结果不为空
        assert result != null && !result.trim().isEmpty();
    }

    @Test
    public void testFileSummaryWithoutFileName() {
        // 创建文件汇总命令（不包含文件名）
        FileSummaryCommand command = new FileSummaryCommand();
        command.setFileUrl("https://example.com/path/to/report.xlsx");
        command.setCustomPrompt("请分析这个报表的数据趋势和关键指标。");

        // 执行文件汇总
        String result = aiEmpowermentService.fileSummary(command);

        System.out.println("文件汇总结果（无文件名）：");
        System.out.println(result);

        // 验证结果不为空
        assert result != null && !result.trim().isEmpty();
    }

    @Test
    public void testExtractFileNameFromUrl() {
        // 测试从URL提取文件名的功能
        FileSummaryCommand command1 = new FileSummaryCommand();
        command1.setFileUrl("https://example.com/documents/report.pdf");

        FileSummaryCommand command2 = new FileSummaryCommand();
        command2.setFileUrl("https://example.com/files/meeting-notes.docx?version=1&token=abc123");

        FileSummaryCommand command3 = new FileSummaryCommand();
        command3.setFileUrl("https://example.com/uploads/presentation.pptx");

        // 执行文件汇总（这会触发文件名提取逻辑）
        String result1 = aiEmpowermentService.fileSummary(command1);
        String result2 = aiEmpowermentService.fileSummary(command2);
        String result3 = aiEmpowermentService.fileSummary(command3);

        System.out.println("测试文件名提取功能完成");

        // 验证结果不为空
        assert result1 != null && !result1.trim().isEmpty();
        assert result2 != null && !result2.trim().isEmpty();
        assert result3 != null && !result3.trim().isEmpty();
    }

    @Test
    public void testFileSummaryWithImages() {
        // 创建包含图片的文件汇总命令
        FileSummaryCommand command = new FileSummaryCommand();
        command.setFileUrl("https://example.com/presentation.pptx");
        command.setFileName("产品介绍.pptx");
        command.setCustomPrompt("请分析这个演示文稿和相关图片，总结产品的核心特性。");
        command.setImageUrls(Arrays.asList(
            "https://example.com/product-image1.jpg",
            "https://example.com/product-image2.jpg"
        ));

        // 执行文件汇总
        String result = aiEmpowermentService.fileSummary(command);

        System.out.println("文件+图片汇总结果：");
        System.out.println(result);

        // 验证结果不为空
        assert result != null && !result.trim().isEmpty();
    }

    @Test
    public void testMixedContentAnalysis() {
        // 创建混合内容分析命令
        MixedContentAnalysisCommand command = new MixedContentAnalysisCommand();
        command.setTextMessage("请分析这个产品的市场竞争力，结合文档和图片信息给出评估。");
        command.setImageUrls(Arrays.asList(
            "https://example.com/market-chart.png",
            "https://example.com/competitor-analysis.jpg"
        ));
        command.setFileUrl("https://example.com/market-report.pdf");
        command.setFileName("市场分析报告.pdf");
        command.setAppId("market_analysis_agent");

        // 执行混合内容分析
        String result = aiEmpowermentService.mixedContentAnalysis(command);

        System.out.println("混合内容分析结果：");
        System.out.println(result);

        // 验证结果不为空
        assert result != null && !result.trim().isEmpty();
    }

    @Test
    public void testMixedContentAnalysisTextOnly() {
        // 创建只有文本的混合内容分析命令
        MixedContentAnalysisCommand command = new MixedContentAnalysisCommand();
        command.setTextMessage("请帮我分析一下当前AI技术的发展趋势和应用前景。");
        command.setAppId("general_analysis_agent");

        // 执行混合内容分析
        String result = aiEmpowermentService.mixedContentAnalysis(command);

        System.out.println("纯文本分析结果：");
        System.out.println(result);

        // 验证结果不为空
        assert result != null && !result.trim().isEmpty();
    }

    @Test
    public void testMixedContentAnalysisImageOnly() {
        // 创建只有图片的混合内容分析命令
        MixedContentAnalysisCommand command = new MixedContentAnalysisCommand();
        command.setTextMessage("请分析这些图片中的内容和趋势。");
        command.setImageUrls(Arrays.asList(
            "https://example.com/chart1.png",
            "https://example.com/chart2.png",
            "https://example.com/diagram.jpg"
        ));
        command.setAppId("image_analysis_agent");

        // 执行混合内容分析
        String result = aiEmpowermentService.mixedContentAnalysis(command);

        System.out.println("图片分析结果：");
        System.out.println(result);

        // 验证结果不为空
        assert result != null && !result.trim().isEmpty();
    }

    @Test
    public void testFileUploadSummary() {
        // 创建模拟文件
        String fileContent = "这是一个测试文档的内容。\n\n" +
                "主要内容包括：\n" +
                "1. 项目概述\n" +
                "2. 技术方案\n" +
                "3. 实施计划\n" +
                "4. 风险评估\n\n" +
                "项目预计在3个月内完成，需要投入5名开发人员。";

        MockMultipartFile mockFile = new MockMultipartFile(
                "file",
                "test-document.txt",
                "text/plain",
                fileContent.getBytes()
        );

        // 创建文件上传汇总命令
        FileUploadSummaryCommand command = new FileUploadSummaryCommand();
        command.setFile(mockFile);
        command.setAppId("file_summary_agent");
        command.setCustomPrompt("请对这个文档进行详细汇总，重点关注项目的关键信息。");
        command.setFilePlatform("test-platform");

        // 执行文件上传汇总（注意：这个测试需要外部服务可用）
        try {
            String result = aiEmpowermentService.fileUploadSummary(command);

            System.out.println("文件上传汇总结果：");
            System.out.println(result);

            // 验证结果不为空
            assert result != null && !result.trim().isEmpty();
        } catch (Exception e) {
            System.out.println("测试需要外部文件上传服务可用，当前测试环境可能无法连接外部服务");
            System.out.println("错误信息：" + e.getMessage());
        }
    }

    @Test
    public void testFileUploadSummaryWord() {
        // 创建模拟Word文档
        String fileContent = "Word文档内容模拟";

        MockMultipartFile mockFile = new MockMultipartFile(
                "file",
                "test-report.docx",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                fileContent.getBytes()
        );

        // 创建文件上传汇总命令
        FileUploadSummaryCommand command = new FileUploadSummaryCommand();
        command.setFile(mockFile);
        command.setCustomPrompt("请分析这个Word文档的主要内容和结构。");

        // 执行文件上传汇总
        String result = aiEmpowermentService.fileUploadSummary(command);

        System.out.println("Word文档上传汇总结果：");
        System.out.println(result);

        // 验证结果不为空
        assert result != null && !result.trim().isEmpty();
    }

    @Test
    public void testFileUploadSummaryPDF() {
        // 创建模拟PDF文档
        String fileContent = "PDF文档内容模拟";

        MockMultipartFile mockFile = new MockMultipartFile(
                "file",
                "test-analysis.pdf",
                "application/pdf",
                fileContent.getBytes()
        );

        // 创建文件上传汇总命令
        FileUploadSummaryCommand command = new FileUploadSummaryCommand();
        command.setFile(mockFile);
        command.setCustomPrompt("请提取PDF中的关键数据和结论。");
        command.setFilePlatform("pdf-analysis-platform");

        // 执行文件上传汇总
        String result = aiEmpowermentService.fileUploadSummary(command);

        System.out.println("PDF文档上传汇总结果：");
        System.out.println(result);

        // 验证结果不为空
        assert result != null && !result.trim().isEmpty();
    }
}
