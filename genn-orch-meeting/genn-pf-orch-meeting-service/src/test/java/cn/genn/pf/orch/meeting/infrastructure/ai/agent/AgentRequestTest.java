package cn.genn.pf.orch.meeting.infrastructure.ai.agent;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.infrastructure.ai.agent.request.AgentRequest;
import org.junit.jupiter.api.Test;

import java.util.Arrays;

/**
 * <AUTHOR> Assistant
 * @description AgentRequest测试
 */
public class AgentRequestTest {

    @Test
    public void testAgentRequestSerialization() {
        // 构建RawData
        AgentRequest.RawData rawData = AgentRequest.RawData.builder()
                .repoId(0)
                .collectionId(0)
                .filePlatform("test-platform")
                .externalFileId("/test/file.pdf")
                .externalFileUrl("https://example.com/file.pdf")
                .length(12345L)
                .fileName("test.pdf")
                .contentType("application/pdf")
                .metadata(null)
                .build();

        // 构建文件Content
        AgentRequest.Content fileContent = AgentRequest.Content.builder()
                .type("file_url")
                .name("test.pdf")
                .url("https://example.com/file.pdf")
                .rawData(rawData)
                .build();

        // 构建文本Content
        AgentRequest.Content textContent = AgentRequest.Content.builder()
                .type("text")
                .text("请分析这个文档")
                .build();

        // 构建消息
        AgentRequest.MessageDTO message = AgentRequest.MessageDTO.builder()
                .content(Arrays.asList(fileContent, textContent))
                .role("user")
                .hideInUI(false)
                .build();

        // 构建完整请求
        AgentRequest request = AgentRequest.builder()
                .appId("test-app-id")
                .detail(true)
                .stream(true)
                .messages(Arrays.asList(message))
                .build();

        // 序列化为JSON
        String json = JsonUtils.toJson(request);
        System.out.println("=== AgentRequest JSON ===");
        System.out.println(json);

        // 检查关键字段
        assert json.contains("\"type\":\"file_url\"");
        assert json.contains("\"name\":\"test.pdf\"");
        assert json.contains("\"url\":\"https://example.com/file.pdf\"");
        assert json.contains("\"contentType\":\"application/pdf\"");
        assert json.contains("\"externalFileUrl\":\"https://example.com/file.pdf\"");

        System.out.println("JSON序列化测试通过！");
    }

    @Test
    public void testRawDataSerialization() {
        AgentRequest.RawData rawData = AgentRequest.RawData.builder()
                .repoId(123)
                .collectionId(456)
                .filePlatform("test-platform")
                .externalFileId("/test/document.docx")
                .externalFileUrl("https://storage.example.com/document.docx")
                .length(98765L)
                .fileName("document.docx")
                .contentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                .metadata("test metadata")
                .build();

        String json = JsonUtils.toJson(rawData);
        System.out.println("=== RawData JSON ===");
        System.out.println(json);

        // 验证所有字段都被序列化
        assert json.contains("\"repoId\":123");
        assert json.contains("\"collectionId\":456");
        assert json.contains("\"filePlatform\":\"test-platform\"");
        assert json.contains("\"externalFileId\":\"/test/document.docx\"");
        assert json.contains("\"externalFileUrl\":\"https://storage.example.com/document.docx\"");
        assert json.contains("\"length\":98765");
        assert json.contains("\"fileName\":\"document.docx\"");
        assert json.contains("\"contentType\":\"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"");
        assert json.contains("\"metadata\":\"test metadata\"");

        System.out.println("RawData序列化测试通过！");
    }

    @Test
    public void testContentSerialization() {
        AgentRequest.Content content = AgentRequest.Content.builder()
                .type("file_url")
                .name("example.pdf")
                .url("https://example.com/example.pdf")
                .rawData(AgentRequest.RawData.builder()
                        .repoId(0)
                        .collectionId(0)
                        .filePlatform("example-platform")
                        .externalFileId("/example/example.pdf")
                        .externalFileUrl("https://example.com/example.pdf")
                        .length(54321L)
                        .fileName("example.pdf")
                        .contentType("application/pdf")
                        .build())
                .build();

        String json = JsonUtils.toJson(content);
        System.out.println("=== Content JSON ===");
        System.out.println(json);

        // 验证Content的关键字段
        assert json.contains("\"type\":\"file_url\"");
        assert json.contains("\"name\":\"example.pdf\"");
        assert json.contains("\"url\":\"https://example.com/example.pdf\"");
        assert json.contains("\"rawData\":");

        // 验证rawData字段不为null
        assert !json.contains("\"rawData\":null");

        System.out.println("Content序列化测试通过！");
    }
}
