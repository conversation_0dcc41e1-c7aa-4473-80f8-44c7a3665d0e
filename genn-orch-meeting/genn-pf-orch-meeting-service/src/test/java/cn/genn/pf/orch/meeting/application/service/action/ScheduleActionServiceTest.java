// package cn.genn.pf.orch.meeting.application.service.action;
//
// import cn.genn.pf.orch.meeting.interfaces.command.AttendUserCommand;
// import cn.genn.pf.orch.meeting.interfaces.command.ScheduleCommand;
// import cn.genn.pf.orch.meeting.interfaces.command.ScheduleConfigCommand;
// import cn.genn.pf.orch.meeting.interfaces.command.UpdateScheduleCommand;
// import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
// import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
// import cn.genn.third.feishu.app.FeishuAppClient;
// import com.lark.oapi.service.contact.v3.model.UserContactInfo;
// import org.junit.jupiter.api.Test;
// import org.springframework.boot.test.context.SpringBootTest;
//
// import javax.annotation.Resource;
//
// import java.time.LocalDateTime;
// import java.util.ArrayList;
// import java.util.Collections;
//
// import static org.junit.jupiter.api.Assertions.*;
//
// @SpringBootTest
// class ScheduleActionServiceTest {
//
//     @Resource
//     private ScheduleActionService scheduleActionService;
//     @Resource
//     private FeishuAppClient feishuAppClient;
//
//     @Test
//     void addConfig() {
//         ScheduleConfigCommand command = ScheduleConfigCommand.builder()
//             .goal("会议目的")
//             .decision("会议决策")
//             .businessMeetingId(3L)
//             .dataCatalog(Collections.singletonList("自保、互保、联保协议书"))
//             .achievementCatalog(Collections.singletonList("会议纪要"))
//             .build();
//         scheduleActionService.addConfig(command);
//     }
//
//     @Test
//     void updateConfig() {
//         ArrayList<String> achievementCatalogs = new ArrayList<>();
//         achievementCatalogs.add("会议纪要1");
//         achievementCatalogs.add("会议纪要2");
//
//         ScheduleConfigCommand command = ScheduleConfigCommand.builder()
//             .goal("会议目的")
//             .decision("一年狂澜100亿")
//             .businessMeetingId(3L)
//             .dataCatalog(Collections.singletonList("自保、互保、联保协议书"))
//             .achievementCatalog(achievementCatalogs)
//             .build();
//         scheduleActionService.updateConfig(command);
//     }
//
//     @Test
//     void testAdd() {
//         UserContactInfo[] userList = feishuAppClient.getContactService().getOpenId("***********").getUserList();
//
//         AttendUserCommand attendUserCommand = AttendUserCommand.builder()
//             .attendUserId(userList[0].getUserId())
//             .attendUserName("杨家炜")
//             .attendUserRole(AttendUserRoleEnum.CREATOR)
//             .attendUserState(AttendUserStateEnum.EXPECT)
//             .build();
//
//         ScheduleCommand scheduleCommand = ScheduleCommand.builder()
//             .startTime(LocalDateTime.of(2025, 1, 10, 20, 40, 0))
//             .endTime(LocalDateTime.of(2025, 1, 10, 21, 40, 0))
//             .teamId(1L)
//             .businessMeetingId(3L)
//             .scheduleNamePrefix("小杨的测试会议")
//             .attendUsers(Collections.singletonList(attendUserCommand))
//             .build();
//
//         scheduleActionService.add(scheduleCommand);
//     }
//
//     @Test
//     public void testUpdate() {
//         UserContactInfo[] userList = feishuAppClient.getContactService().getOpenId("***********").getUserList();
//
//         AttendUserCommand attendUserCommand = AttendUserCommand.builder()
//             .attendUserId(userList[0].getUserId())
//             .attendUserName("张志伟")
//             .attendUserRole(AttendUserRoleEnum.NORMAL)
//             .attendUserState(AttendUserStateEnum.EXPECT)
//             .build();
//
//         UpdateScheduleCommand updateScheduleCommand = new UpdateScheduleCommand();
//         updateScheduleCommand.setScheduleId(5L);
//         updateScheduleCommand.setStartTime(LocalDateTime.of(2025, 1, 4, 18, 30, 0));
//         updateScheduleCommand.setEndTime(LocalDateTime.of(2025, 1, 4, 19, 40, 0));
//         updateScheduleCommand.setTeamId(2L);
//         updateScheduleCommand.setBusinessMeetingId(3L);
//         updateScheduleCommand.setScheduleNamePrefix("小杨新的会议名称Test");
//         updateScheduleCommand.setAttendUsers(Collections.singletonList(attendUserCommand));
//
//         scheduleActionService.update(updateScheduleCommand);
//     }
//
//     @Test
//     public void delete() {
//         scheduleActionService.delete(3L);
//     }
//
//     @Test
//     public void testSubscription() {
//         scheduleActionService.subscription();
//     }
// }
