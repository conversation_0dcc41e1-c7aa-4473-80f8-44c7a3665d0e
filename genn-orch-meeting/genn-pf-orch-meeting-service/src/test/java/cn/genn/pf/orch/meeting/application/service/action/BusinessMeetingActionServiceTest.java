package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.pf.orch.meeting.application.service.query.BusinessMeetingQueryService;
import cn.genn.pf.orch.meeting.interfaces.command.AddBusinessMeetingCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.BusinessMeetingLevelDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.SingleBusinessMeetingDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
class BusinessMeetingActionServiceTest {

    @Resource
    private BusinessMeetingActionService businessMeetingActionService;
    @Resource
    private BusinessMeetingQueryService businessMeetingQueryService;

    @Test
    void add() {
        AddBusinessMeetingCommand addBusinessMeetingCommand = new AddBusinessMeetingCommand();
        addBusinessMeetingCommand.setName("地产事业部会议");
        addBusinessMeetingCommand.setParentId(1L);
        businessMeetingActionService.add(addBusinessMeetingCommand);
    }

    @Test
    void add2() {
        AddBusinessMeetingCommand addBusinessMeetingCommand = new AddBusinessMeetingCommand();
        addBusinessMeetingCommand.setName("经营类会议");
        businessMeetingActionService.add(addBusinessMeetingCommand);
    }

    @Test
    void level() {
        BusinessMeetingLevelDTO level = businessMeetingQueryService.level();
        System.out.println("level = " + level);
    }

    @Test
    void list() {
        List<SingleBusinessMeetingDTO> list = businessMeetingQueryService.list();
        System.out.println("list = " + list);
    }
}
