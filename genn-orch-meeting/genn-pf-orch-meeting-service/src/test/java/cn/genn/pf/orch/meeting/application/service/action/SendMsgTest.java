package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.pf.orch.meeting.Application;
import cn.genn.third.feishu.app.FeishuAppClient;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserRespBody;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;

@SpringBootTest(classes = Application.class)
@Slf4j
public class SendMsgTest {

    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private FeishuAppClient feishuAppClient;


    @Test
    public void Test(){
        String telephone = "18435186300";
        BatchGetIdUserRespBody body = feishuAppClient.getContactService().getOpenId(telephone);
        String openId = Arrays.asList(body.getUserList()).get(0).getUserId();
        // cardSendActionService.sendSignInCode(openId);
        // cardSendActionService.sendSignInRemind(openId);
        // cardSendActionService.sendUploadMeetingRecord(openId);
        // cardSendActionService.sendMeetingEvaluation(openId);
        // cardSendActionService.sendShareMeetingVideo(openId);
    }
}
