package cn.genn.pf.orch.meeting.application.service.query;

import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleConfigDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.pf.orch.meeting.interfaces.query.ScheduleConfigQuery;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest
class ScheduleQueryServiceTest {

    @Resource
    private ScheduleQueryService scheduleQueryService;
    @Test
    void getConfig() {
        ScheduleConfigQuery query = ScheduleConfigQuery.builder().businessMeetingId(3L).build();

        ScheduleConfigDTO config = scheduleQueryService.getConfig(query);
        System.out.println("config = " + config);
    }

    @Test
    void get() {
        ScheduleDTO scheduleId = scheduleQueryService.getByScheduleId(16L);
        System.out.println("scheduleId = " + scheduleId);
    }
}
