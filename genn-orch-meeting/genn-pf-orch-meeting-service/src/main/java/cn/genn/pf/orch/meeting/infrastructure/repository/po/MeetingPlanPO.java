package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.database.mybatisplus.typehandler.ListStringTypeHandler;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingPlanStatusEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.PriorityLevelEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * MeetingPlanPO对象
 *
 * <AUTHOR>
 * @desc 会议规划表
 */
@Data
@Accessors(chain = true)
@TableName(value = "meeting_plan", autoResultMap = true)
public class MeetingPlanPO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 会议规划名称
     */
    @TableField("plan_name")
    private String planName;

    /**
     * 会议规划描述/备注
     */
    @TableField("plan_description")
    private String planDescription;

    /**
     * 计划开始时间
     */
    @TableField("planned_start_time")
    private LocalDateTime plannedStartTime;

    /**
     * 计划结束时间
     */
    @TableField("planned_end_time")
    private LocalDateTime plannedEndTime;

    /**
     * 计划持续时长(分钟)
     */
    @TableField("planned_duration")
    private Integer plannedDuration;

    /**
     * 会议规划状态
     */
    @TableField("status")
    private MeetingPlanStatusEnum status;

    /**
     * 会议标准ID
     */
    @TableField("meeting_standard_id")
    private Long meetingStandardId;

    /**
     * 优先级
     */
    @TableField("priority_level")
    private PriorityLevelEnum priorityLevel;

    /**
     * 部门ID
     */
    @TableField("department_id")
    private Long departmentId;

    /**
     * 部门名称
     */
    @TableField("department_name")
    private String departmentName;

    /**
     * 业务会议ID
     */
    @TableField("business_meeting_id")
    private Long businessMeetingId;

    /**
     * 参会人员列表
     */
    @TableField(value = "attendees", typeHandler = ListStringTypeHandler.class)
    private List<String> attendees;

    /**
     * 会议地点
     */
    @TableField("meeting_location")
    private String meetingLocation;

    /**
     * 提前通知发送标记(0-未发送,1-已发送)
     */
    @TableField("advance_notice_sent")
    private Integer advanceNoticeSent;

    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新人姓名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记(0-未删除,1-已删除)
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;
}
