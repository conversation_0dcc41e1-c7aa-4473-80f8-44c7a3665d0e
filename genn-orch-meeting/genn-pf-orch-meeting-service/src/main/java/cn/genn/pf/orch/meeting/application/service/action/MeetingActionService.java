package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.pf.orch.meeting.application.dto.card.SendSignInCodeDTO;
import cn.genn.pf.orch.meeting.application.dto.card.SendSignInRemindDTO;
import cn.genn.pf.orch.meeting.application.processor.MeetingProcessor;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingAttendUser;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingInfo;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IMeetingRepository;
import cn.genn.pf.orch.meeting.domain.schedule.service.MeetingDomainService;
import cn.genn.pf.orch.meeting.infrastructure.config.CurrentUserHolder;
import cn.genn.pf.orch.meeting.infrastructure.constant.CacheConstants;
import cn.genn.pf.orch.meeting.infrastructure.exception.MessageCode;
import cn.genn.pf.orch.meeting.infrastructure.properties.MeetingSeverProperties;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.ScheduleAttendUserMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.ScheduleAttendUserPO;
import cn.genn.pf.orch.meeting.infrastructure.utils.DateUtils;
import cn.genn.pf.orch.meeting.interfaces.command.*;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.AttendUserDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.CheckInStatusEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingStatusEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议应用层操作服务
 * @date 2025-01-02
 */
@Service
public class MeetingActionService {

    @Resource
    private MeetingDomainService meetingDomainService;
    @Resource
    private MeetingProcessor meetingProcessor;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private MeetingSeverProperties meetingSeverProperties;
    @Resource
    private ScheduleAttendUserMapper scheduleAttendUserMapper;
    @Resource
    private ScheduleQueryService scheduleQueryService;
    @Resource
    private IMeetingRepository meetingRepository;

    public void uploadRecord(MeetingUploadRecordCommand command) {
        MeetingAgg meetingAgg = meetingDomainService.findById(command.getId());

        ScheduleDTO scheduleDTO = scheduleQueryService.getByScheduleId(meetingAgg.getInfo().getScheduleId());
        Set<String> recorders = scheduleDTO.getAttendUsers().stream()
            .filter(attendUserDTO -> attendUserDTO.getAttendUserRole().equals(AttendUserRoleEnum.RECORDER))
            .map(AttendUserDTO::getAttendUserId)
            .collect(Collectors.toSet());
        if (!recorders.contains(CurrentUserHolder.getCurrentUser().getOpenId())) {
            // 不是记录人
            throw new BusinessException(MessageCode.CURRENT_IDENTITY_NOT_ALLOW_UPLOADING);
        }

        if (meetingAgg.getInfo().getStatus().equals(MeetingStatusEnum.WAIT_START)) {
            // 会议未开始，不允许上传
            throw new BusinessException(MessageCode.UPLOAD_NOT_ALLOWED_BEFORE_MEETING_STARTS);
        }

        if (meetingAgg.getInfo().getStatus().equals(MeetingStatusEnum.END)) {
            if (meetingAgg.getInfo().getEndTime().plusHours(2L).isBefore(LocalDateTime.now())) {
                // 会议已结束, 超过2小时, 且已经上传
                if (!meetingRepository.findNotUploadedMeetingIds().contains(command.getId())) {
                    throw new BusinessException(MessageCode.EXCEEDING_UPLOAD_TIME);
                }
            }
        }

        meetingDomainService.uploadRecord(meetingAgg, command);
    }

    public void uploadEvaluate(MeetingUploadEvaluateCommand command) {
        MeetingAgg meetingAgg = meetingDomainService.findById(command.getId());
        meetingDomainService.uploadEvaluate(meetingAgg, command);
    }

    public void signIn(MeetingSignInCommand command) {
        if(StrUtil.isBlank(command.getOpenId())){
            command.setOpenId(CurrentUserHolder.getOpenId());
        }
        meetingProcessor.checkSignIn(command);
        MeetingAttendUser meetingAttendUser = MeetingAttendUser.builder()
            .meetingId(command.getMeetingId())
            .fsUserId(command.getOpenId())
            .checkInStatus(CheckInStatusEnum.CHECKED)
            .checkInAddress(command.getCheckInAddress())
            .checkInTime(LocalDateTime.now())
            .build();
        meetingDomainService.signIn(meetingAttendUser);
    }

    public void sendSignInCode(Long id) {
        String key = CacheConstants.getSignInCodeLock(id);
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(key))) {
            return;
        }
        stringRedisTemplate.opsForValue().set(key, LocalDateTime.now().toString(), 1, TimeUnit.DAYS);
        MeetingAgg meetingAgg = meetingDomainService.findById(id);
        MeetingInfo info = meetingAgg.getInfo();
        // 获取签到码人员
        List<String> codeOpenIds = getSignCodeOpenIds(info.getScheduleId());
        // 获取会议时间
        ScheduleDTO scheduleDTO = scheduleQueryService.getByScheduleId(info.getScheduleId());
        for (String openId : codeOpenIds) {
            SendSignInCodeDTO dto = SendSignInCodeDTO.builder()
                .openId(openId)
                .meetingType(scheduleDTO.getBusinessMeetingName())
                .meetingName(info.getName())
                .meetingTime(DateUtils.getTimeRange(scheduleDTO.getStartTime(), scheduleDTO.getEndTime()))
                .meetingUrl(info.getMeetingUrl())
                .signInCode(info.getCheckInNumber())
                .build();
            cardSendActionService.sendSignInCode(dto);
        }
        // 获取参会人员
        List<String> signInOpenIds = getSignInRemind(info.getScheduleId());
        String time = DateUtils.formatLocalDateTime(scheduleDTO.getStartTime());
        String url = meetingSeverProperties.getCardSend().getJumpUrl().getSignInUrl() + "?id=" + info.getId()+"&name="+info.getName();
        for (String openId : signInOpenIds) {
            SendSignInRemindDTO dto = SendSignInRemindDTO.builder()
                .openId(openId)
                .meetingType(scheduleDTO.getBusinessMeetingName())
                .meetingName(info.getName())
                .signTime(time)
                .signUrl(url)
                .build();
            cardSendActionService.sendSignInRemind(dto);
        }
    }

    /**
     * 获取签到码人员
     */
    private List<String> getSignCodeOpenIds(Long scheduleId) {
        ScheduleDTO dto = scheduleQueryService.getByScheduleId(scheduleId);
        TeamInfoDTO teamInfoDTO = dto.getTeamInfoDTO();
        List<String> openIds = new ArrayList<>();
        if (StrUtil.isNotBlank(teamInfoDTO.getLeader())) {
            openIds.add(teamInfoDTO.getLeader());
        }
        if (CollUtil.isNotEmpty(teamInfoDTO.getTechnicians())) {
            openIds.addAll(teamInfoDTO.getTechnicians());
        }
        //记录员
        List<String> recordIds = dto.getAttendUsers().stream().filter(item -> AttendUserRoleEnum.RECORDER.equals(item.getAttendUserRole()) && AttendUserStateEnum.EXPECT.equals(item.getAttendUserState()))
            .map(AttendUserDTO::getAttendUserId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(recordIds)) {
            openIds.addAll(recordIds);
        }
        return openIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获取参会人员
     */
    private List<String> getSignInRemind(Long scheduleId) {
        List<ScheduleAttendUserPO> poList = scheduleAttendUserMapper.selectScheduleAndAttendState(scheduleId, AttendUserStateEnum.EXPECT);
        return poList.stream().map(ScheduleAttendUserPO::getAttendUserId).distinct().collect(Collectors.toList());
    }

    public void saveRecordDraft(MeetingUploadRecordCommand command) {
        MeetingAgg meetingAgg = meetingDomainService.findById(command.getId());
        meetingDomainService.saveRecordDraft(meetingAgg, command);
    }

    public void saveEvaluateDraft(MeetingUploadEvaluateCommand command) {
        MeetingAgg meetingAgg = meetingDomainService.findById(command.getId());
        meetingDomainService.saveEvaluateDraft(meetingAgg, command);
    }

    public void deleteRecordDraft(Long meetingId) {
        meetingDomainService.deleteRecordDraft(meetingId);
    }

    public void deleteEvaluateDraft(Long meetingId) {
        meetingDomainService.deleteEvaluateDraft(meetingId);
    }
}
