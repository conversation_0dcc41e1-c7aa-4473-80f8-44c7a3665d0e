package cn.genn.pf.orch.meeting.infrastructure.repository.mapper;

import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPO;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingListDTO;
import cn.genn.pf.orch.meeting.interfaces.query.MeetingQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MeetingMapper extends BaseMapper<MeetingPO> {
    Page<MeetingListDTO> miniPage(Page<MeetingPO> page, @Param("meetingQuery") MeetingQuery meetingQuery);

    List<String> queryValidCheckInNumbers();

    /**
     * 获取未开始,且开始时间小于30分钟的会议
     * @return
     */
    List<MeetingPO> queryNoStartStartMeeting();

    List<MeetingPO> queryInProcessMeeting();

}
