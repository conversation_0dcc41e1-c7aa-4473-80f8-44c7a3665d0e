package cn.genn.pf.orch.meeting.infrastructure.converter;

import cn.genn.core.model.converter.POConverter;
import cn.genn.pf.orch.meeting.domain.team.model.entity.TeamInfo;
import cn.genn.pf.orch.meeting.domain.team.model.entity.TeamLevel;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamInfoPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamLevelPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TeamConverter{

    TeamLevelPO toPO(TeamLevel teamLevel);

    TeamInfoPO toPO(TeamInfo teamInfo);
}
