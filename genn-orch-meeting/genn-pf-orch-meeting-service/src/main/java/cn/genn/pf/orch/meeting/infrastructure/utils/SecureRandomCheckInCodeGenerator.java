package cn.genn.pf.orch.meeting.infrastructure.utils;

import java.security.SecureRandom;
import java.util.List;

/**
 * <AUTHOR>
 * @description 签到码随机数生成器
 * @date 2025-01-02
 */
public class SecureRandomCheckInCodeGenerator {

    private static final SecureRandom secureRandom = new SecureRandom();
    private static final long MIN_CODE = 1_000_0000L; // 最小8位数
    private static final long MAX_CODE = 9_999_9999L; // 最大8位数

    public static String generate(List<String> checkInNumbers) {
        String code;
        do {
            // 生成一个在MIN_CODE和MAX_CODE之间的随机数
            long randomNumber = secureRandom.nextInt((int) (MAX_CODE - MIN_CODE + 1)) + MIN_CODE;
            // 格式化为8位数字字符串
            code = String.format("%08d", randomNumber);
        } while (checkInNumbers.contains(code)); // 如果生成的签到码已存在于列表中，则重新生成

        return code;
    }
}
