package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.pf.orch.meeting.application.service.action.ScheduleActionService;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.interfaces.command.DeleteScheduleCommand;
import cn.genn.pf.orch.meeting.interfaces.command.ScheduleConfigCommand;
import cn.genn.pf.orch.meeting.interfaces.command.ScheduleCommand;
import cn.genn.pf.orch.meeting.interfaces.command.UpdateScheduleCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleConfigDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.pf.orch.meeting.interfaces.query.ScheduleConfigQuery;
import cn.genn.pf.orch.meeting.interfaces.query.ScheduleQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 日程会议
 *
 * <AUTHOR>
 */
@Api(tags = "日程会议")
@RestController
@RequestMapping("/schedule")
@RequiredArgsConstructor
public class ScheduleController {

    private final ScheduleQueryService queryService;
    private final ScheduleActionService actionService;

    @PostMapping("/config/add")
    @ApiOperation(value = "日程会议配置创建")
    public void addConfig(@Validated @RequestBody ScheduleConfigCommand command) {
        actionService.addConfig(command);
    }

    @PostMapping("/config/update")
    @ApiOperation(value = "日程会议配置修改")
    public void updateConfig(@Validated @RequestBody ScheduleConfigCommand command) {
        actionService.updateConfig(command);
    }

    @PostMapping("/config/get")
    @ApiOperation(value = "日程会议配置获取")
    public ScheduleConfigDTO getConfig(@Validated @RequestBody ScheduleConfigQuery query) {
        return queryService.getConfig(query);
    }

    @PostMapping("/add")
    @ApiOperation(value = "日程创建")
    public void add(@Validated @RequestBody ScheduleCommand command) {
        actionService.add(command);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "日程删除")
    public void delete(@Validated @RequestBody DeleteScheduleCommand command) {
        actionService.delete(command.getScheduleId());
    }

    @PostMapping("/update")
    @ApiOperation(value = "日程更新")
    public void update(@Validated @RequestBody UpdateScheduleCommand command) {
        actionService.update(command);
    }

    @PostMapping("/get")
    @ApiOperation(value = "日程获取")
    public ScheduleDTO get(@Validated @RequestBody ScheduleQuery query) {
        return queryService.getByScheduleId(query.getScheduleId());
    }

    @PostMapping("/subscription")
    @ApiOperation(value = "日历订阅")
    public void subscription() {
        actionService.subscription();
    }
}
