package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.pf.orch.meeting.application.dto.MeetingEvaluationSubmitCommand;
import cn.genn.pf.orch.meeting.application.dto.card.SendMeetingEvaluationSurveyDTO;
import cn.genn.pf.orch.meeting.application.service.action.CardSendActionService;
import cn.genn.pf.orch.meeting.application.service.action.MeetingEvaluationActionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议评价测试控制器
 * @date 2025-01-24
 */
@Slf4j
@Api(tags = "会议评价测试")
@RestController
@RequestMapping("/test/evaluation")
public class MeetingEvaluationController {

    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private MeetingEvaluationActionService evaluationActionService;

    @PostMapping("/sendCard")
    @ApiOperation(value = "测试发送会议评价卡片")
    public void testSendEvaluationCard(@RequestBody SendMeetingEvaluationSurveyDTO dto) {
        log.info("测试发送会议评价卡片，会议ID：{}，接收人：{}", dto.getMeetingId(), dto.getOpenId());
        cardSendActionService.sendMeetingEvaluationSurvey(dto);
    }

    @PostMapping("/submitEvaluation")
    @ApiOperation(value = "测试提交会议评价")
    public void testSubmitEvaluation(@RequestBody MeetingEvaluationSubmitCommand command) {
        log.info("测试提交会议评价，会议ID：{}，评价人：{}", command.getMeetingId(), command.getEvaluatorOpenId());
        evaluationActionService.submitEvaluation(command);
    }
} 