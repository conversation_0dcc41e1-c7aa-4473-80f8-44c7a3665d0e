package cn.genn.pf.orch.meeting.application.service.query;

import cn.genn.pf.orch.meeting.infrastructure.config.CurrentUserHolder;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TeamInfoMapper;
import cn.genn.pf.orch.meeting.interfaces.dto.AuthConfigDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SsoQueryService {

    @Resource
    private TeamInfoMapper teamInfoMapper;
    public AuthConfigDTO getAuthConfig(String openId) {
        boolean hasCreateSchedule = teamInfoMapper.hasCreateSchedule(openId);
        return AuthConfigDTO.builder()
            .hasCreateSchedule(hasCreateSchedule).build();

    }
}
