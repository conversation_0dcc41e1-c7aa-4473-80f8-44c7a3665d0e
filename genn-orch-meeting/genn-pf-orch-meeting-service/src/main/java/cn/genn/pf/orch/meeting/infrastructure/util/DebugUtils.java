package cn.genn.pf.orch.meeting.infrastructure.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Assistant
 * @description 调试工具类
 */
@Slf4j
public class DebugUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 打印对象的JSON格式
     */
    public static void printJson(String label, Object obj) {
        try {
            String json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
            log.info("=== {} ===\n{}", label, json);
        } catch (Exception e) {
            log.error("打印JSON失败：{}", e.getMessage());
            log.info("=== {} === {}", label, obj);
        }
    }

    /**
     * 检查对象是否为空或字段为空
     */
    public static void checkNullFields(String label, Object obj) {
        log.info("=== 检查 {} ===", label);
        if (obj == null) {
            log.warn("{} 对象为 null", label);
            return;
        }
        
        try {
            // 使用反射检查字段
            java.lang.reflect.Field[] fields = obj.getClass().getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value == null) {
                    log.warn("{} 的字段 {} 为 null", label, field.getName());
                } else if (value instanceof String && ((String) value).isEmpty()) {
                    log.warn("{} 的字段 {} 为空字符串", label, field.getName());
                } else {
                    log.info("{} 的字段 {} = {}", label, field.getName(), value);
                }
            }
        } catch (Exception e) {
            log.error("检查字段失败：{}", e.getMessage());
        }
    }
}
