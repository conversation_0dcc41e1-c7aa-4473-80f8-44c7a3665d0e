package cn.genn.pf.orch.meeting.application.service.query.impl;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.application.service.query.TaskTrackingQueryService;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TaskMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TaskPO;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskListDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskTrackingStatisticsDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.TaskStatusEnum;
import cn.genn.pf.orch.meeting.interfaces.query.TaskListQuery;
import cn.genn.pf.orch.meeting.interfaces.query.TaskStatisticsQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 任务追踪查询服务实现类
 * @date 2025-01-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskTrackingQueryServiceImpl implements TaskTrackingQueryService {

    private final TaskMapper taskMapper;

    @Override
    public PageResultDTO<TaskListDTO> getTaskList(TaskListQuery query) {
        try {
            Page<TaskPO> page = new Page<>(query.getPageNo(), query.getPageSize());

            LambdaQueryWrapper<TaskPO> wrapper = new LambdaQueryWrapper<>();

            // 逻辑删除条件：只查询未删除的数据
            wrapper.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);

            // 必填条件：状态
            wrapper.eq(TaskPO::getStatus, query.getStatus());

            // 可选条件：用户ID（当userId不为空时才添加过滤条件）
            if (query.getUserId() != null && !query.getUserId().trim().isEmpty()) {
                wrapper.eq(TaskPO::getCreateUserId, query.getUserId());
            }

            // 按创建时间倒序
            wrapper.orderByDesc(TaskPO::getCreateTime);

            IPage<TaskPO> result = taskMapper.selectPage(page, wrapper);

            List<TaskListDTO> records = result.getRecords().stream()
                    .map(this::convertToTaskListDTO)
                    .collect(Collectors.toList());

            return new PageResultDTO<>(query.getPageNo(), query.getPageSize(), result.getTotal(), records);
        } catch (Exception e) {
            log.error("获取任务列表失败, query: {}", query, e);
            throw new RuntimeException("获取任务列表失败", e);
        }
    }

    @Override
    public TaskTrackingStatisticsDTO getTaskStatistics(TaskStatisticsQuery query) {
        try {
            LambdaQueryWrapper<TaskPO> wrapper = new LambdaQueryWrapper<>();

            // 逻辑删除条件：只查询未删除的数据
            wrapper.eq(TaskPO::getDeleted, DeletedEnum.NOT_DELETED);

            // 可选条件：用户ID（当userId不为空时才添加过滤条件）
            if (query.getUserId() != null && !query.getUserId().trim().isEmpty()) {
                wrapper.eq(TaskPO::getCreateUserId, query.getUserId());
            }

            // 查询所有任务
            List<TaskPO> allTasks = taskMapper.selectList(wrapper);

            // 统计各状态任务数量
            int totalCount = allTasks.size();
            int notStartedCount = (int) allTasks.stream().filter(task -> TaskStatusEnum.NOT_STARTED.equals(task.getStatus())).count();
            int inProgressCount = (int) allTasks.stream().filter(task -> TaskStatusEnum.IN_PROGRESS.equals(task.getStatus())).count();
            int completedCount = (int) allTasks.stream().filter(task -> TaskStatusEnum.COMPLETED.equals(task.getStatus())).count();
            int overdueCount = (int) allTasks.stream().filter(task -> TaskStatusEnum.OVERDUE.equals(task.getStatus())).count();

            return TaskTrackingStatisticsDTO.builder()
                    .notStartedCount(notStartedCount)
                    .inProgressCount(inProgressCount)
                    .completedCount(completedCount)
                    .overdueCount(overdueCount)
                    .totalCount(totalCount)
                    .build();
        } catch (Exception e) {
            log.error("获取任务统计数据失败, userId: {}", query.getUserId(), e);
            throw new RuntimeException("获取任务统计数据失败", e);
        }
    }

    /**
     * 转换为TaskListDTO
     */
    private TaskListDTO convertToTaskListDTO(TaskPO taskPO) {
        return TaskListDTO.builder()
                .id(taskPO.getId())
                .feishuTaskId(taskPO.getFeishuTaskId())
                .title(taskPO.getTitle())
                .description(taskPO.getDescription())
                .ownerOpenId(taskPO.getOwnerOpenId())
                .ownerName(taskPO.getOwnerName())
                .priority(taskPO.getPriority())
                .status(taskPO.getStatus())
                .dueDate(taskPO.getDueDate())
                .completedAt(taskPO.getCompletedAt())
                .meetingId(taskPO.getMeetingId())
                .deleted(taskPO.getDeleted())
                .createTime(taskPO.getCreateTime())
                .createUserId(taskPO.getCreateUserId())
                .createUserName(taskPO.getCreateUserName())
                .updateTime(taskPO.getUpdateTime())
                .updateUserId(taskPO.getUpdateUserId())
                .updateUserName(taskPO.getUpdateUserName())
                .ownerAvatarUrl(null) // 暂时设为null，如需要可后续扩展
                .isOverdue(isTaskOverdue(taskPO)) // 计算是否超期
                .build();
    }

    /**
     * 判断任务是否超期
     */
    private Boolean isTaskOverdue(TaskPO taskPO) {
        if (taskPO.getDueDate() == null) {
            return false;
        }
        return java.time.LocalDateTime.now().isAfter(taskPO.getDueDate()) &&
               !TaskStatusEnum.COMPLETED.equals(taskPO.getStatus());
    }

}
