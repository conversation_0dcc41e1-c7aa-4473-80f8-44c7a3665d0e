package cn.genn.pf.orch.meeting.application.assembler;

import cn.genn.pf.orch.meeting.application.service.query.UserInfoQueryService;
import cn.genn.pf.orch.meeting.domain.task.model.aggregates.TaskAgg;
import cn.genn.pf.orch.meeting.domain.task.model.entity.TaskInfo;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskListDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务组装器
 */
@Slf4j
@Component
public class TaskAssembler {

    @Resource
    private UserInfoQueryService userInfoQueryService;

    /**
     * TaskInfo转TaskDTO
     *
     * @param taskInfo 任务信息
     * @return TaskDTO
     */
    public TaskDTO toTaskDTO(TaskInfo taskInfo) {
        if (taskInfo == null) {
            return null;
        }

        TaskDTO taskDTO = new TaskDTO();
        BeanUtils.copyProperties(taskInfo, taskDTO);

        // 获取负责人头像
        if (StrUtil.isNotBlank(taskInfo.getOwnerOpenId())) {
            String avatarUrl = userInfoQueryService.getUserAvatarUrl(taskInfo.getOwnerOpenId());
            taskDTO.setOwnerAvatarUrl(avatarUrl);
        }

        return taskDTO;
    }

    /**
     * TaskInfo转TaskListDTO
     *
     * @param taskInfo 任务信息
     * @return TaskListDTO
     */
    public TaskListDTO toTaskListDTO(TaskInfo taskInfo) {
        if (taskInfo == null) {
            return null;
        }

        TaskListDTO taskListDTO = new TaskListDTO();
        BeanUtils.copyProperties(taskInfo, taskListDTO);

        // 设置是否超期
        taskListDTO.setIsOverdue(isOverdue(taskInfo));

        // 获取负责人头像
        if (StrUtil.isNotBlank(taskInfo.getOwnerOpenId())) {
            String avatarUrl = userInfoQueryService.getUserAvatarUrl(taskInfo.getOwnerOpenId());
            taskListDTO.setOwnerAvatarUrl(avatarUrl);
        }

        return taskListDTO;
    }

    /**
     * TaskInfo列表转TaskDTO列表
     *
     * @param taskInfoList 任务信息列表
     * @return TaskDTO列表
     */
    public List<TaskDTO> toTaskDTOList(List<TaskInfo> taskInfoList) {
        if (CollUtil.isEmpty(taskInfoList)) {
            return Collections.emptyList();
        }

        // 批量获取用户头像
        List<String> ownerOpenIds = taskInfoList.stream()
                .map(TaskInfo::getOwnerOpenId)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> avatarUrlMap = userInfoQueryService.getUserAvatarUrls(ownerOpenIds);

        return taskInfoList.stream()
                .map(taskInfo -> {
                    TaskDTO taskDTO = new TaskDTO();
                    BeanUtils.copyProperties(taskInfo, taskDTO);

                    // 设置头像
                    if (StrUtil.isNotBlank(taskInfo.getOwnerOpenId())) {
                        taskDTO.setOwnerAvatarUrl(avatarUrlMap.get(taskInfo.getOwnerOpenId()));
                    }

                    return taskDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * TaskInfo列表转TaskListDTO列表
     *
     * @param taskInfoList 任务信息列表
     * @return TaskListDTO列表
     */
    public List<TaskListDTO> toTaskListDTOList(List<TaskInfo> taskInfoList) {
        if (CollUtil.isEmpty(taskInfoList)) {
            return Collections.emptyList();
        }

        // 批量获取用户头像
        List<String> ownerOpenIds = taskInfoList.stream()
                .map(TaskInfo::getOwnerOpenId)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        Map<String, String> avatarUrlMap = userInfoQueryService.getUserAvatarUrls(ownerOpenIds);

        return taskInfoList.stream()
                .map(taskInfo -> {
                    TaskListDTO taskListDTO = new TaskListDTO();
                    BeanUtils.copyProperties(taskInfo, taskListDTO);

                    // 设置是否超期
                    taskListDTO.setIsOverdue(isOverdue(taskInfo));

                    // 设置头像
                    if (StrUtil.isNotBlank(taskInfo.getOwnerOpenId())) {
                        taskListDTO.setOwnerAvatarUrl(avatarUrlMap.get(taskInfo.getOwnerOpenId()));
                    }

                    return taskListDTO;
                })
                .collect(Collectors.toList());
    }

    /**
     * TaskAgg转TaskDTO
     *
     * @param taskAgg 任务聚合
     * @return TaskDTO
     */
    public TaskDTO toTaskDTO(TaskAgg taskAgg) {
        if (taskAgg == null || taskAgg.getInfo() == null) {
            return null;
        }
        return toTaskDTO(taskAgg.getInfo());
    }

    /**
     * TaskAgg转TaskListDTO
     *
     * @param taskAgg 任务聚合
     * @return TaskListDTO
     */
    public TaskListDTO toTaskListDTO(TaskAgg taskAgg) {
        if (taskAgg == null || taskAgg.getInfo() == null) {
            return null;
        }
        return toTaskListDTO(taskAgg.getInfo());
    }

    /**
     * TaskAgg列表转TaskDTO列表
     *
     * @param taskAggList 任务聚合列表
     * @return TaskDTO列表
     */
    public List<TaskDTO> toTaskDTOListFromAgg(List<TaskAgg> taskAggList) {
        if (CollUtil.isEmpty(taskAggList)) {
            return Collections.emptyList();
        }

        List<TaskInfo> taskInfoList = taskAggList.stream()
                .map(TaskAgg::getInfo)
                .filter(info -> info != null)
                .collect(Collectors.toList());

        return toTaskDTOList(taskInfoList);
    }

    /**
     * TaskAgg列表转TaskListDTO列表
     *
     * @param taskAggList 任务聚合列表
     * @return TaskListDTO列表
     */
    public List<TaskListDTO> toTaskListDTOListFromAgg(List<TaskAgg> taskAggList) {
        if (CollUtil.isEmpty(taskAggList)) {
            return Collections.emptyList();
        }

        List<TaskInfo> taskInfoList = taskAggList.stream()
                .map(TaskAgg::getInfo)
                .filter(info -> info != null)
                .collect(Collectors.toList());

        return toTaskListDTOList(taskInfoList);
    }

    /**
     * 判断任务是否超期
     *
     * @param taskInfo 任务信息
     * @return 是否超期
     */
    private Boolean isOverdue(TaskInfo taskInfo) {
        if (taskInfo.getDueDate() == null) {
            return false;
        }
        return LocalDateTime.now().isAfter(taskInfo.getDueDate()) &&
               taskInfo.getStatus() != cn.genn.pf.orch.meeting.interfaces.enums.TaskStatusEnum.COMPLETED;
    }
}
