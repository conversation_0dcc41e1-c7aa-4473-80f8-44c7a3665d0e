package cn.genn.pf.orch.meeting.infrastructure.repository.mapper;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamInfoPO;
import cn.genn.pf.orch.meeting.interfaces.query.team.TeamPageQuery;
import cn.genn.pf.orch.meeting.interfaces.query.team.TeamQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TeamInfoMapper extends BaseMapper<TeamInfoPO> {

    default List<TeamInfoPO> selectByLevelId(Long levelId) {
        LambdaQueryWrapper<TeamInfoPO> wrapper = Wrappers.lambdaQuery(TeamInfoPO.class)
            .eq(TeamInfoPO::getLevelId, levelId)
            .eq(TeamInfoPO::getDeleted, DeletedEnum.NOT_DELETED);
        return selectList(wrapper);
    }

    default List<TeamInfoPO> selectByLeader(String openId) {
        LambdaQueryWrapper<TeamInfoPO> wrapper = Wrappers.lambdaQuery(TeamInfoPO.class)
            .and(w -> w.eq(TeamInfoPO::getLeader, openId)
                .or()
                .like(TeamInfoPO::getTechnicians, openId))
            .eq(TeamInfoPO::getDeleted, DeletedEnum.NOT_DELETED);
        return selectList(wrapper);
    }

    Page<TeamInfoPO> TeamPage(Page<TeamInfoPO> page, @Param("query") TeamPageQuery query);

    default List<TeamInfoPO> queryTeamList(TeamQuery query) {
        LambdaQueryWrapper<TeamInfoPO> wrapper = Wrappers.lambdaQuery(TeamInfoPO.class)
            .eq(TeamInfoPO::getLevelId, query.getLevelId())
            .eq(TeamInfoPO::getGroupName, query.getGroupName())
            .eq(TeamInfoPO::getDeleted, DeletedEnum.NOT_DELETED);
        return selectList(wrapper);
    }

    boolean hasCreateSchedule(@Param("openId") String openId);

    TeamInfoPO selectByScheduleId(@Param("scheduleId") Long scheduleId);
}
