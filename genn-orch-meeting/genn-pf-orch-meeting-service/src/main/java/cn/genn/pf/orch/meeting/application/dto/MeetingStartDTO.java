package cn.genn.pf.orch.meeting.application.dto;

import cn.genn.pf.orch.meeting.interfaces.command.MeetingStartEndEventCommand;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议开始结束对象
 * @date 2024-12-31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingStartDTO {

    private LocalDateTime startTime;

    private String meetingNo;

    private String fsMeetingId;
}
