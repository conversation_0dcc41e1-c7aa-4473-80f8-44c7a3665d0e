package cn.genn.pf.orch.meeting.domain.task.model.aggregates;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.pf.orch.meeting.domain.task.model.entity.TaskInfo;
import cn.genn.pf.orch.meeting.infrastructure.config.CurrentUserHolder;
import cn.genn.pf.orch.meeting.interfaces.enums.TaskPriorityEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.TaskStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务聚合根
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskAgg {

    /**
     * 任务信息
     */
    private TaskInfo info;

    /**
     * 创建任务聚合
     */
    public static TaskAgg create(String title, String description, String ownerOpenId,
                                String ownerName, TaskPriorityEnum priority,
                                LocalDateTime dueDate, Long meetingId) {
        // 获取当前登录用户信息
        String currentUserOpenId = getCurrentUserOpenIdSafely();
        String currentUserName = getCurrentUserNameSafely();

        return create(title, description, ownerOpenId, ownerName, priority, dueDate, meetingId,
                     currentUserOpenId, currentUserName);
    }

    /**
     * 创建任务聚合（指定操作用户）
     */
    public static TaskAgg create(String title, String description, String ownerOpenId,
                                String ownerName, TaskPriorityEnum priority,
                                LocalDateTime dueDate, Long meetingId,
                                String operatorOpenId, String operatorName) {
        TaskInfo taskInfo = TaskInfo.builder()
                .title(title)
                .description(description)
                .ownerOpenId(ownerOpenId)
                .ownerName(ownerName)
                .priority(priority != null ? priority : TaskPriorityEnum.MEDIUM)
                .status(TaskStatusEnum.NOT_STARTED)
                .dueDate(dueDate)
                .meetingId(meetingId)
                .deleted(DeletedEnum.NOT_DELETED)
                .createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now())
                // 设置创建人和修改人
                .createUserId(operatorOpenId)
                .createUserName(operatorName)
                .updateUserId(operatorOpenId)
                .updateUserName(operatorName)
                .build();

        return TaskAgg.builder()
                .info(taskInfo)
                .build();
    }

    /**
     * 更新任务信息
     */
    public void update(String title, String description, String ownerOpenId,
                      String ownerName, TaskPriorityEnum priority,
                      LocalDateTime dueDate, Long meetingId) {
        // 获取当前登录用户信息
        String currentUserOpenId = getCurrentUserOpenIdSafely();
        String currentUserName = getCurrentUserNameSafely();

        update(title, description, ownerOpenId, ownerName, priority, dueDate, meetingId,
               currentUserOpenId, currentUserName);
    }

    /**
     * 更新任务信息（指定操作用户）
     */
    public void update(String title, String description, String ownerOpenId,
                      String ownerName, TaskPriorityEnum priority,
                      LocalDateTime dueDate, Long meetingId,
                      String operatorOpenId, String operatorName) {
        this.info.setTitle(title);
        this.info.setDescription(description);
        this.info.setOwnerOpenId(ownerOpenId);
        this.info.setOwnerName(ownerName);
        this.info.setPriority(priority);
        this.info.setDueDate(dueDate);
        this.info.setMeetingId(meetingId);
        this.info.setUpdateTime(LocalDateTime.now());
        // 设置修改人
        this.info.setUpdateUserId(operatorOpenId);
        this.info.setUpdateUserName(operatorName);
    }

    /**
     * 完成任务
     */
    public void complete() {
        // 获取当前登录用户信息
        String currentUserOpenId = getCurrentUserOpenIdSafely();
        String currentUserName = getCurrentUserNameSafely();

        complete(currentUserOpenId, currentUserName);
    }

    /**
     * 完成任务（指定操作用户）
     */
    public void complete(String operatorOpenId, String operatorName) {
        this.info.complete();
        this.info.setUpdateTime(LocalDateTime.now());
        // 设置修改人
        this.info.setUpdateUserId(operatorOpenId);
        this.info.setUpdateUserName(operatorName);
    }

    /**
     * 开始任务
     */
    public void start() {
        // 获取当前登录用户信息
        String currentUserOpenId = getCurrentUserOpenIdSafely();
        String currentUserName = getCurrentUserNameSafely();

        start(currentUserOpenId, currentUserName);
    }

    /**
     * 开始任务（指定操作用户）
     */
    public void start(String operatorOpenId, String operatorName) {
        this.info.start();
        this.info.setUpdateTime(LocalDateTime.now());
        // 设置修改人
        this.info.setUpdateUserId(operatorOpenId);
        this.info.setUpdateUserName(operatorName);
    }

    /**
     * 标记为超期
     */
    public void markOverdue() {
        this.info.markOverdue();
        this.info.setUpdateTime(LocalDateTime.now());
    }

    /**
     * 检查是否超期
     */
    public boolean isOverdue() {
        return this.info.isOverdue();
    }

    /**
     * 更新飞书任务ID
     */
    public void updateFeishuTaskId(String feishuTaskId) {
        // 获取当前登录用户信息
        String currentUserOpenId = getCurrentUserOpenIdSafely();
        String currentUserName = getCurrentUserNameSafely();

        updateFeishuTaskId(feishuTaskId, currentUserOpenId, currentUserName);
    }

    /**
     * 更新飞书任务ID（指定操作用户）
     */
    public void updateFeishuTaskId(String feishuTaskId, String operatorOpenId, String operatorName) {
        this.info.setFeishuTaskId(feishuTaskId);
        this.info.setUpdateTime(LocalDateTime.now());
        // 设置修改人
        this.info.setUpdateUserId(operatorOpenId);
        this.info.setUpdateUserName(operatorName);
    }

    /**
     * 删除任务
     */
    public void delete() {
        this.info.setDeleted(DeletedEnum.DELETED);
    }

    /**
     * 安全获取当前用户OpenID
     * 在没有登录态时返回系统默认值
     */
    private static String getCurrentUserOpenIdSafely() {
        try {
            return CurrentUserHolder.getCurrentUser().getOpenId();
        } catch (Exception e) {
            // 飞书回调等场景下没有登录态，使用系统默认值
            return "system";
        }
    }

    /**
     * 安全获取当前用户姓名
     * 在没有登录态时返回系统默认值
     */
    private static String getCurrentUserNameSafely() {
        try {
            return CurrentUserHolder.getCurrentUser().getName();
        } catch (Exception e) {
            // 飞书回调等场景下没有登录态，使用系统默认值
            return "系统";
        }
    }
}
