package cn.genn.pf.orch.meeting.infrastructure.properties;

import lombok.Data;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class SsoAuthProperties {

    private boolean enable = true;
    //权限校验默认白名单路径
    private List<String> defaultUris = new ArrayList<>(Arrays.asList("/sso/**", "/health/**", "/white/**"));
    private List<String> excludePatterns = new ArrayList<>();
}
