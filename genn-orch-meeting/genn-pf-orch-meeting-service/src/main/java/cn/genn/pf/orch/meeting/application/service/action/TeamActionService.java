package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.pf.orch.meeting.application.assembler.TeamAssembler;
import cn.genn.pf.orch.meeting.application.processor.TeamProcessor;
import cn.genn.pf.orch.meeting.domain.team.service.TeamDomainService;
import cn.genn.pf.orch.meeting.interfaces.command.TeamInfoCommand;
import cn.genn.pf.orch.meeting.interfaces.command.TeamLevelCommand;
import io.swagger.annotations.ApiOperation;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

@Service
public class TeamActionService {

    @Resource
    private TeamDomainService teamDomainService;
    @Resource
    private TeamAssembler teamAssembler;
    @Resource
    private TeamProcessor teamProcessor;

    public Boolean saveOrUpdateLevel(TeamLevelCommand command) {
        return teamDomainService.saveOrUpdateLevel(teamAssembler.toTeamLevel(command));
    }

    public Boolean removeLevel(Long id){
        teamProcessor.checkDeleteLevel(id);
        return teamDomainService.removeLevel(id);
    }

    public Boolean saveOrUpdate(TeamInfoCommand command) {
        return teamDomainService.saveOrUpdate(teamAssembler.toTeamInfo(command));
    }

    public Boolean remove(Long id) {
        teamProcessor.checkDeleteInfo(id);
        return teamDomainService.removeInfo(id);
    }
}
