package cn.genn.pf.orch.meeting.application.dto;

import cn.genn.pf.orch.meeting.domain.schedule.model.entity.Schedule;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议创建对象
 * @date 2024-12-31
 */
@Data
@Builder
public class MeetingCreateDTO {

    /**
     * 日程
     */
    private ScheduleDTO schedule;

    /**
     * 已存在的签到码
     */
    private List<String> existCheckInNumbers;
}
