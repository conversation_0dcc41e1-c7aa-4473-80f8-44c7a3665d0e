package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.pf.orch.meeting.application.service.action.SsoActionService;
import cn.genn.pf.orch.meeting.application.service.query.SsoQueryService;
import cn.genn.pf.orch.meeting.infrastructure.config.CurrentUserHolder;
import cn.genn.pf.orch.meeting.interfaces.command.LogoutToolCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.user.UserInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.user.UserTokenDTO;
import cn.genn.pf.orch.meeting.interfaces.query.JsTicketQuery;
import cn.genn.pf.orch.meeting.interfaces.query.SsoUserLoginQuery;
import cn.genn.pf.orch.meeting.interfaces.query.SsoUserTokenQuery;
import cn.genn.third.feishu.app.FeishuAppClient;
import cn.genn.third.feishu.app.model.SignatureModel;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "免登相关")
@RestController
@RequestMapping("/sso/web")
public class WebSsoController {

    @Resource
    private SsoActionService ssoActionService;
    @Resource
    private SsoQueryService ssoQueryService;
    @Resource
    private FeishuAppClient feishuAppClient;

    @GetMapping("/getCode")
    @ApiOperation("获取授权码-测试接口")
    public String getCode(@RequestParam("code") String code){
        log.info("授权码:{}",code);
        return code;
    }

    @PostMapping("/getUserToken")
    @ApiOperation("免登过程-获取token")
    public UserTokenDTO getUserToken(@Validated @RequestBody SsoUserLoginQuery query) {
        return ssoActionService.getUserToken(query);
    }

    @PostMapping("/getJsTicket")
    @ApiOperation("获取js_sdk_ticket")
    public SignatureModel getJsTicket(@Validated @RequestBody JsTicketQuery query) {
        return feishuAppClient.getAuthService().generateSignature(query.getUrl());
    }

    @PostMapping("/getUserInfo")
    @ApiOperation("获取用户信息")
    public UserInfoDTO getUserInfo(@Validated @RequestBody SsoUserTokenQuery query) {
        String token = query.getToken();
        if (StrUtil.isBlank(token)) {
            query.setToken(CurrentUserHolder.getToken());
        }
        UserInfoDTO userInfo = ssoActionService.getUserInfo(query.getToken());
        userInfo.setAuthConfig(ssoQueryService.getAuthConfig(userInfo.getOpenId()));
        return userInfo;
    }

    @PostMapping("/refreshToken")
    @ApiOperation("刷新token")
    public Boolean refreshToken(@Validated @RequestBody SsoUserTokenQuery query) {
        return ssoActionService.refreshToken(query);
    }

    @PostMapping("/logout")
    @ApiOperation("登出")
    public Boolean logout(@Validated @RequestBody SsoUserTokenQuery query) {
        return ssoActionService.logout(query.getToken());
    }

    @PostMapping("/logoutTool")
    @ApiOperation("登出工具")
    public Boolean logoutTool(@RequestBody LogoutToolCommand command) {
        return ssoActionService.logoutTool(command);
    }

}


