package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * BusinessMeetingPO
 *
 * <AUTHOR>
 * @desc 业务会议层级表
 */
@Data
@Accessors(chain = true)
@TableName(value = "business_meeting_level", autoResultMap = true)
public class BusinessMeetingLevelPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 父业务会议id
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 子业务会议id
     */
    @TableField("son_id")
    private Long sonId;
}
