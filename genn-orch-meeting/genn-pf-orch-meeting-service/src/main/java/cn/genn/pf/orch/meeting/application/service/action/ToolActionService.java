package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.pf.orch.meeting.infrastructure.exception.MessageCode;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TeamLevelMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamLevelPO;
import cn.genn.pf.orch.meeting.interfaces.command.TeamNotifyUserCommand;
import cn.genn.third.feishu.app.FeishuAppClient;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserRespBody;
import com.lark.oapi.service.contact.v3.model.UserContactInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class ToolActionService {

    @Resource
    private TeamLevelMapper teamLevelMapper;
    @Resource
    private FeishuAppClient feishuAppClient;

    public void addTeamNotifyUser(TeamNotifyUserCommand command) {
        List<TeamLevelPO> teamLevelPOList = new ArrayList<>();
        if (CollUtil.isEmpty(command.getLevelIds())) {
            teamLevelPOList = teamLevelMapper.selectByLevel(2,null);
        } else {
            teamLevelPOList = teamLevelMapper.selectBatchIds(command.getLevelIds());
        }
        for (String telephone : command.getTelephone()) {
            // 手机号获取openId
            BatchGetIdUserRespBody userInfo = feishuAppClient.getContactService().getOpenId(telephone);
            List<UserContactInfo> list = Arrays.asList(userInfo.getUserList());
            String openId = list.get(0).getUserId();
            if (StrUtil.isBlank(openId)) {
                log.error("手机号:{}添加失败", telephone);
                throw new BusinessException(MessageCode.TELEPHONE_NO_EXIST);
            }
            for (TeamLevelPO teamLevelPO : teamLevelPOList) {
                if(teamLevelPO.getLevel()!=2){
                    continue;
                }
                if(!teamLevelPO.getFinishNotify().contains(openId)){
                    teamLevelPO.getFinishNotify().add(openId);
                    teamLevelMapper.updateById(teamLevelPO);
                }
            }
            log.info("手机号:{}添加成功", telephone);
        }
    }
}
