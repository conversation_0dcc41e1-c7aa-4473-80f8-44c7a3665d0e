package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import cn.genn.database.mybatisplus.typehandler.ListStringTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import cn.genn.core.model.enums.DeletedEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * TeamInfoPO对象
 *
 * <AUTHOR>
 * @desc 班组信息表
 */
@Data
@Accessors(chain = true)
@TableName(value = "team_info", autoResultMap = true)
public class TeamInfoPO {

    /**
     * 班组id
     */
    @TableId
    private Long id;

    /**
     * 队组名称
     */
    @TableField("group_name")
    private String groupName;

    /**
     * 班组名称
     */
    @TableField("name")
    private String name;

    /**
     * 班组层级id
     */
    @TableField("level_id")
    private Long levelId;

    /**
     * 班组长
     */
    @TableField("leader")
    private String leader;

    /**
     * 技术员
     */
    @TableField(value = "technicians",typeHandler = ListStringTypeHandler.class)
    private List<String> technicians = new ArrayList<>();

    /**
     * 班组成员
     */
    @TableField(value = "members",typeHandler = ListStringTypeHandler.class)
    private List<String> members = new ArrayList<>();

    /**
     * 评价人员
     */
    @TableField(value = "reviewers",typeHandler = ListStringTypeHandler.class)
    private List<String> reviewers = new ArrayList<>();

    /**
     * 删除标记（0未删除）
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建人
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 更新人id
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新人
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

