package cn.genn.pf.orch.meeting.domain.task.model.entity;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.TaskPriorityEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.TaskStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskInfo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 对应的飞书任务ID，用于API同步
     */
    private String feishuTaskId;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 负责人open_id
     */
    private String ownerOpenId;

    /**
     * 负责人名称
     */
    private String ownerName;

    /**
     * 优先级 (0: 低, 1: 中, 2: 高)
     */
    private TaskPriorityEnum priority;

    /**
     * 任务状态（0:未开始；1:进行中；2:已完成；3:已超期）
     */
    private TaskStatusEnum status;

    /**
     * 截止时间
     */
    private LocalDateTime dueDate;

    /**
     * 实际完成时间
     */
    private LocalDateTime completedAt;

    /**
     * 关联的会议ID (关联 meeting.id)
     */
    private Long meetingId;

    /**
     * 逻辑删除
     */
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建用户ID
     */
    private String createUserId;

    /**
     * 创建用户名
     */
    private String createUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新用户ID
     */
    private String updateUserId;

    /**
     * 更新用户名
     */
    private String updateUserName;

    /**
     * 完成任务
     */
    public void complete() {
        this.status = TaskStatusEnum.COMPLETED;
        this.completedAt = LocalDateTime.now();
    }

    /**
     * 开始任务
     */
    public void start() {
        this.status = TaskStatusEnum.IN_PROGRESS;
    }

    /**
     * 标记为超期
     */
    public void markOverdue() {
        this.status = TaskStatusEnum.OVERDUE;
    }

    /**
     * 检查是否超期
     */
    public boolean isOverdue() {
        return dueDate != null && LocalDateTime.now().isAfter(dueDate) &&
               status != TaskStatusEnum.COMPLETED;
    }
}
