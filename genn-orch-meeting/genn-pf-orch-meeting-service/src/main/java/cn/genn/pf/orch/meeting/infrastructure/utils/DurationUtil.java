package cn.genn.pf.orch.meeting.infrastructure.utils;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @description 耗时工具类
 * @date 2025-01-03
 */
public class DurationUtil {

    public static String calculateDuration(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null || start.isAfter(end)) {
            return StrUtil.EMPTY;
        }

        // 计算总秒数
        long totalSeconds = java.time.temporal.ChronoUnit.SECONDS.between(start, end);

        return formatDuration(totalSeconds);
    }

    public static String calculateDurationFromSeconds(long totalSeconds) {
        if (totalSeconds < 0) {
            return StrUtil.EMPTY; // 或者可以抛出异常，根据业务需求决定
        }

        return formatDuration(totalSeconds);
    }

    private static String formatDuration(long totalSeconds) {
        // 计算天数、小时、分钟和秒
        long days = totalSeconds / 86400;
        long hours = (totalSeconds % 86400) / 3600;
        long minutes = (totalSeconds % 3600) / 60;
        long seconds = totalSeconds % 60;

        // 格式化输出
        if (days > 0) {
            return String.format("%d天%02d:%02d:%02d", days, hours, minutes, seconds);
        } else {
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        }
    }
}
