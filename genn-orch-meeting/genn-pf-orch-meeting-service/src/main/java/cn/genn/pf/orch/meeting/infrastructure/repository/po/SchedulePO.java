package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * SchedulePO
 *
 * <AUTHOR>
 * @desc 日程表
 */
@Data
@Accessors(chain = true)
@TableName(value = "schedule", autoResultMap = true)
public class SchedulePO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 班组id
     */
    @TableField("team_id")
    private Long teamId;

    /**
     * 业务会议id
     */
    @TableField("business_meeting_id")
    private Long businessMeetingId;

    /**
     * 日程名称
     */
    @TableField("schedule_name")
    private String scheduleName;

    /**
     * 起始时间, 与全天互斥
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 中止时间, 与全天互斥
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 全天, 与起止时间互斥
     */
    @TableField("all_day")
    private LocalDateTime allDay;

    /**
     * 飞书日历Id
     */
    @TableField("fs_calendar_id")
    private String fsCalendarId;

    /**
     * 飞书日程Id
     */
    @TableField("fs_calendar_event_id")
    private String fsCalendarEventId;

    /**
     * 飞书会议Url
     */
    @TableField("fs_meeting_url")
    private String fsMeetingUrl;

    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新人名称
     */
    @TableField("update_user_id")
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 更新时间
     */
    @TableField("deleted")
    private DeletedEnum deleted;
}
