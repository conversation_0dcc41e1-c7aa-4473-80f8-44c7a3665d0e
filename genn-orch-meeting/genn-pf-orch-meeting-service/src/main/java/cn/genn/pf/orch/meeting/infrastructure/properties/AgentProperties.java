package cn.genn.pf.orch.meeting.infrastructure.properties;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 智能体应用配置
 * @date 2025-04-09
 */
@Data
public class AgentProperties {

    /**
     * 智能体调用域名
     */
    private String invokeDomain = "https://cerebro-sit.genn.cn";

    /**
     * 内部鉴权
     */
    private String authorization = "Bearer gennai-internal-api-key-4vQYhr7VPLTQyV9X4GRPQgmRrDg4Kb7qsdGP";



    private Integer maxRetries = 3;

    /**
     * 高频问题统计
     */
    private String highFreApiKey = "gennai-uIePn9MMUaptejVxEXrWVlnsPtyMdyIRYR1xqA5bxiVVDDxyHBRntYyt";

    /**
     * 知识缺失分析
     */
    private String gapAnlApiKey = "gennai-xqS9TnK5uy6EvXBcp7wXinUBQYVupJwtMvXHKJvjmYDz2bvaa9VLNX";
}
