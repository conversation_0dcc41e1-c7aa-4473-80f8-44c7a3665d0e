package cn.genn.pf.orch.meeting.infrastructure.repository.mapper;

import cn.genn.pf.orch.meeting.infrastructure.repository.po.NewMeetingPO;
import cn.genn.pf.orch.meeting.interfaces.command.newmeeting.NewMeetingQuery;
import cn.genn.pf.orch.meeting.interfaces.dto.newmeeting.NewMeetingListDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 新会议映射器
 * @date 2025-01-24
 */
public interface NewMeetingMapper extends BaseMapper<NewMeetingPO> {

    /**
     * 分页查询新会议列表
     */
    Page<NewMeetingListDTO> queryPage(Page<NewMeetingPO> page, @Param("query") NewMeetingQuery query);
}
