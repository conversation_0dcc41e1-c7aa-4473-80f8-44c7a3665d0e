package cn.genn.pf.orch.meeting.application.job;

import cn.genn.pf.orch.meeting.application.dto.MeetingEndDTO;
import cn.genn.pf.orch.meeting.application.service.query.MeetingQueryService;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.schedule.service.MeetingDomainService;
import cn.genn.pf.orch.meeting.infrastructure.constant.CacheConstants;
import cn.genn.pf.orch.meeting.infrastructure.constant.Constants;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.third.feishu.app.FeishuAppClient;
import cn.genn.third.feishu.app.model.meeting.GetMeetingModel;
import com.lark.oapi.service.vc.v1.model.GetMeetingRespBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * <AUTHOR>
 * @description 刷新会议是否结束任务
 * @date 2025-01-18
 */

@Slf4j
@Component
public class RefreshMeetingEndTask {

    @Resource
    private MeetingMapper meetingMapper;
    @Resource
    private MeetingDomainService meetingDomainService;
    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private ScheduleQueryService scheduleQueryService;

    @Scheduled(fixedRate = 1L * 60 * 1000) //每1分钟执行一次
    public void executeTask() {
        log.info("定时任务:刷新会议结束开始执行");
        List<MeetingPO> meetingPOS = meetingMapper.queryInProcessMeeting();
        meetingPOS.forEach(meetingPO -> {
            try {
                // 获取会议信息
                MeetingAgg meetingAgg = meetingDomainService.findById(meetingPO.getId());
                if (meetingAgg == null) {
                    return;
                }

                ScheduleDTO scheduleDTO = scheduleQueryService.getByScheduleId(meetingAgg.getInfo().getScheduleId());

                // 查询会议信息
                GetMeetingModel fsMeetingReq = GetMeetingModel.builder()
                    .meetingId(meetingAgg.getInfo().getFsMeetingId())
                    .withParticipants(true)
                    .userIdType(Constants.OPEN_ID)
                    .build();
                GetMeetingRespBody fsMeetingRes = feishuAppClient.getMeetingService().getMeeting(fsMeetingReq);
                // 非已结束忽略
                if (fsMeetingRes.getMeeting().getStatus() != 3) {
                    return;
                }

                log.info("定时任务:会议结束,meetingNo:{}", fsMeetingRes.getMeeting().getMeetingNo());
                // 将秒级时间戳转换为Instant
                Instant instant = Instant.ofEpochSecond(Long.parseLong(fsMeetingRes.getMeeting().getEndTime()));
                // 将Instant转换为LocalDateTime
                LocalDateTime endTime = LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Shanghai"));
                MeetingEndDTO meetingEndDTO = MeetingEndDTO.builder()
                    .meetingNo(fsMeetingRes.getMeeting().getMeetingNo())
                    .endTime(endTime)
                    .feishuMeeting(fsMeetingRes)
                    .schedule(scheduleDTO)
                    .build();

                meetingDomainService.end(meetingAgg, meetingEndDTO);
            } catch (Exception e) {
                log.error("定时任务:刷新会议({})结束异常: {}", meetingPO.getMeetingNo(), e.getMessage(), e);
            }
        });
    }
}
