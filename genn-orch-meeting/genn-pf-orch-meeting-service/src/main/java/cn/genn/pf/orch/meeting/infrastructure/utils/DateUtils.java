package cn.genn.pf.orch.meeting.infrastructure.utils;


import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

public class DateUtils {

    public static final String DATE_FORMAT = "MM月dd日 HH:mm:ss";

    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy.MM.dd");
    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    public static String formatLocalDateTime(LocalDateTime dateTime){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT, Locale.CHINA);
        return dateTime.format(formatter);
    }


    /**
     * 返回yyyy.MM.dd HH:mm:ss-HH:mm:ss格式
     * @param startTime
     * @param endTime
     * @return
     */
    public static String getTimeRange(LocalDateTime startTime, LocalDateTime endTime){
        if(startTime == null){
            return "";
        }
        StringBuilder sb = new StringBuilder();
        sb.append(startTime.format(DATE_FORMATTER)).append(" ").append(startTime.format(TIME_FORMATTER));
        if(endTime != null){
            sb.append("-").append(endTime.format(TIME_FORMATTER));
        }
        return sb.toString();

    }
}
