package cn.genn.pf.orch.meeting.application.service.query;

import cn.genn.pf.orch.meeting.application.assembler.ScheduleAssembler;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IScheduleRepository;
import cn.genn.pf.orch.meeting.domain.schedule.service.MeetingDomainService;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.*;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.AttendUserDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleConfigDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.query.ScheduleConfigQuery;
import cn.genn.third.feishu.app.FeishuAppClient;
import cn.hutool.core.collection.CollUtil;
import com.lark.oapi.service.contact.v3.model.AvatarInfo;
import com.lark.oapi.service.contact.v3.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 日程会议查询服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ScheduleQueryService {

    private final IScheduleRepository scheduleRepository;
    private final BusinessMeetingQueryService businessMeetingQueryService;
    private final TeamQueryService teamQueryService;
    private final ScheduleAssembler scheduleAssembler;
    private final FeishuAppClient feishuAppClient;
    private final MeetingDomainService meetingDomainService;

    public ScheduleConfigDTO getConfig(ScheduleConfigQuery query) {
        ScheduleConfigPO scheduleConfigPO = scheduleRepository.queryConfigByBusinessMeetingId(query.getBusinessMeetingId());
        return scheduleAssembler.toScheduleConfigDTO(scheduleConfigPO);
    }

    public List<ScheduleDTO> getByScheduleIds(List<Long> scheduleIds) {
        // 批量查 schedule
        List<SchedulePO> schedulePOS = scheduleRepository.queryScheduleByIds(scheduleIds);

        // 批量查 config
        List<Long> businessMeetingIds = schedulePOS.stream()
            .map(SchedulePO::getBusinessMeetingId)
            .collect(Collectors.toList());
        Map<Long, ScheduleConfigPO> scheduleConfigMap = scheduleRepository.queryConfigByBusinessMeetingIds(businessMeetingIds).stream()
            .collect(Collectors.toMap(ScheduleConfigPO::getBusinessMeetingId, Function.identity()));

        // 批量查 teamInfo
        List<Long> teamIds = schedulePOS.stream()
            .map(SchedulePO::getTeamId)
            .collect(Collectors.toList());
        Map<Long, TeamInfoDTO> teamInfoMap = teamQueryService.queryByIds(teamIds).stream()
            .collect(Collectors.toMap(TeamInfoDTO::getId, Function.identity()));

        // 批量查参与人
        List<ScheduleAttendUserPO> scheduleAttendUserPOS = scheduleRepository.queryUserByScheduleIds(scheduleIds);
        Map<Long, List<ScheduleAttendUserPO>> scheduleAttendUserMap = scheduleAttendUserPOS.stream()
            .collect(Collectors.groupingBy(ScheduleAttendUserPO::getScheduleId));

        List<String> attendUserIds = scheduleAttendUserPOS.stream()
            .map(ScheduleAttendUserPO::getAttendUserId)
            .distinct()
            .collect(Collectors.toList());
        Map<String, User> users = getUsers(attendUserIds);

        ArrayList<ScheduleDTO> result = new ArrayList<>();
        for (SchedulePO schedulePO : schedulePOS) {
            List<AttendUserDTO> attendUserDTOs = scheduleAttendUserMap.get(schedulePO.getId()).stream()
                .map(scheduleAssembler::toAttendUserDTO)
                .collect(Collectors.toList());
            attendUserDTOs.forEach(attendUser -> {
                User user = users.get(attendUser.getAttendUserId());
                attendUser.setAvatarUrl(Optional.of(user.getAvatar()).map(AvatarInfo::getAvatarOrigin).orElse(null));
            });

            result.add(ScheduleDTO.builder()
                .id(schedulePO.getId())
                .businessMeetingId(schedulePO.getBusinessMeetingId())
                .scheduleConfigDTO(scheduleAssembler.toScheduleConfigDTO(scheduleConfigMap.get(schedulePO.getBusinessMeetingId())))
                .scheduleName(schedulePO.getScheduleName())
                .startTime(schedulePO.getStartTime())
                .endTime(schedulePO.getEndTime())
                .allDay(schedulePO.getAllDay())
                .teamInfoDTO(teamInfoMap.get(schedulePO.getTeamId()))
                .attendUsers(attendUserDTOs)
                .createUserId(schedulePO.getCreateUserId())
                .fsMeetingUrl(schedulePO.getFsMeetingUrl())
                .build());
        }
        return result;
    }

    public ScheduleDTO getByScheduleId(Long scheduleId) {
        return getScheduleDTO(scheduleRepository.queryScheduleById(scheduleId));
    }

    public ScheduleDTO getByFSCalendarEventId(String fsCalendarEventId) {
        return getScheduleDTO(scheduleRepository.queryScheduleByFSCalendarEventId(fsCalendarEventId));
    }

    private ScheduleDTO getScheduleDTO(SchedulePO schedulePO) {
        if (schedulePO == null) {
            return null;
        }
        ScheduleConfigDTO config = getConfig(ScheduleConfigQuery.builder().businessMeetingId(schedulePO.getBusinessMeetingId()).build());
        List<AttendUserDTO> attendUsers = scheduleRepository.queryUserByScheduleId(schedulePO.getId()).stream()
            .map(scheduleAssembler::toAttendUserDTO)
            .collect(Collectors.toList());

        return ScheduleDTO.builder()
            .id(schedulePO.getId())
            .businessMeetingId(schedulePO.getBusinessMeetingId())
            .businessMeetingName(businessMeetingQueryService.get(schedulePO.getBusinessMeetingId()).getName())
            .scheduleConfigDTO(config)
            .scheduleName(schedulePO.getScheduleName())
            .startTime(schedulePO.getStartTime())
            .endTime(schedulePO.getEndTime())
            .allDay(schedulePO.getAllDay())
            .teamInfoDTO(teamQueryService.queryById(schedulePO.getTeamId()))
            .attendUsers(fillUserInfo(attendUsers))
            .createUserId(schedulePO.getCreateUserId())
            .fsMeetingUrl(schedulePO.getFsMeetingUrl())
            .build();
    }

    private Map<String, User> getUsers(List<String> userIds) {
        List<User> users = feishuAppClient.getContactService().userBatch(userIds);
        return users.stream().collect(Collectors.toMap(User::getOpenId, Function.identity(), (k1, k2) -> k1));
    }

    private List<AttendUserDTO> fillUserInfo(List<AttendUserDTO> attendUsers) {
        if (CollUtil.isEmpty(attendUsers)) {
            return attendUsers;
        }

        List<String> userIds = attendUsers.stream()
            .map(AttendUserDTO::getAttendUserId)
            .distinct()
            .collect(Collectors.toList());
        List<User> users = feishuAppClient.getContactService().userBatch(userIds);
        Map<String, User> userMap = users.stream().collect(Collectors.toMap(User::getOpenId, Function.identity()));

        attendUsers.forEach(attendUser -> {
            User user = userMap.get(attendUser.getAttendUserId());
            attendUser.setAvatarUrl(Optional.of(user.getAvatar()).map(AvatarInfo::getAvatarOrigin).orElse(null));
        });
        return attendUsers;
    }

    public ScheduleDTO getSnapshotScheduleDTO(Long scheduleId) {
        ScheduleDTO scheduleDTO = getByScheduleId(scheduleId);

        MeetingAgg meetingAgg = meetingDomainService.findByScheduleId(scheduleDTO.getId());
        if (meetingAgg.getExtend().getTeamInfo() != null) {
            scheduleDTO.setTeamInfoDTO(meetingAgg.getExtend().getTeamInfo());
        }
        return scheduleDTO;
    }
}
