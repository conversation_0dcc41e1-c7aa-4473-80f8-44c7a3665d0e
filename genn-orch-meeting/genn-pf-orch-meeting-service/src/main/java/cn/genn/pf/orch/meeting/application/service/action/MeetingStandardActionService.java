package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.pf.orch.meeting.application.assembler.MeetingStandardAssembler;
import cn.genn.pf.orch.meeting.domain.meetingstandard.model.entity.MeetingStandard;
import cn.genn.pf.orch.meeting.domain.meetingstandard.service.MeetingStandardDomainService;
import cn.genn.pf.orch.meeting.interfaces.command.meetingstandard.MeetingStandardCreateCommand;
import cn.genn.pf.orch.meeting.interfaces.command.meetingstandard.MeetingStandardUpdateCommand;
import cn.genn.pf.orch.meeting.interfaces.enums.PriorityLevelEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.TimeUnitEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 会议标准操作服务
 * @date 2025-01-24
 */
@Service
@RequiredArgsConstructor
public class MeetingStandardActionService {

    private final MeetingStandardDomainService meetingStandardDomainService;
    private final MeetingStandardAssembler meetingStandardAssembler;

    /**
     * 创建会议标准
     */
    public void createMeetingStandard(MeetingStandardCreateCommand command) {
        // 1. 设置默认值
        setDefaultValues(command);
        
        // 2. 转换为领域对象
        MeetingStandard meetingStandard = meetingStandardAssembler.toEntity(command);
        
        // 3. 调用领域服务
        meetingStandardDomainService.createMeetingStandard(meetingStandard);
    }

    /**
     * 更新会议标准
     */
    public void updateMeetingStandard(MeetingStandardUpdateCommand command) {
        // 1. 转换为领域对象
        MeetingStandard meetingStandard = meetingStandardAssembler.toEntity(command);
        
        // 2. 调用领域服务
        meetingStandardDomainService.updateMeetingStandard(meetingStandard);
    }

    /**
     * 删除会议标准
     */
    public void deleteMeetingStandard(Long id) {
        meetingStandardDomainService.deleteMeetingStandard(id);
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(MeetingStandardCreateCommand command) {
        // 设置默认时间单位为DAY
        if (command.getAdvanceNoticeUnit() == null) {
            command.setAdvanceNoticeUnit(TimeUnitEnum.DAY);
        }
        
        // 设置默认优先级为高
        if (command.getPriorityLevel() == null) {
            command.setPriorityLevel(PriorityLevelEnum.HIGH);
        }
    }
} 