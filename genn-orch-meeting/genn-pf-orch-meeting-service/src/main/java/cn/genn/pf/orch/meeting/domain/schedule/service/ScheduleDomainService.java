package cn.genn.pf.orch.meeting.domain.schedule.service;

import cn.genn.core.exception.BusinessException;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.AttendUser;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.Schedule;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.ScheduleConfig;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IScheduleRepository;
import cn.genn.pf.orch.meeting.infrastructure.exception.MessageCode;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.ScheduleConfigPO;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 日程领域服务
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ScheduleDomainService {

    private final IScheduleRepository scheduleRepository;

    public void addConfig(ScheduleConfig scheduleConfig) {
        ScheduleConfigPO scheduleConfigPO = scheduleRepository.queryConfigByBusinessMeetingId(scheduleConfig.getBusinessMeetingId());

        if (ObjectUtil.isNotNull(scheduleConfigPO)) {
            throw new BusinessException(MessageCode.BUSINESS_MEETING_CONFIG_ALREADY_EXISTS);
        }

        scheduleRepository.addConfig(scheduleConfig);
    }

    public void updateConfig(ScheduleConfig scheduleConfig) {
        scheduleRepository.updateConfig(scheduleConfig);
    }

    public void add(Schedule schedule) {
        scheduleRepository.add(schedule);
    }

    public void update(Schedule schedule, List<AttendUser> attendUsers) {
        scheduleRepository.update(schedule);
        scheduleRepository.updateAttendUsers(schedule.getId(), attendUsers);
    }

    public void addAttendUsers(Long scheduleId, List<AttendUser> addAttendUsers) {
        scheduleRepository.addAttendUsers(scheduleId, addAttendUsers);
    }

    public void removeAttendUsers(Long scheduleId, List<String> removeAttendUserIds) {
        scheduleRepository.removeAttendUsers(scheduleId, removeAttendUserIds);
    }

    public void delete(Long scheduleId) {
        scheduleRepository.delete(scheduleId);
    }
}
