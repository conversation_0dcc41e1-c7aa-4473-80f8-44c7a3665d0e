package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ScheduleAttendUserPO
 *
 * <AUTHOR>
 * @desc 日程参与人表
 */
@Data
@Accessors(chain = true)
@TableName(value = "schedule_attend_user", autoResultMap = true)
public class ScheduleAttendUserPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 日程id
     */
    @TableField("schedule_id")
    private Long scheduleId;

    /**
     * 参会人员角色, 0:CREATOR-创建人, 1:NORMAL-普通, 2:RECORDER-记录员 3:EVALUATOR-评价员
     */
    @TableField("attend_user_role")
    private AttendUserRoleEnum attendUserRole;

    /**
     * 参会人员状态, 1:EXPECT-预期参会, 2:ABSENT-请假缺席
     */
    @TableField("attend_user_state")
    private AttendUserStateEnum attendUserState;

    /**
     * 请假原因，仅当参会人员类别为ABSENT时有效
     */
    @TableField("absent_reason")
    private String absentReason;

    /**
     * 参会人id
     */
    @TableField("attend_user_id")
    private String attendUserId;

    /**
     * 参会人姓名
     */
    @TableField("attend_user_name")
    private String attendUserName;
}
