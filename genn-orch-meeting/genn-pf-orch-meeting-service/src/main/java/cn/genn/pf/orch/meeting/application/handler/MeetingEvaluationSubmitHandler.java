package cn.genn.pf.orch.meeting.application.handler;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.application.dto.MeetingEvaluationSubmitCommand;
import cn.genn.pf.orch.meeting.application.service.action.MeetingEvaluationActionService;
import cn.genn.pf.orch.meeting.infrastructure.enums.EventCallbackEnum;
import cn.genn.pf.orch.meeting.interfaces.command.MeetingEvaluationSubmitEventCommand;
import cn.genn.third.feishu.app.model.callback.EventCallbackCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议评价提交回调处理器
 * @date 2025-01-24
 */
@Slf4j
@Component
public class MeetingEvaluationSubmitHandler implements CallbackHandler<MeetingEvaluationSubmitEventCommand> {
    
    @Resource
    private MeetingEvaluationActionService evaluationActionService;
    
    @Override
    public void handle(EventCallbackCommand command) {
        String eventJson = JsonUtils.toJson(command.getEvent());
        MeetingEvaluationSubmitEventCommand submitCommand = JsonUtils.parse(eventJson, MeetingEvaluationSubmitEventCommand.class);
        
        log.info("收到会议评价提交回调，会议ID：{}，评价人：{}", 
            submitCommand.getMeetingId(), submitCommand.getEvaluatorOpenId());
        
        try {
            // 构建评价提交命令
            MeetingEvaluationSubmitCommand submitEvaluationCommand = MeetingEvaluationSubmitCommand.builder()
                .meetingId(submitCommand.getMeetingId())
                .evaluatorOpenId(submitCommand.getEvaluatorOpenId())
                .evaluatorName(submitCommand.getEvaluatorName())
                .evaluationDTO(cn.genn.pf.orch.meeting.application.dto.MeetingEvaluationDTO.builder()
                    .meetingScore(submitCommand.getMeetingScore())
                    .contentScore(submitCommand.getContentScore())
                    .durationScore(submitCommand.getDurationScore())
                    .effectivenessScore(submitCommand.getEffectivenessScore())
                    .suggestions(submitCommand.getSuggestions())
                    .build())
                .build();
            
            // 提交评价
            evaluationActionService.submitEvaluation(submitEvaluationCommand);
            
            log.info("会议评价提交处理成功，会议ID：{}，评价人：{}", 
                submitCommand.getMeetingId(), submitCommand.getEvaluatorOpenId());
        } catch (Exception e) {
            log.error("会议评价提交处理失败，会议ID：{}，评价人：{}", 
                submitCommand.getMeetingId(), submitCommand.getEvaluatorOpenId(), e);
        }
    }
    
    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.MEETING_EVALUATION_SUBMIT;
    }
} 