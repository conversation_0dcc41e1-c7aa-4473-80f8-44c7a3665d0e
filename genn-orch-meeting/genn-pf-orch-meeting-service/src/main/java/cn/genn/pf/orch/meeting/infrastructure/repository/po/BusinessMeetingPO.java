package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import cn.genn.pf.orch.meeting.interfaces.enums.TopBusinessEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * BusinessMeetingPO
 *
 * <AUTHOR>
 * @desc 业务会议表
 */
@Data
@Accessors(chain = true)
@TableName(value = "business_meeting", autoResultMap = true)
public class BusinessMeetingPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 业务名
     */
    @TableField("name")
    private String name;

    /**
     * 顶层业务类, 0:NOT_TOP_BUSINESS-不是顶层业务, 1:TOP_BUSINESS-是顶层业务
     */
    @TableField("top_business")
    private TopBusinessEnum topBusiness;

}
