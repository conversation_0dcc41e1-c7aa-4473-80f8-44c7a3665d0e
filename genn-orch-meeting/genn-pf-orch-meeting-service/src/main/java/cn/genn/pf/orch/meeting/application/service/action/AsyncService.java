package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.pf.orch.meeting.domain.schedule.model.entity.Schedule;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import cn.genn.third.feishu.app.FeishuAppClient;
import cn.genn.third.feishu.app.config.FeishuAppContext;
import cn.genn.third.feishu.app.model.AttendUserModel;
import cn.genn.third.feishu.app.model.CreateCalendarEventAttendeesModel;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 异步服务
 * @date 2025-01-18
 */
@Service
public class AsyncService {

    private final FeishuAppClient feishuAppClient;

    public AsyncService(FeishuAppClient feishuAppClient) {
        this.feishuAppClient = feishuAppClient;
    }

    @Async
    public void addFSCalendarEventAttendeesAsync(Schedule schedule, FeishuAppContext feishuAppContext) {
        // 异步上下文传递
        FeishuAppContext.set(feishuAppContext);
        List<AttendUserModel> attendUsers = schedule.getAttendUsers().stream()
            .filter(attendUser -> AttendUserStateEnum.EXPECT.equals(attendUser.getAttendUserState()))
            .map(attendUser -> AttendUserModel.builder()
                .userId(attendUser.getAttendUserId())
                .isOrganizer(schedule.getCreateUserId().equals(attendUser.getAttendUserId()))
                .build())
            .collect(Collectors.toList());

        feishuAppClient.addCalendarEventAttendees(CreateCalendarEventAttendeesModel.builder()
            .calendarId(schedule.getFsCalendarId())
            .eventId(schedule.getFsCalendarEventId())
            .attendUsers(attendUsers)
            .build());
        // 异步上下文清除
        FeishuAppContext.remove();
    }
}
