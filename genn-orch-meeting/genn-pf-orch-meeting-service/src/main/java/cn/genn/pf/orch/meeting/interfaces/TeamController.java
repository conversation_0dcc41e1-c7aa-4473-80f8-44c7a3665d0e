package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.application.service.action.TeamActionService;
import cn.genn.pf.orch.meeting.application.service.query.TeamQueryService;
import cn.genn.pf.orch.meeting.interfaces.command.TeamInfoCommand;
import cn.genn.pf.orch.meeting.interfaces.command.TeamLevelCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.DropDownDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamLevelDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamLevelTreeDTO;
import cn.genn.pf.orch.meeting.interfaces.query.team.*;
import com.lark.oapi.service.contact.v3.model.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 班组配置
 */
@Api(tags = "班组配置")
@RestController
@RequestMapping("/team")
public class TeamController {

    @Resource
    private TeamQueryService teamQueryService;
    @Resource
    private TeamActionService teamActionService;

    @GetMapping("/level/getTree")
    @ApiOperation("查询结构树")
    public List<TeamLevelTreeDTO> getTree(@RequestParam(value = "name",required = false) String name) {
        return teamQueryService.getTree(name);
    }

    @PostMapping("/level/saveOrUpdate")
    @ApiOperation("添加或编辑班组层级")
    public Boolean saveOrUpdateLevel(@Validated @RequestBody TeamLevelCommand command) {
        return teamActionService.saveOrUpdateLevel(command);
    }

    @PostMapping("/level/remove")
    @ApiOperation("删除班组层级")
    public Boolean removeLevel(@RequestParam("id") Long id) {
        return teamActionService.removeLevel(id);
    }

    @PostMapping("/queryPage")
    @ApiOperation("条件查询班组信息")
    public PageResultDTO<TeamInfoDTO> page(@Validated @RequestBody TeamPageQuery query){
        return teamQueryService.page(query);
    }

    @GetMapping("/get")
    @ApiOperation("查询班组信息")
    public TeamInfoDTO getById(@RequestParam("id") Long id) {
        return teamQueryService.queryById(id);
    }

    @PostMapping("/saveOrUpdate")
    @ApiOperation("添加或编辑班组")
    public Boolean saveOrUpdate(@Validated @RequestBody TeamInfoCommand command) {
        return teamActionService.saveOrUpdate(command);
    }

    @PostMapping("/remove")
    @ApiOperation("删除班组")
    public Boolean remove(@RequestParam("id") Long id) {
        return teamActionService.remove(id);
    }

    @PostMapping("/queryGroupList")
    @ApiOperation("队组下拉")
    public List<String> queryGroupList(@Validated @RequestBody TeamGroupQuery query){
        return teamQueryService.queryGroupList(query);
    }
    @PostMapping("/queryTeamList")
    @ApiOperation("班组下拉")
    public List<TeamInfoDTO> queryTeamList(@Validated @RequestBody TeamQuery query){
        return teamQueryService.queryTeamList(query);
    }
    @PostMapping("/queryLeaderList")
    @ApiOperation("班组长下拉")
    public List<User> queryLeaderList(@Validated @RequestBody TeamLeaderQuery query){
        return teamQueryService.queryLeaderList(query);
    }
    @PostMapping("/queryReviewerList")
    @ApiOperation("评价人员下拉")
    public List<User> queryReviewerList(@Validated @RequestBody TeamReviewerQuery query){
        return teamQueryService.queryReviewerList(query);
    }

    @PostMapping("/queryCoalMiningUnit")
    @ApiOperation("煤矿单位下拉")
    public List<TeamLevelDTO> queryCoalMiningUnit(@Validated @RequestBody CoalMiningUnitQuery query){
        return teamQueryService.queryCoalMiningUnit(query);
    }

    @PostMapping("/queryCoalMiningRegion")
    @ApiOperation("煤矿区域下拉")
    public List<TeamLevelDTO> queryCoalMiningRegion(){
        return teamQueryService.queryCoalMiningRegion();
    }

    @PostMapping("/queryTeamByCurrentOpenId")
    @ApiOperation("当前用户查询所属班组")
    public List<DropDownDTO<Long,String>> queryTeamByCurrentOpenId(){
        return teamQueryService.queryTeamByCurrentOpenId();
    }


}
