package cn.genn.pf.orch.meeting.application.assembler;

import cn.genn.pf.orch.meeting.domain.schedule.model.entity.AttendUser;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.Schedule;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.ScheduleConfig;
import cn.genn.pf.orch.meeting.infrastructure.config.CurrentUserHolder;
import cn.genn.pf.orch.meeting.infrastructure.constant.Constants;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.*;
import cn.genn.pf.orch.meeting.interfaces.command.AttendUserCommand;
import cn.genn.pf.orch.meeting.interfaces.command.ScheduleCommand;
import cn.genn.pf.orch.meeting.interfaces.command.ScheduleConfigCommand;
import cn.genn.pf.orch.meeting.interfaces.command.UpdateScheduleCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.AttendUserDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleConfigDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ScheduleAssembler {

    default ScheduleConfig toScheduleConfig(ScheduleConfigCommand command) {
        return ScheduleConfig.builder()
            .businessMeetingId(command.getBusinessMeetingId())
            .goal(command.getGoal())
            .decision(command.getDecision())
            .nameTemplate(Constants.NAME_TEMPLATE)
            .dataCatalog(Catalog.builder()
                .names(command.getDataCatalog())
                .build())
            .achievementCatalog(Catalog.builder()
                .names(command.getAchievementCatalog())
                .build())
            .build();
    }

    ScheduleConfigPO toScheduleConfigPO(ScheduleConfig scheduleConfig);

    default ScheduleConfigDTO toScheduleConfigDTO(ScheduleConfigPO scheduleConfigPO) {
        if (scheduleConfigPO == null) {
            return null;
        }
        return ScheduleConfigDTO.builder()
            .id(scheduleConfigPO.getId())
            .businessMeetingId(scheduleConfigPO.getBusinessMeetingId())
            .goal(scheduleConfigPO.getGoal())
            .decision(scheduleConfigPO.getDecision())
            .dataCatalog(scheduleConfigPO.getDataCatalog().getNames())
            .achievementCatalog(scheduleConfigPO.getAchievementCatalog().getNames())
            .build();
    }

    AttendUser toAttendUser(AttendUserCommand command);

    default Schedule toSchedule(ScheduleCommand command) {
        String scheduleNameSuffix;
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        if (command.getAllDay() != null) {
            scheduleNameSuffix = command.getAllDay().format(timeFormatter);
        } else {
            scheduleNameSuffix = command.getStartTime().format(timeFormatter);
        }

        Schedule schedule = Schedule.builder()
            .teamId(command.getTeamId())
            .businessMeetingId(command.getBusinessMeetingId())
            .startTime(command.getStartTime())
            .endTime(command.getEndTime())
            .allDay(command.getAllDay())
            .scheduleName(command.getScheduleNamePrefix() + "-" + scheduleNameSuffix)
            .attendUsers(command.getAttendUsers().stream()
                .map(this::toAttendUser)
                .collect(Collectors.toList()))
            .build();

        schedule.setCreateUserId(CurrentUserHolder.getCurrentUser().getOpenId());
        if (command instanceof UpdateScheduleCommand) {
            schedule.setUpdateUserId(CurrentUserHolder.getCurrentUser().getOpenId());
        }

        Optional<AttendUser> creator = schedule.getAttendUsers().stream()
            .filter(attendUser -> attendUser.getAttendUserId().equals(CurrentUserHolder.getCurrentUser().getOpenId()))
            .findFirst();
        if (!creator.isPresent()) {
            schedule.getAttendUsers().add(AttendUser.builder()
                .attendUserId(CurrentUserHolder.getCurrentUser().getOpenId())
                .attendUserName(CurrentUserHolder.getCurrentUser().getName())
                .attendUserRole(AttendUserRoleEnum.NORMAL)
                .attendUserState(AttendUserStateEnum.EXPECT)
                .build());
        }
        return schedule;
    }

    SchedulePO toSchedulePO(Schedule schedule);

    ScheduleAttendUserPO toScheduleAttendUserPO(AttendUser attendUser);

    List<ScheduleAttendUserPO> toScheduleAttendUserPOS(List<AttendUser> addAttendUsers);

    default List<ScheduleAttendUserPO> toAttendUserPOS(Long scheduleId, List<AttendUser> attendUsers) {
        return attendUsers.stream()
            .map(attendUser -> {
                ScheduleAttendUserPO attendUserPO = this.toScheduleAttendUserPO(attendUser);
                attendUserPO.setScheduleId(scheduleId);
                return attendUserPO;
            })
            .collect(Collectors.toList());
    }

    AttendUserDTO toAttendUserDTO(ScheduleAttendUserPO attendUserPO);
}
