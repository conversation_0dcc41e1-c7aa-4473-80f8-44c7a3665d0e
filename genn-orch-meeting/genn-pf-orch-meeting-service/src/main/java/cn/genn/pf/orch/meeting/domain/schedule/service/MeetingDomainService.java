package cn.genn.pf.orch.meeting.domain.schedule.service;

import cn.genn.pf.orch.meeting.application.dto.MeetingEndDTO;
import cn.genn.pf.orch.meeting.application.dto.MeetingStartDTO;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingAttendUser;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IMeetingRepository;
import cn.genn.pf.orch.meeting.interfaces.command.MeetingUploadEvaluateCommand;
import cn.genn.pf.orch.meeting.interfaces.command.MeetingUploadRecordCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议领域服务
 * @date 2025-01-02
 */
@Slf4j
@Service
public class MeetingDomainService {

    @Resource
    private IMeetingRepository meetingRepository;

    public void create(MeetingAgg meetingAgg) {
        meetingRepository.insert(meetingAgg);
    }

    public void update(MeetingAgg meetingAgg, ScheduleDTO schedule) {
        meetingAgg.update(schedule);
        meetingRepository.updateExtend(meetingAgg);
        meetingRepository.updateInfo(meetingAgg);
    }

    public void start(MeetingAgg meetingAgg, MeetingStartDTO meetingStartDTO) {
        meetingAgg.start(meetingStartDTO);
        meetingRepository.start(meetingAgg);
    }

    public void end(MeetingAgg meetingAgg, MeetingEndDTO meetingEndDTO) {
        meetingAgg.end(meetingEndDTO);
        meetingRepository.end(meetingAgg);
    }

    public MeetingAgg findById(Long id) {
        return meetingRepository.findById(id);
    }

    public MeetingAgg findByScheduleId(Long scheduleId) {
        return meetingRepository.findByScheduleId(scheduleId);
    }

    public void uploadRecord(MeetingAgg meetingAgg, MeetingUploadRecordCommand command) {
        meetingAgg.uploadRecord(command);
        meetingRepository.uploadRecord(meetingAgg);
    }

    public void uploadEvaluate(MeetingAgg meetingAgg, MeetingUploadEvaluateCommand command) {
        meetingAgg.uploadEvaluate(command);
        meetingRepository.uploadEvaluate(meetingAgg);
    }

    public void delete(MeetingAgg meetingAgg) {
        meetingAgg.delete();
        meetingRepository.delete(meetingAgg);
    }

    public void recordReady(MeetingAgg meetingAgg, String minuteUrl) {
        meetingAgg.recordReady(minuteUrl);
        meetingRepository.recordReady(meetingAgg);
    }

    public void signIn(MeetingAttendUser meetingAttendUser) {
        meetingRepository.signIn(meetingAttendUser);
    }


    public void saveRecordDraft(MeetingAgg meetingAgg, MeetingUploadRecordCommand command) {
        meetingAgg.saveRecordDraft(command);
        meetingRepository.saveRecordDraft(meetingAgg);
    }

    public void saveEvaluateDraft(MeetingAgg meetingAgg, MeetingUploadEvaluateCommand command) {
        meetingAgg.saveEvaluateDraft(command);
        meetingRepository.saveEvaluateDraft(meetingAgg);
    }

    public void deleteRecordDraft(Long meetingId) {
        meetingRepository.deleteRecordDraft(meetingId);
    }

    public void deleteEvaluateDraft(Long meetingId) {
        meetingRepository.deleteEvaluateDraft(meetingId);
    }
}
