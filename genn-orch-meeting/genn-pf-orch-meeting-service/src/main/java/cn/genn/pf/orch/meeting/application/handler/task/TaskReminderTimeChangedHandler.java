package cn.genn.pf.orch.meeting.application.handler.task;

import cn.genn.pf.orch.meeting.domain.task.model.aggregates.TaskAgg;
import cn.genn.pf.orch.meeting.domain.task.service.TaskDomainService;
import cn.genn.pf.orch.meeting.infrastructure.enums.TaskNotificationTypeEnum;
import com.lark.oapi.service.task.v1.model.P2TaskUpdatedV1Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 任务提醒时间发生变化
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TaskReminderTimeChangedHandler implements TaskNotificationHandler {

    private final TaskDomainService taskDomainService;

    @Override
    public void handle(P2TaskUpdatedV1Data notification) {
        log.info("处理任务提醒时间变化事件，飞书任务ID：{}", notification.getTaskId());
        try {
            // 查询本地任务
            TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(notification.getTaskId());
            if (taskAgg == null) {
                log.warn("未找到对应的本地任务，飞书任务ID：{}", notification.getTaskId());
                return;
            }

            // 这里可以根据需要从飞书API获取最新的提醒时间信息并更新
            // 由于我们的任务模型中主要关注截止时间，暂时不处理提醒时间
            // 如果需要扩展提醒功能，可以在TaskInfo中添加相应字段

            log.info("任务提醒时间变化处理完成，飞书任务ID：{}", notification.getTaskId());
        } catch (Exception e) {
            log.error("处理任务提醒时间变化事件失败，飞书任务ID：{}", notification.getTaskId(), e);
        }
    }

    @Override
    public TaskNotificationTypeEnum getObjType() {
        return TaskNotificationTypeEnum.TASK_REMINDER_CHANGED;
    }
}
