package cn.genn.pf.orch.meeting.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 飞书配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "app.third.feishu")
public class FeishuConfigProperties {

    /**
     * 是否启用飞书功能
     */
    private boolean enabled = true;

    /**
     * 小程序配置
     */
    private MiniConfig mini;

    @Data
    public static class MiniConfig {
        /**
         * 配置列表
         */
        private List<AppConfig> config;
    }

    @Data
    public static class AppConfig {
        /**
         * 应用代码
         */
        private String code;

        /**
         * 应用ID
         */
        private String appId;

        /**
         * 应用密钥
         */
        private String appSecret;
    }

    /**
     * 根据code获取应用配置
     *
     * @param code 应用代码
     * @return 应用配置
     */
    public AppConfig getAppConfigByCode(String code) {
        if (mini == null || mini.getConfig() == null) {
            return null;
        }

        return mini.getConfig().stream()
                .filter(config -> code.equals(config.getCode()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取默认的应用配置（第一个）
     *
     * @return 应用配置
     */
    public AppConfig getDefaultAppConfig() {
        if (mini == null || mini.getConfig() == null || mini.getConfig().isEmpty()) {
            return null;
        }

        return mini.getConfig().get(0);
    }

    /**
     * 获取pf_meeting的应用配置
     *
     * @return 应用配置
     */
    public AppConfig getPfMeetingConfig() {
        AppConfig config = getAppConfigByCode("pf_meeting");
        return config != null ? config : getDefaultAppConfig();
    }
}
