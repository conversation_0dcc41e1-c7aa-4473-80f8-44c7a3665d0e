package cn.genn.pf.orch.meeting.infrastructure.repository.mapper;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.SchedulePO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

public interface ScheduleMapper extends BaseMapper<SchedulePO> {

    void insertAndGetId(SchedulePO schedulePO);

    default boolean hasDataByTeamId(Long teamId) {
        return selectCount(Wrappers.lambdaQuery(SchedulePO.class)
            .eq(SchedulePO::getTeamId, teamId)
            .eq(SchedulePO::getDeleted, DeletedEnum.NOT_DELETED)) > 0;
    }
}
