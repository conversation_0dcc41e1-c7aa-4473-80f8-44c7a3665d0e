package cn.genn.pf.orch.meeting.domain.meeting.model.aggregates;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.pf.orch.meeting.application.dto.MeetingCreateDTO;
import cn.genn.pf.orch.meeting.application.dto.MeetingEndDTO;
import cn.genn.pf.orch.meeting.application.dto.MeetingStartDTO;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingAttendUser;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingExtend;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingInfo;
import cn.genn.pf.orch.meeting.infrastructure.utils.SecureRandomCheckInCodeGenerator;
import cn.genn.pf.orch.meeting.interfaces.command.MeetingUploadEvaluateCommand;
import cn.genn.pf.orch.meeting.interfaces.command.MeetingUploadRecordCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.EvaluateReminderEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingStatusEnum;
import com.lark.oapi.service.vc.v1.model.MeetingParticipant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 会议聚合
 * @date 2024-12-31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingAgg {

    private MeetingInfo info;

    private MeetingExtend extend;

    private List<MeetingAttendUser> attendUsers;

    public static MeetingAgg create(MeetingCreateDTO dto) {
        // 签到码生成
        String checkInNumber = SecureRandomCheckInCodeGenerator.generate(dto.getExistCheckInNumbers());
        ScheduleDTO schedule = dto.getSchedule();
        MeetingInfo meetingInfo = MeetingInfo.builder()
            .scheduleId(schedule.getId())
            .name(schedule.getScheduleName())
            .meetingUrl(schedule.getFsMeetingUrl())
            .checkInNumber(checkInNumber)
            .createTime(LocalDateTime.now())
            .updateTime(LocalDateTime.now())
            .createUserId(schedule.getCreateUserId())
            .updateUserId(schedule.getCreateUserId())
            .status(MeetingStatusEnum.WAIT_START)
            .deleted(DeletedEnum.NOT_DELETED)
            .build();

        MeetingExtend meetingExtend = MeetingExtend.builder().scheduleConfig(schedule.getScheduleConfigDTO()).teamInfo(schedule.getTeamInfoDTO()).build();
        return MeetingAgg.builder().info(meetingInfo).extend(meetingExtend).build();
    }

    public void update(ScheduleDTO schedule) {
        info.setName(schedule.getScheduleName());
        extend.setTeamInfo(schedule.getTeamInfoDTO());
        extend.setScheduleConfig(schedule.getScheduleConfigDTO());
    }

    /**
     * 会议开始
     */
    public void start(MeetingStartDTO dto) {
        info.setMeetingNo(dto.getMeetingNo());
        info.setFsMeetingId(dto.getFsMeetingId());
        info.setStartTime(dto.getStartTime());
        info.setStatus(MeetingStatusEnum.IN_PROCESS);
        info.setEndTime(null);
    }

    /**
     * 会议结束
     */
    public void end(MeetingEndDTO dto) {
        // 1.会议状态变更、结束时间设置
        info.setEndTime(dto.getEndTime());
        info.setStatus(MeetingStatusEnum.END);
        // 2.会议参会人具体时长更新
        for (MeetingParticipant participant : dto.getFeishuMeeting().getMeeting().getParticipants()) {
            Optional<MeetingAttendUser> existingUserOpt = attendUsers.stream()
                .filter(user -> user.getFsUserId().equals(participant.getId()))
                .findFirst();

            if (existingUserOpt.isPresent()) {
                // Update existing user
                MeetingAttendUser existingUser = existingUserOpt.get();
                existingUser.setFirstJoinTime(getTime(participant.getFirstJoinTime()));
                existingUser.setFinalLeaveTime(getTime(participant.getFinalLeaveTime()));
                existingUser.setInMeetingDuration(Integer.valueOf(participant.getInMeetingDuration()));
            } else {
                // Add new user
                MeetingAttendUser newUser = new MeetingAttendUser();
                newUser.setFsUserId(participant.getId());
                newUser.setMeetingId(info.getId());
                newUser.setFirstJoinTime(getTime(participant.getFirstJoinTime()));
                newUser.setFinalLeaveTime(getTime(participant.getFinalLeaveTime()));
                newUser.setInMeetingDuration(Integer.valueOf(participant.getInMeetingDuration()));
                attendUsers.add(newUser);
            }
        }
    }

    private LocalDateTime getTime(String time) {
        Instant instant = Instant.ofEpochSecond(Long.parseLong(time));
        // 将Instant转换为LocalDateTime，使用系统默认时区
        return LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Shanghai"));
    }

    public void uploadRecord(MeetingUploadRecordCommand command) {
        // 删除草稿
        extend.setRecordDraft(null);
        extend.setDataCatalogDraft(Collections.emptyList());
        extend.setAchievementCatalogDraft(Collections.emptyList());

        extend.setRecord(command.getRecord());
        extend.setDataCatalog(command.getDataCatalog());
        extend.setAchievementCatalog(command.getAchievementCatalog());
        extend.setRecordTime(LocalDateTime.now());
        // 修改会议记录，重新触发提醒
        extend.setEvaluateReminder(EvaluateReminderEnum.NOT_REMINDED);
    }

    public void uploadEvaluate(MeetingUploadEvaluateCommand command) {
        // 删除草稿
        extend.setEvaluateDraft("");

        extend.setEvaluate(command.getEvaluate());
    }

    public void delete() {
        info.setDeleted(DeletedEnum.DELETED);
        extend = null;
    }

    public void recordReady(String url) {
        info.setMinuteUrl(url);
    }

    public void saveRecordDraft(MeetingUploadRecordCommand command) {
        extend.setRecordDraft(command.getRecord());
        extend.setDataCatalogDraft(command.getDataCatalog());
        extend.setAchievementCatalogDraft(command.getAchievementCatalog());
    }

    public void saveEvaluateDraft(MeetingUploadEvaluateCommand command) {
        extend.setEvaluateDraft(command.getEvaluate());
    }
}
