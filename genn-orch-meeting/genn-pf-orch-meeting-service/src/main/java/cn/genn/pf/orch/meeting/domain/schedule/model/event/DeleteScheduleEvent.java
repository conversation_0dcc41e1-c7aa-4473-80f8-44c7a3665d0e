package cn.genn.pf.orch.meeting.domain.schedule.model.event;

import cn.genn.spring.boot.starter.event.spring.model.SpringBaseEvent;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 删除日程事件
 */
public class DeleteScheduleEvent extends SpringBaseEvent {

    public DeleteScheduleEvent(DeleteScheduleData data) {
        super(data);
    }

    @Data
    @Builder
    public static class DeleteScheduleData {

        Long scheduleId;
    }
}
