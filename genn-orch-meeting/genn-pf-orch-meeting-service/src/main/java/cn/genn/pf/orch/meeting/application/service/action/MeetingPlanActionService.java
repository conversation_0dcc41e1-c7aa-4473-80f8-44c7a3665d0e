package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.pf.orch.meeting.application.assembler.MeetingPlanAssembler;
import cn.genn.pf.orch.meeting.domain.meetingplan.model.entity.MeetingPlan;
import cn.genn.pf.orch.meeting.domain.meetingplan.service.MeetingPlanDomainService;
import cn.genn.pf.orch.meeting.interfaces.command.meetingplan.MeetingPlanCreateCommand;
import cn.genn.pf.orch.meeting.interfaces.command.meetingplan.MeetingPlanUpdateCommand;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description 会议规划操作服务
 * @date 2025-01-24
 */
@Service
@RequiredArgsConstructor
public class MeetingPlanActionService {

    private final MeetingPlanDomainService meetingPlanDomainService;
    private final MeetingPlanAssembler meetingPlanAssembler;

    /**
     * 创建会议规划
     */
    @Transactional
    public void create(MeetingPlanCreateCommand command) {
        MeetingPlan meetingPlan = meetingPlanAssembler.toEntity(command);
        meetingPlanDomainService.createMeetingPlan(meetingPlan);
    }

    /**
     * 更新会议规划
     */
    @Transactional
    public void update(MeetingPlanUpdateCommand command) {
        MeetingPlan meetingPlan = meetingPlanAssembler.toEntity(command);
        meetingPlanDomainService.updateMeetingPlan(meetingPlan);
    }

    /**
     * 删除会议规划
     */
    @Transactional
    public void delete(Long id) {
        meetingPlanDomainService.deleteMeetingPlan(id);
    }


}
