package cn.genn.pf.orch.meeting.application.processor;

import cn.genn.core.exception.BusinessException;
import cn.genn.pf.orch.meeting.domain.team.model.entity.TeamInfo;
import cn.genn.pf.orch.meeting.domain.team.model.entity.TeamLevel;
import cn.genn.pf.orch.meeting.infrastructure.exception.MessageCode;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.ScheduleMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TeamInfoMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TeamLevelMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamInfoPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamLevelPO;
import cn.genn.pf.orch.meeting.interfaces.command.TeamLevelCommand;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class TeamProcessor {

    @Resource
    private TeamInfoMapper teamInfoMapper;
    @Resource
    private TeamLevelMapper teamLevelMapper;
    @Resource
    private ScheduleMapper scheduleMapper;

    public void checkDeleteLevel(Long id) {
        List<TeamLevelPO> teamLevels  = teamLevelMapper.selectByPid(id);
        if(CollUtil.isNotEmpty(teamLevels)){
            throw new BusinessException(MessageCode.TEAM_LEVEL_DELETE_STOP2);
        }
        List<TeamInfoPO> teamInfos  = teamInfoMapper.selectByLevelId(id);
        if(CollUtil.isNotEmpty(teamInfos)){
            throw new BusinessException(MessageCode.TEAM_LEVEL_DELETE_STOP);
        }
    }

    public void checkDeleteInfo(Long id) {
        if(scheduleMapper.hasDataByTeamId(id)){
            throw new BusinessException(MessageCode.TEAM_INFO_DELETE_STOP);
        }
    }

}
