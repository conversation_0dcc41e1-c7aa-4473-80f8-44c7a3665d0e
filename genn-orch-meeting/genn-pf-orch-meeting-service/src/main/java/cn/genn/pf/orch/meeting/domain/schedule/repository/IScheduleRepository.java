package cn.genn.pf.orch.meeting.domain.schedule.repository;

import cn.genn.pf.orch.meeting.domain.schedule.model.entity.AttendUser;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.Schedule;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.ScheduleConfig;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.ScheduleAttendUserPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.ScheduleConfigPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.SchedulePO;

import java.util.List;

/**
 * 日程仓储接口
 *
 * <AUTHOR>
 */
public interface IScheduleRepository {

    void addConfig(ScheduleConfig scheduleConfig);

    void updateConfig(ScheduleConfig scheduleConfig);

    ScheduleConfigPO queryConfigByBusinessMeetingId(Long businessMeetingId);

    List<ScheduleConfigPO> queryConfigByBusinessMeetingIds(List<Long> businessMeetingIds);

    void add(Schedule schedule);

    SchedulePO queryScheduleById(Long scheduleId);

    List<SchedulePO> queryScheduleByIds(List<Long> scheduleIds);

    SchedulePO queryScheduleByFSCalendarEventId(String fsCalendarEventId);

    List<ScheduleAttendUserPO> queryUserByScheduleId(Long scheduleIds);

    List<ScheduleAttendUserPO> queryUserByScheduleIds(List<Long>  scheduleIds);

    void update(Schedule schedule);

    void addAttendUsers(Long scheduleId, List<AttendUser> addAttendUsers);

    void removeAttendUsers(Long scheduleId, List<String> removeAttendUserIds);

    void updateAttendUsers(Long scheduleId, List<AttendUser> attendUsers);

    void delete(Long scheduleId);
}
