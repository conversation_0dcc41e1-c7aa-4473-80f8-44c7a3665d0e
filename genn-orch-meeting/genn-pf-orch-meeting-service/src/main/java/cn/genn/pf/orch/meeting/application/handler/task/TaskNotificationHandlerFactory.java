package cn.genn.pf.orch.meeting.application.handler.task;

import cn.genn.pf.orch.meeting.infrastructure.enums.TaskNotificationTypeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TaskNotificationHandlerFactory {

    @Resource
    private List<TaskNotificationHandler> handlerBeans;

    private final Map<TaskNotificationTypeEnum, TaskNotificationHandler> handlers = new HashMap<>();

    @PostConstruct
    public void init() {
        // 遍历所有处理器Bean并按objType注册
        for (TaskNotificationHandler handler : handlerBeans) {
            TaskNotificationTypeEnum objType = handler.getObjType();
            if (handlers.containsKey(objType)) {
                throw new IllegalStateException("发现重复的任务通知处理器类型: " + objType);
            }
            handlers.put(objType, handler);
        }
    }

    public TaskNotificationHandler getHandler(TaskNotificationTypeEnum objType) {
        return objType != null ? handlers.getOrDefault(objType, new DefaultTaskNotificationHandler()) : new DefaultTaskNotificationHandler();
    }
}
