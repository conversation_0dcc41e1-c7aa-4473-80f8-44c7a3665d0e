package cn.genn.pf.orch.meeting.infrastructure.repository.persistence;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.pf.orch.meeting.domain.newmeeting.model.entity.NewMeeting;
import cn.genn.pf.orch.meeting.domain.newmeeting.repository.INewMeetingRepository;
import cn.genn.pf.orch.meeting.infrastructure.converter.NewMeetingConverter;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.NewMeetingMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.NewMeetingPO;
import cn.genn.pf.orch.meeting.interfaces.enums.NewMeetingStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议仓储实现
 * @date 2025-01-24
 */
@Repository
@RequiredArgsConstructor
public class NewMeetingRepositoryImpl implements INewMeetingRepository {

    private final NewMeetingMapper newMeetingMapper;
    private final NewMeetingConverter newMeetingConverter;

    @Override
    public void save(NewMeeting meeting) {
        NewMeetingPO po = newMeetingConverter.toPO(meeting);
        newMeetingMapper.insert(po);
        meeting.setId(po.getId());
    }

    @Override
    public void update(NewMeeting meeting) {
        NewMeetingPO po = newMeetingConverter.toPO(meeting);
        newMeetingMapper.updateById(po);
    }

    @Override
    public NewMeeting findById(Long id) {
        NewMeetingPO po = newMeetingMapper.selectById(id);
        return po != null ? newMeetingConverter.toEntity(po) : null;
    }

    @Override
    public NewMeeting findByFsCalendarEventId(String fsCalendarEventId) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = Wrappers.lambdaQuery(NewMeetingPO.class)
            .eq(NewMeetingPO::getFsCalendarEventId, fsCalendarEventId);
        NewMeetingPO po = newMeetingMapper.selectOne(wrapper);
        return po != null ? newMeetingConverter.toEntity(po) : null;
    }

    @Override
    public NewMeeting findByFsMeetingId(String fsMeetingId) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = Wrappers.lambdaQuery(NewMeetingPO.class)
            .eq(NewMeetingPO::getFsMeetingId, fsMeetingId);
        NewMeetingPO po = newMeetingMapper.selectOne(wrapper);
        return po != null ? newMeetingConverter.toEntity(po) : null;
    }

    @Override
    public void deleteById(Long id) {
        newMeetingMapper.deleteById(id);
    }

    @Override
    public List<NewMeeting> findPage(String meetingName, Integer status, Integer priorityLevel,
                                   Long meetingPlanId, Long meetingStandardId,
                                   String startTimeFrom, String startTimeTo,
                                   String createUserId, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = buildQueryWrapper(meetingName, status, priorityLevel,
                meetingPlanId, meetingStandardId, startTimeFrom, startTimeTo, createUserId);
        wrapper.orderByDesc(NewMeetingPO::getCreateTime);

        Page<NewMeetingPO> page = new Page<>(pageNum, pageSize);
        Page<NewMeetingPO> result = newMeetingMapper.selectPage(page, wrapper);
        return newMeetingConverter.toEntityList(result.getRecords());
    }

    @Override
    public Long count(String meetingName, Integer status, Integer priorityLevel,
                     Long meetingPlanId, Long meetingStandardId,
                     String startTimeFrom, String startTimeTo, String createUserId) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = buildQueryWrapper(meetingName, status, priorityLevel,
                meetingPlanId, meetingStandardId, startTimeFrom, startTimeTo, createUserId);
        return newMeetingMapper.selectCount(wrapper);
    }

    private LambdaQueryWrapper<NewMeetingPO> buildQueryWrapper(String meetingName, Integer status, Integer priorityLevel,
                                                             Long meetingPlanId, Long meetingStandardId,
                                                             String startTimeFrom, String startTimeTo, String createUserId) {
        LambdaQueryWrapper<NewMeetingPO> wrapper = Wrappers.lambdaQuery(NewMeetingPO.class);

        // 逻辑删除条件：只查询未删除的数据
        wrapper.eq(NewMeetingPO::getDeleted, DeletedEnum.NOT_DELETED);

        if (StringUtils.hasText(meetingName)) {
            wrapper.like(NewMeetingPO::getMeetingName, meetingName);
        }

        if (status != null) {
            wrapper.eq(NewMeetingPO::getStatus, status);
        }

        if (priorityLevel != null) {
            wrapper.eq(NewMeetingPO::getPriorityLevel, priorityLevel);
        }

        if (meetingPlanId != null) {
            wrapper.eq(NewMeetingPO::getMeetingPlanId, meetingPlanId);
        }

        if (meetingStandardId != null) {
            wrapper.eq(NewMeetingPO::getMeetingStandardId, meetingStandardId);
        }

        if (StringUtils.hasText(startTimeFrom)) {
            wrapper.ge(NewMeetingPO::getStartTime, startTimeFrom);
        }

        if (StringUtils.hasText(startTimeTo)) {
            wrapper.le(NewMeetingPO::getStartTime, startTimeTo);
        }

        if (StringUtils.hasText(createUserId)) {
            wrapper.eq(NewMeetingPO::getCreateUserId, createUserId);
        }

        return wrapper;
    }

    @Override
    public void updateStatus(Long id, NewMeetingStatusEnum status) {
        NewMeetingPO po = new NewMeetingPO();
        po.setId(id);
        po.setStatus(status);
        newMeetingMapper.updateById(po);
    }

    @Override
    public void updateFeishuInfo(Long id, String fsCalendarEventId, String fsMeetingId, String meetingUrl) {
        NewMeetingPO po = new NewMeetingPO();
        po.setId(id);
        po.setFsCalendarEventId(fsCalendarEventId);
        po.setFsMeetingId(fsMeetingId);
        po.setMeetingUrl(meetingUrl);
        newMeetingMapper.updateById(po);
    }

    @Override
    public void updateMeetingNoAndFsMeetingId(Long id, String meetingNo, String fsMeetingId) {
        NewMeetingPO po = new NewMeetingPO();
        po.setId(id);
        po.setMeetingNo(meetingNo);
        po.setFsMeetingId(fsMeetingId);
        newMeetingMapper.updateById(po);
    }

    @Override
    public void updateMeetingInfo(Long id, String meetingNo, String fsMeetingId, String meetingUrl) {
        NewMeetingPO po = new NewMeetingPO();
        po.setId(id);
        po.setMeetingNo(meetingNo);
        po.setFsMeetingId(fsMeetingId);
//        po.setMeetingUrl(meetingUrl);
        newMeetingMapper.updateById(po);
    }

    @Override
    public void updateMinuteUrl(Long id, String minuteUrl) {
        NewMeetingPO po = new NewMeetingPO();
        po.setId(id);
        po.setMinuteUrl(minuteUrl);
        newMeetingMapper.updateById(po);
    }
}
