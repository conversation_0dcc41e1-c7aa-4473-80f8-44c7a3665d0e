package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.pf.orch.meeting.application.dto.MeetingEvaluationSubmitCommand;
import cn.genn.pf.orch.meeting.application.dto.card.SendMeetingEvaluationSurveyDTO;
import cn.genn.pf.orch.meeting.application.service.query.MeetingQueryService;
import cn.genn.pf.orch.meeting.application.service.query.NewMeetingQueryService;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingEvaluationAgg;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingEvaluationInfo;
import cn.genn.pf.orch.meeting.domain.meeting.service.MeetingEvaluationDomainService;
import cn.genn.pf.orch.meeting.infrastructure.utils.DateUtils;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingDetailDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingAttendUserDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.newmeeting.NewMeetingDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.user.FSUserInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.query.MeetingDetailQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议评价服务
 * @date 2025-01-24
 */
@Service
@Slf4j
public class MeetingEvaluationActionService {

    @Resource
    private MeetingEvaluationDomainService evaluationDomainService;
    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private MeetingQueryService meetingQueryService;
    @Resource
    private NewMeetingQueryService newMeetingQueryService;
    @Resource
    private ScheduleQueryService scheduleQueryService;

    /**
     * 会议结束后发送评价卡片（传统会议系统）
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendEvaluationCards(Long meetingId) {
        log.info("开始发送会议评价卡片，会议ID：{}", meetingId);

        // 获取会议信息
        MeetingDetailDTO meetingDetail = meetingQueryService.detail(
            MeetingDetailQuery.builder().id(meetingId).build()
        );

        // 获取日程信息以获取会议类型
        ScheduleDTO scheduleDTO = scheduleQueryService.getByScheduleId(meetingDetail.getScheduleId());

        // 获取参会人员列表
        List<String> attendeeOpenIds = meetingDetail.getAttendUserList().stream()
            .map(MeetingAttendUserDTO::getAttendUserId)
            .collect(Collectors.toList());

        List<String> attendeeNames = meetingDetail.getAttendUserList().stream()
            .map(MeetingAttendUserDTO::getAttendUserName)
            .collect(Collectors.toList());

        // 发送评价卡片
        for (int i = 0; i < attendeeOpenIds.size(); i++) {
            try {
                SendMeetingEvaluationSurveyDTO surveyDTO = SendMeetingEvaluationSurveyDTO.builder()
                    .meetingId(meetingId)
                    .meetingName(meetingDetail.getName())
                    .meetingType(scheduleDTO.getBusinessMeetingName())
                    .meetingTime(DateUtils.getTimeRange(meetingDetail.getStartTime(), meetingDetail.getEndTime()))
                    .openId(attendeeOpenIds.get(i))
                    .evaluatorName(attendeeNames.get(i))
                    .build();

                cardSendActionService.sendMeetingEvaluationSurvey(surveyDTO);

                log.info("评价卡片发送成功，会议ID：{}，接收人：{}", meetingId, attendeeNames.get(i));
            } catch (Exception e) {
                log.error("评价卡片发送失败，会议ID：{}，接收人：{}", meetingId, attendeeNames.get(i), e);
            }
        }
    }

    /**
     * 新会议系统会议结束后发送评价卡片
     */
    @Transactional(rollbackFor = Exception.class)
    public void sendNewMeetingEvaluationCards(Long newMeetingId) {
        log.info("开始发送新会议评价卡片，会议ID：{}", newMeetingId);

        // 获取新会议信息
        NewMeetingDTO newMeeting = newMeetingQueryService.getById(newMeetingId);
        if (newMeeting == null) {
            log.error("未找到新会议信息，会议ID：{}", newMeetingId);
            return;
        }

        // 获取参会人员列表
        List<String> attendeeOpenIds = newMeeting.getAttendees();
        if (CollectionUtils.isEmpty(attendeeOpenIds)) {
            log.warn("新会议没有参会人员，会议ID：{}", newMeetingId);
            return;
        }

        // 获取参会人员姓名列表
        List<String> attendeeNames;
        if (!CollectionUtils.isEmpty(newMeeting.getAttendeeDetails())) {
            attendeeNames = newMeeting.getAttendeeDetails().stream()
                .map(FSUserInfoDTO::getName)
                .collect(Collectors.toList());
        } else {
            // 如果详细信息为空，使用openId作为姓名
            attendeeNames = attendeeOpenIds;
        }

        // 发送评价卡片
        for (int i = 0; i < attendeeOpenIds.size(); i++) {
            try {
                SendMeetingEvaluationSurveyDTO surveyDTO = SendMeetingEvaluationSurveyDTO.builder()
                    .meetingId(newMeetingId)
                    .meetingName(newMeeting.getMeetingName())
                    .meetingType("新会议") // 新会议系统的会议类型
                    .meetingTime(DateUtils.getTimeRange(newMeeting.getStartTime(), newMeeting.getEndTime()))
                    .openId(attendeeOpenIds.get(i))
                    .evaluatorName(attendeeNames.get(i))
                    .build();

                cardSendActionService.sendMeetingEvaluationSurvey(surveyDTO);

                log.info("新会议评价卡片发送成功，会议ID：{}，接收人：{}", newMeetingId, attendeeNames.get(i));
            } catch (Exception e) {
                log.error("新会议评价卡片发送失败，会议ID：{}，接收人：{}", newMeetingId, attendeeNames.get(i), e);
            }
        }
    }

    /**
     * 处理评价提交
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitEvaluation(MeetingEvaluationSubmitCommand command) {
        log.info("提交会议评价，会议ID：{}，评价人：{}", command.getMeetingId(), command.getEvaluatorOpenId());

        // 检查是否已经评价过
        MeetingEvaluationAgg existingEvaluation = evaluationDomainService.findByMeetingIdAndEvaluator(
            command.getMeetingId(), command.getEvaluatorOpenId());

        if (existingEvaluation != null) {
            throw new BusinessException("您已经评价过此会议");
        }

        // 创建新的评价聚合根
        MeetingEvaluationAgg evaluationAgg = MeetingEvaluationAgg.builder()
            .info(MeetingEvaluationInfo.builder()
                .meetingId(command.getMeetingId())
                .build())
            .build();

        // 提交评价
        evaluationAgg.submitEvaluation(
            command.getEvaluatorOpenId(),
            command.getEvaluatorName(),
            command.getEvaluationDTO()
        );

        // 保存评价结果
        evaluationDomainService.saveEvaluation(evaluationAgg);

        log.info("会议评价提交成功，会议ID：{}，评价人：{}", command.getMeetingId(), command.getEvaluatorOpenId());
    }
}
