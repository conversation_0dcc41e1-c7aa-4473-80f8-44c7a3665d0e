package cn.genn.pf.orch.meeting.domain.meetingplan.service;

import cn.genn.core.exception.BusinessException;
import cn.genn.pf.orch.meeting.domain.meetingplan.model.entity.MeetingPlan;
import cn.genn.pf.orch.meeting.domain.meetingplan.repository.IMeetingPlanRepository;
import cn.genn.pf.orch.meeting.domain.meetingstandard.model.entity.MeetingStandard;
import cn.genn.pf.orch.meeting.domain.meetingstandard.repository.IMeetingStandardRepository;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingPlanStatusEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划领域服务
 * @date 2025-01-24
 */
@Service
@RequiredArgsConstructor
public class MeetingPlanDomainService {

    private final IMeetingPlanRepository meetingPlanRepository;
    private final IMeetingStandardRepository meetingStandardRepository;

    /**
     * 创建会议规划
     */
    public void createMeetingPlan(MeetingPlan meetingPlan) {
        // 1. 验证会议标准
        MeetingStandard standard = meetingStandardRepository.findById(meetingPlan.getMeetingStandardId());
        if (standard == null) {
            throw new BusinessException("会议标准不存在");
        }

        // 2. 验证会议开始时间必须在当前时间之后
        if (meetingPlan.getPlannedStartTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException("会议开始时间必须在当前时间之后");
        }

        // 3. 应用默认配置
        applyStandardDefaults(meetingPlan, standard);

        // 4. 计算持续时长
        meetingPlan.calculateDuration();

        // 5. 校验会议规划是否符合会议标准限制
        validateMeetingPlanAgainstStandard(meetingPlan, standard);

        // 6. 设置初始状态
        meetingPlan.setStatus(MeetingPlanStatusEnum.NOT_STARTED);

        // 7. 设置提前通知发送标记为未发送
        meetingPlan.setAdvanceNoticeSent(0);

        // 8. 冲突检测
        validateNoConflicts(meetingPlan);

        // 9. 保存
        meetingPlanRepository.save(meetingPlan);
    }

    /**
     * 更新会议规划
     */
    public void updateMeetingPlan(MeetingPlan meetingPlan) {
        // 1. 检查是否存在
        MeetingPlan existing = meetingPlanRepository.findById(meetingPlan.getId());
        if (existing == null) {
            throw new BusinessException("会议规划不存在");
        }

        // 2. 检查是否可以修改
        if (existing.getStatus() != MeetingPlanStatusEnum.NOT_STARTED) {
            throw new BusinessException("只有未开始的会议规划才能修改");
        }

        // 3. 验证会议标准
        MeetingStandard standard = meetingStandardRepository.findById(meetingPlan.getMeetingStandardId());
        if (standard == null) {
            throw new BusinessException("会议标准不存在");
        }

        // 4. 应用默认配置
        applyStandardDefaults(meetingPlan, standard);

        // 5. 计算持续时长
        meetingPlan.calculateDuration();

        // 6. 校验会议规划是否符合会议标准限制
        validateMeetingPlanAgainstStandard(meetingPlan, standard);

        // 7. 冲突检测（排除自己）
        validateNoConflicts(meetingPlan);

        // 8. 更新
        meetingPlanRepository.update(meetingPlan);
    }

    /**
     * 删除会议规划
     */
    public void deleteMeetingPlan(Long id) {
        MeetingPlan existing = meetingPlanRepository.findById(id);
        if (existing == null) {
            throw new BusinessException("会议规划不存在");
        }

        if (existing.getStatus() != MeetingPlanStatusEnum.NOT_STARTED) {
            throw new BusinessException("只有未开始的会议规划才能删除");
        }

        meetingPlanRepository.deleteById(id);
    }

    /**
     * 更新逾期状态
     */
    public void updateOverdueStatus() {
        List<MeetingPlan> overduePlans = meetingPlanRepository.findOverduePlans();
        for (MeetingPlan plan : overduePlans) {
            plan.setStatus(MeetingPlanStatusEnum.OVERDUE);
            meetingPlanRepository.update(plan);
        }
    }

    /**
     * 应用标准默认配置
     */
    private void applyStandardDefaults(MeetingPlan meetingPlan, MeetingStandard standard) {
        if (meetingPlan.getPriorityLevel() == null) {
            meetingPlan.setPriorityLevel(standard.getPriorityLevel());
        }
        if (meetingPlan.getMeetingLocation() == null) {
            meetingPlan.setMeetingLocation(standard.getDefaultLocation());
        }
        if (meetingPlan.getPlannedEndTime() == null && standard.getDefaultDuration() != null) {
            meetingPlan.setPlannedEndTime(
                meetingPlan.getPlannedStartTime().plusMinutes(standard.getDefaultDuration())
            );
        }
    }

    /**
     * 校验会议规划是否符合会议标准限制
     */
    private void validateMeetingPlanAgainstStandard(MeetingPlan meetingPlan, MeetingStandard standard) {
        // 1. 校验参会人数限制（只在会议标准设置了限制的情况下校验）
        validateAttendeeLimits(meetingPlan, standard);

        // 2. 校验持续时长限制（只在会议标准设置了默认持续时长的情况下校验）
        validateDurationLimits(meetingPlan, standard);
    }

    /**
     * 校验参会人数限制
     */
    private void validateAttendeeLimits(MeetingPlan meetingPlan, MeetingStandard standard) {
        if (meetingPlan.getAttendees() == null || meetingPlan.getAttendees().isEmpty()) {
            return; // 如果没有设置参会人员，则不校验
        }

        int attendeeCount = meetingPlan.getAttendees().size();

        // 校验最少参会人数（只在会议标准设置了的情况下校验）
        if (standard.getMinAttendees() != null && attendeeCount < standard.getMinAttendees()) {
            throw new BusinessException(String.format(
                "会议规划参会人数为%d人，少于会议标准要求的最少%d人",
                attendeeCount, standard.getMinAttendees()));
        }

        // 校验最多参会人数（只在会议标准设置了的情况下校验）
        if (standard.getMaxAttendees() != null && attendeeCount > standard.getMaxAttendees()) {
            throw new BusinessException(String.format(
                "会议规划参会人数为%d人，超过会议标准要求的最多%d人",
                attendeeCount, standard.getMaxAttendees()));
        }
    }

    /**
     * 校验持续时长限制
     */
    private void validateDurationLimits(MeetingPlan meetingPlan, MeetingStandard standard) {
        if (meetingPlan.getPlannedDuration() == null) {
            return; // 如果没有计算持续时长，则不校验
        }

        // 校验持续时长（只在会议标准设置了默认持续时长的情况下校验）
        if (standard.getDefaultDuration() != null) {
            // 允许一定的浮动范围（比如±30分钟）
            int tolerance = 30;
            int minDuration = standard.getDefaultDuration() - tolerance;
            int maxDuration = standard.getDefaultDuration() + tolerance;

            if (meetingPlan.getPlannedDuration() < minDuration || meetingPlan.getPlannedDuration() > maxDuration) {
                throw new BusinessException(String.format(
                    "会议规划持续时长为%d分钟，与会议标准默认时长%d分钟差异过大（允许范围：%d-%d分钟）",
                    meetingPlan.getPlannedDuration(), standard.getDefaultDuration(), minDuration, maxDuration));
            }
        }
    }

    /**
     * 冲突检测
     */
    private void validateNoConflicts(MeetingPlan meetingPlan) {
        List<MeetingPlan> conflicts = meetingPlanRepository.findConflictPlans(
            meetingPlan.getPlannedStartTime(),
            meetingPlan.getPlannedEndTime(),
            meetingPlan.getMeetingLocation(),
            meetingPlan.getAttendees(),
            meetingPlan.getId()
        );

        if (!conflicts.isEmpty()) {
            StringBuilder message = new StringBuilder("存在冲突：");
            for (MeetingPlan conflict : conflicts) {
                message.append("\n- ").append(conflict.getPlanName())
                       .append("(").append(conflict.getPlannedStartTime()).append(")");

                // 详细冲突信息
                boolean locationConflict = hasLocationConflict(meetingPlan.getMeetingLocation(), conflict.getMeetingLocation());
                boolean attendeeConflict = hasAttendeeConflict(meetingPlan.getAttendees(), conflict.getAttendees());

                if (locationConflict && attendeeConflict) {
                    message.append(" [会议室冲突+人员冲突]");
                } else if (locationConflict) {
                    message.append(" [会议室冲突: ").append(conflict.getMeetingLocation()).append("]");
                } else if (attendeeConflict) {
                    message.append(" [人员冲突]");
                }
            }
            throw new BusinessException(message.toString());
        }
    }

    private boolean hasLocationConflict(String location1, String location2) {
        return location1 != null && location2 != null && location1.equals(location2);
    }

    private boolean hasAttendeeConflict(List<String> attendees1, List<String> attendees2) {
        if (attendees1 == null || attendees2 == null ||
            attendees1.isEmpty() || attendees2.isEmpty()) {
            return false;
        }
        return attendees1.stream().anyMatch(attendees2::contains);
    }
}
