package cn.genn.pf.orch.meeting.application.service.query;

import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.BusinessMeetingLevelMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.BusinessMeetingMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.BusinessMeetingLevelPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.BusinessMeetingPO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.BusinessMeetingDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.BusinessMeetingLevelDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.SingleBusinessMeetingDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.TopBusinessEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务会议查询服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessMeetingQueryService {

    private final BusinessMeetingMapper businessMeetingMapper;
    private final BusinessMeetingLevelMapper businessMeetingLevelMapper;

    public BusinessMeetingLevelDTO level() {
        List<BusinessMeetingPO> allBusinessMeetings = businessMeetingMapper.selectList(null);
        List<BusinessMeetingLevelPO> allBusinessMeetingLevels = businessMeetingLevelMapper.selectList(null);

        List<BusinessMeetingPO> businessMeetings = allBusinessMeetings.stream()
            .filter(businessMeeting -> TopBusinessEnum.TOP_BUSINESS.equals(businessMeeting.getTopBusiness()))
            .collect(Collectors.toList());

        return BusinessMeetingLevelDTO.builder()
            .businessMeetingLevel(getBusinessMeetingLevel(allBusinessMeetings, allBusinessMeetingLevels, businessMeetings, 0L))
            .build();
    }

    private List<BusinessMeetingDTO> getBusinessMeetingLevel(List<BusinessMeetingPO> allBusinessMeetings,
                                                             List<BusinessMeetingLevelPO> allBusinessMeetingLevels,
                                                             List<BusinessMeetingPO> parentBusinessMeetings,
                                                             Long parentId) {
        ArrayList<BusinessMeetingDTO> businessMeetingLevel = new ArrayList<>();

        for (BusinessMeetingPO businessMeeting : parentBusinessMeetings) {
            List<BusinessMeetingDTO> sonBusinessMeetings = getSonBusinessMeetings(allBusinessMeetings, allBusinessMeetingLevels, businessMeeting.getId());

            businessMeetingLevel.add(BusinessMeetingDTO.builder()
                .levelId(parentId + "-" + businessMeeting.getId())
                .id(businessMeeting.getId())
                .name(businessMeeting.getName())
                .sonBusinessMeeting(sonBusinessMeetings)
                .build());
        }

        return businessMeetingLevel;
    }

    private List<BusinessMeetingDTO> getSonBusinessMeetings(List<BusinessMeetingPO> allBusinessMeetings,
                                                            List<BusinessMeetingLevelPO> allBusinessMeetingLevels,
                                                            Long parentId) {
        List<Long> sonIds = allBusinessMeetingLevels.stream()
            .filter(businessMeetingLevel -> parentId.equals(businessMeetingLevel.getParentId()))
            .map(BusinessMeetingLevelPO::getSonId)
            .collect(Collectors.toList());

        if (sonIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<BusinessMeetingPO> businessMeetings = allBusinessMeetings.stream()
            .filter(businessMeeting -> sonIds.contains(businessMeeting.getId()))
            .collect(Collectors.toList());

        return getBusinessMeetingLevel(allBusinessMeetings, allBusinessMeetingLevels, businessMeetings, parentId);
    }

    public List<SingleBusinessMeetingDTO> list() {
        List<BusinessMeetingPO> allBusinessMeetings = businessMeetingMapper.selectList(null);
        List<BusinessMeetingLevelPO> allBusinessMeetingLevels = businessMeetingLevelMapper.selectList(null);

        List<Long> allParentId = allBusinessMeetingLevels.stream()
            .map(BusinessMeetingLevelPO::getParentId)
            .collect(Collectors.toList());

        return allBusinessMeetings.stream()
            .filter(businessMeeting -> TopBusinessEnum.NOT_TOP_BUSINESS.equals(businessMeeting.getTopBusiness()))
            .filter(businessMeeting -> !allParentId.contains(businessMeeting.getId()))
            .map(businessMeeting -> SingleBusinessMeetingDTO.builder()
                .id(businessMeeting.getId())
                .name(businessMeeting.getName())
                .build())
            .collect(Collectors.toList());
    }

    public BusinessMeetingPO get(Long id) {
        return businessMeetingMapper.selectById(id);
    }
}
