package cn.genn.pf.orch.meeting.domain.schedule.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Schedule
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Schedule {

    private Long id;

    private Long teamId;
    private Long businessMeetingId;

    private String scheduleName;

    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private LocalDateTime allDay;

    private String fsCalendarId;
    private String fsCalendarEventId;
    private String fsMeetingUrl;

    private String createUserId;
    private String updateUserId;

    private List<AttendUser> attendUsers;
}
