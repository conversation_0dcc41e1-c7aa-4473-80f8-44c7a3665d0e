package cn.genn.pf.orch.meeting.infrastructure.converter;

import cn.genn.pf.orch.meeting.domain.task.model.entity.TaskInfo;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TaskPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务转换器
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TaskConverter {

    /**
     * TaskInfo转TaskPO
     *
     * @param taskInfo 任务信息
     * @return TaskPO
     */
    TaskPO toTaskPO(TaskInfo taskInfo);

    /**
     * TaskPO转TaskInfo
     *
     * @param taskPO TaskPO
     * @return 任务信息
     */
    TaskInfo toTaskInfo(TaskPO taskPO);

    /**
     * TaskPO列表转TaskInfo列表
     *
     * @param taskPOList TaskPO列表
     * @return 任务信息列表
     */
    List<TaskInfo> toTaskInfoList(List<TaskPO> taskPOList);

    /**
     * TaskInfo列表转TaskPO列表
     *
     * @param taskInfoList 任务信息列表
     * @return TaskPO列表
     */
    List<TaskPO> toTaskPOList(List<TaskInfo> taskInfoList);
}
