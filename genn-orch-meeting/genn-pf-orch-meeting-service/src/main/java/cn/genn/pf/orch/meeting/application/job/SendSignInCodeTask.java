package cn.genn.pf.orch.meeting.application.job;

import cn.genn.pf.orch.meeting.application.service.action.MeetingActionService;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class SendSignInCodeTask {

    @Resource
    private MeetingMapper meetingMapper;
    @Resource
    private MeetingActionService meetingActionService;
    /**
     * 发送签到码
     */
    @Scheduled(fixedRate = 1L * 60 * 1000) //每5分钟执行一次
    public void executeTask() {
        List<MeetingPO> meetingPOS = meetingMapper.queryNoStartStartMeeting();
        for (MeetingPO meetingPO : meetingPOS) {
            meetingActionService.sendSignInCode(meetingPO.getId());
        }
    }
}
