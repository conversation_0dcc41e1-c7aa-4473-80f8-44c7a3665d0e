package cn.genn.pf.orch.meeting.domain.schedule.repository;

import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingAttendUser;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议仓储层接口
 * @date 2025-01-02
 */
public interface IMeetingRepository {

    void insert(MeetingAgg meetingAgg);

    MeetingAgg findByScheduleId(Long scheduleId);

    void start(MeetingAgg meetingAgg);

    void end(MeetingAgg meetingAgg);

    MeetingAgg findById(Long id);

    List<MeetingAgg> findEvaluateReminders();

    void uploadRecord(MeetingAgg meetingAgg);

    void uploadEvaluate(MeetingAgg meetingAgg);

    void delete(MeetingAgg meetingAgg);

    MeetingAgg findByFsMeetingId(String fsMeetingId);

    void recordReady(MeetingAgg meetingAgg);

    void signIn(MeetingAttendUser meetingAttendUser);

    void evaluateReminder(Long meetingId);

    void updateExtend(MeetingAgg meetingAgg);

    void updateInfo(MeetingAgg meetingAgg);

    List<Long> findNotUploadedMeetingIds();

    void saveRecordDraft(MeetingAgg meetingAgg);

    void saveEvaluateDraft(MeetingAgg meetingAgg);

    void deleteRecordDraft(Long meetingId);

    void deleteEvaluateDraft(Long meetingId);
}
