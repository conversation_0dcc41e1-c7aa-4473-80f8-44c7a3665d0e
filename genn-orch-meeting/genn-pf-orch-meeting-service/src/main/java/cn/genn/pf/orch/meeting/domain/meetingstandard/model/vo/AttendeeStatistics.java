package cn.genn.pf.orch.meeting.domain.meetingstandard.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 参会人数统计值对象
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendeeStatistics {

    /**
     * 会议规划总数
     */
    private Integer totalPlans;

    /**
     * 最小参会人数
     */
    private Integer minAttendees;

    /**
     * 最大参会人数
     */
    private Integer maxAttendees;

    /**
     * 平均参会人数
     */
    private Double avgAttendees;

    /**
     * 参会人数分布统计
     */
    private AttendeeDistribution distribution;

    /**
     * 参会人数分布统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttendeeDistribution {
        /**
         * 1-5人的会议数量
         */
        private Integer smallMeetings;

        /**
         * 6-15人的会议数量
         */
        private Integer mediumMeetings;

        /**
         * 16-30人的会议数量
         */
        private Integer largeMeetings;

        /**
         * 30人以上的会议数量
         */
        private Integer extraLargeMeetings;
    }
} 