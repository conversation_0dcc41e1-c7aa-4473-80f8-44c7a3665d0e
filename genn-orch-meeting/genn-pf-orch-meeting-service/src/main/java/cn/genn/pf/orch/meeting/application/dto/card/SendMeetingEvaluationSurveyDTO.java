package cn.genn.pf.orch.meeting.application.dto.card;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 会议评价调查卡片DTO
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendMeetingEvaluationSurveyDTO {
    
    private Long meetingId;
    private String meetingName;
    private String meetingType;
    private String meetingTime;
    private String openId;
    private String evaluatorName;
} 