package cn.genn.pf.orch.meeting.infrastructure.service;

import cn.genn.pf.orch.meeting.infrastructure.constant.AgentConstants;
import cn.genn.pf.orch.meeting.infrastructure.properties.MeetingSeverProperties;
import cn.genn.pf.orch.meeting.interfaces.dto.KbRepoFileDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description 外部文件上传服务
 */
@Slf4j
@Service
public class ExternalFileUploadService {

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private MeetingSeverProperties properties;
    @Resource
    private ObjectMapper objectMapper;

    /**
     * 文件上传接口路径
     */
    private static final String UPLOAD_PATH = "/api/ai/kbRepoFile/uploadWithoutLogin";

    /**
     * 上传文件到外部服务
     *
     * @param file 要上传的文件
     * @return 文件信息DTO
     */
    public KbRepoFileDTO uploadFile(MultipartFile file) {
        log.info("开始上传文件到外部服务，文件名：{}，大小：{} bytes",
                file.getOriginalFilename(), file.getSize());

        try {
            // 构建上传URL
            String uploadUrl = properties.getAgent().getInvokeDomain() + UPLOAD_PATH;
            log.info("文件上传URL：{}", uploadUrl);

            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            headers.set("Authorization", AgentConstants.SUMMARY_AUTHORIZATION);

            // 构建请求体
            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();

            // 将MultipartFile转换为ByteArrayResource
            ByteArrayResource fileResource = new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }
            };

            body.add("file", fileResource);

            HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(
                    uploadUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.info("文件上传成功，响应：{}", responseBody);

                // 解析响应为KbRepoFileDTO
                KbRepoFileDTO result = parseUploadResponse(responseBody);

                if (result != null) {
                    log.info("文件上传解析成功，完整结果：{}", result);
                    log.info("关键字段 - 文件ID：{}，URL：{}，文件名：{}，ContentType：{}",
                            result.getExternalFileId(), result.getExternalFileUrl(),
                            result.getFileName(), result.getContentType());
                    return result;
                } else {
                    log.error("解析上传响应失败，响应内容：{}", responseBody);
                    return null;
                }
            } else {
                log.error("文件上传失败，状态码：{}，响应：{}",
                        response.getStatusCode(), response.getBody());
                return null;
            }

        } catch (Exception e) {
            log.error("文件上传异常，文件名：{}", file.getOriginalFilename(), e);
            return null;
        }
    }

    /**
     * 解析上传响应
     *
     * @param responseBody 响应体
     * @return KbRepoFileDTO
     */
    private KbRepoFileDTO parseUploadResponse(String responseBody) {
        log.info("=== 开始解析上传响应 ===");
        log.info("响应内容长度：{}", responseBody.length());
        log.info("响应内容：{}", responseBody);

        try {
            // 解析为JsonNode
            com.fasterxml.jackson.databind.JsonNode rootNode = objectMapper.readTree(responseBody);
            log.info("JSON解析成功，根节点类型：{}", rootNode.getNodeType());

            // 检查success字段
            boolean isSuccess = rootNode.has("success") && rootNode.get("success").asBoolean();
            log.info("响应success状态：{}", isSuccess);

            if (!isSuccess) {
                log.error("响应状态为失败，完整响应：{}", rootNode);
                return null;
            }

            // 检查data字段
            if (!rootNode.has("data")) {
                log.error("响应中没有data字段");
                return null;
            }

            com.fasterxml.jackson.databind.JsonNode dataNode = rootNode.get("data");
            if (dataNode.isNull()) {
                log.error("data字段为null");
                return null;
            }

            log.info("=== 找到data字段 ===");
            log.info("data节点类型：{}", dataNode.getNodeType());
            log.info("data内容：{}", dataNode);

            // 手动检查data中的关键字段
            log.info("=== 检查data中的字段 ===");
            log.info("repoId: {}", dataNode.has("repoId") ? dataNode.get("repoId") : "缺失");
            log.info("collectionId: {}", dataNode.has("collectionId") ? dataNode.get("collectionId") : "缺失");
            log.info("filePlatform: {}", dataNode.has("filePlatform") ? dataNode.get("filePlatform") : "缺失");
            log.info("externalFileId: {}", dataNode.has("externalFileId") ? dataNode.get("externalFileId") : "缺失");
            log.info("externalFileUrl: {}", dataNode.has("externalFileUrl") ? dataNode.get("externalFileUrl") : "缺失");
            log.info("length: {}", dataNode.has("length") ? dataNode.get("length") : "缺失");
            log.info("fileName: {}", dataNode.has("fileName") ? dataNode.get("fileName") : "缺失");
            log.info("contentType: {}", dataNode.has("contentType") ? dataNode.get("contentType") : "缺失");
            log.info("metadata: {}", dataNode.has("metadata") ? dataNode.get("metadata") : "缺失");

            // 解析data节点为KbRepoFileDTO
            KbRepoFileDTO result = objectMapper.treeToValue(dataNode, KbRepoFileDTO.class);
            log.info("=== 解析结果 ===");
            log.info("解析后的对象：{}", result);

            if (result != null) {
                log.info("=== 验证解析后的字段 ===");
                log.info("result.repoId: {}", result.getRepoId());
                log.info("result.collectionId: {}", result.getCollectionId());
                log.info("result.filePlatform: {}", result.getFilePlatform());
                log.info("result.externalFileId: {}", result.getExternalFileId());
                log.info("result.externalFileUrl: {}", result.getExternalFileUrl());
                log.info("result.length: {}", result.getLength());
                log.info("result.fileName: {}", result.getFileName());
                log.info("result.contentType: {}", result.getContentType());
                log.info("result.metadata: {}", result.getMetadata());

                // 验证关键字段
                if (result.getExternalFileUrl() != null && result.getFileName() != null) {
                    log.info("✅ 解析成功，关键字段验证通过");
                    return result;
                } else {
                    log.error("❌ 解析失败，关键字段缺失：url={}, fileName={}",
                            result.getExternalFileUrl(), result.getFileName());
                }
            } else {
                log.error("❌ 解析结果为null");
            }

            return null;

        } catch (Exception e) {
            log.error("❌ 解析上传响应异常", e);
            return null;
        }
    }
}
