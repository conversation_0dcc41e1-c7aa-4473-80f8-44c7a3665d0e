package cn.genn.pf.orch.meeting.infrastructure.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;


@Data
@RefreshScope
@ConfigurationProperties(prefix = "genn.meeting")
public class MeetingSeverProperties {

    /**
     * 鉴权
     */
    @NestedConfigurationProperty
    private SsoAuthProperties permission = new SsoAuthProperties();

    /**
     * 登录
     */
    @NestedConfigurationProperty
    private LoginProperties login = new LoginProperties();

    /**
     * 卡片通知
     */
    @NestedConfigurationProperty
    private CardSendProperties cardSend = new CardSendProperties();

    @NestedConfigurationProperty
    private AgentProperties agent = new AgentProperties();
}
