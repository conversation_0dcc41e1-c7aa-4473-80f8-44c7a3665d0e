package cn.genn.pf.orch.meeting.domain.meeting.listener;

import cn.genn.pf.orch.meeting.application.service.action.MeetingActionService;
import cn.genn.pf.orch.meeting.domain.meeting.model.event.SignInCodeEvent;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventSyncListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class SignInfCodeListener extends SpringEventSyncListener<SignInCodeEvent> {

    @Resource
    private MeetingActionService meetingActionService;
    @Override
    protected void onMessage (SignInCodeEvent event){
        Long meetingId= (Long) event.getSource();
        log.info("receive SignInfCode event meetingId: {}", meetingId);
        meetingActionService.sendSignInCode(meetingId);
    }
}
