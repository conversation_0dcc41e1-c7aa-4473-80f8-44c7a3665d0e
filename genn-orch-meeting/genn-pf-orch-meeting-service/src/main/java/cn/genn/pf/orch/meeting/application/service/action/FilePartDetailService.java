package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.FilePartDetailMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.FileDetailPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.FilePartDetailPO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.SneakyThrows;
import org.dromara.x.file.storage.core.upload.FilePartInfo;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 用来将文件分片上传记录保存到数据库
 * 目前仅手动分片分片上传时使用
 */
@Service
public class FilePartDetailService extends ServiceImpl<FilePartDetailMapper, FilePartDetailPO> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 保存文件分片信息
     * @param info 文件分片信息
     */
    @SneakyThrows
    public void saveFilePart(FilePartInfo info) {
        FilePartDetailPO detail = toFilePartDetail(info);
        if (save(detail)) {
            info.setId(detail.getId());
        }
    }

    /**
     * 删除文件分片信息
     */
    public void deleteFilePartByUploadId(String uploadId) {
        remove(new QueryWrapper<FilePartDetailPO>().eq(FileDetailPO.COL_UPLOAD_ID, uploadId));
    }

    /**
     * 将 FilePartInfo 转成 FilePartDetail
     * @param info 文件分片信息
     */
    public FilePartDetailPO toFilePartDetail(FilePartInfo info) throws JsonProcessingException {
        FilePartDetailPO detail = new FilePartDetailPO();
        detail.setPlatform(info.getPlatform());
        detail.setUploadId(info.getUploadId());
        detail.setETag(info.getETag());
        detail.setPartNumber(info.getPartNumber());
        detail.setPartSize(info.getPartSize());
        detail.setHashInfo(valueToJson(info.getHashInfo()));
        LocalDateTime createTime = info.getCreateTime().toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
        detail.setCreateTime(createTime);
        return detail;
    }

    /**
     * 将指定值转换成 json 字符串
     */
    public String valueToJson(Object value) throws JsonProcessingException {
        if (value == null) return null;
        return objectMapper.writeValueAsString(value);
    }
}

