package cn.genn.pf.orch.meeting.domain.newmeeting.repository;

import cn.genn.pf.orch.meeting.domain.newmeeting.model.entity.NewMeeting;
import cn.genn.pf.orch.meeting.interfaces.enums.NewMeetingStatusEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议仓储接口
 * @date 2025-01-24
 */
public interface INewMeetingRepository {

    /**
     * 保存会议
     */
    void save(NewMeeting meeting);

    /**
     * 更新会议
     */
    void update(NewMeeting meeting);

    /**
     * 根据ID查询会议
     */
    NewMeeting findById(Long id);

    /**
     * 根据飞书日程事件ID查询会议
     */
    NewMeeting findByFsCalendarEventId(String fsCalendarEventId);

    /**
     * 根据飞书会议ID查询会议
     */
    NewMeeting findByFsMeetingId(String fsMeetingId);

    /**
     * 删除会议
     */
    void deleteById(Long id);

    /**
     * 分页查询会议
     */
    List<NewMeeting> findPage(String meetingName, Integer status, Integer priorityLevel,
                             Long meetingPlanId, Long meetingStandardId,
                             String startTimeFrom, String startTimeTo,
                             String createUserId, Integer pageNum, Integer pageSize);

    /**
     * 统计总数
     */
    Long count(String meetingName, Integer status, Integer priorityLevel,
               Long meetingPlanId, Long meetingStandardId,
               String startTimeFrom, String startTimeTo,
               String createUserId);

    /**
     * 更新会议状态
     */
    void updateStatus(Long id, NewMeetingStatusEnum status);

    /**
     * 更新飞书相关信息
     */
    void updateFeishuInfo(Long id, String fsCalendarEventId, String fsMeetingId, String meetingUrl);

    /**
     * 更新会议编号和飞书会议ID
     */
    void updateMeetingNoAndFsMeetingId(Long id, String meetingNo, String fsMeetingId);

    /**
     * 更新会议编号、飞书会议ID和会议链接
     */
    void updateMeetingInfo(Long id, String meetingNo, String fsMeetingId, String meetingUrl);

    /**
     * 更新妙计链接
     */
    void updateMinuteUrl(Long id, String minuteUrl);
}
