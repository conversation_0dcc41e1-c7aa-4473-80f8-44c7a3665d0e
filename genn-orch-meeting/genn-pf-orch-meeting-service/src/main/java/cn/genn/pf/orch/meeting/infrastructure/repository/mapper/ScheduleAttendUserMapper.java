package cn.genn.pf.orch.meeting.infrastructure.repository.mapper;

import cn.genn.pf.orch.meeting.infrastructure.repository.po.ScheduleAttendUserPO;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ScheduleAttendUserMapper extends BaseMapper<ScheduleAttendUserPO> {

    void batchInsertAttendUser(@Param("list") List<ScheduleAttendUserPO> list);

    void batchUpdateAttendUser(@Param("scheduleId") Long scheduleId, @Param("list") List<ScheduleAttendUserPO> list);

    default List<ScheduleAttendUserPO> selectScheduleAndAttendState(Long scheduleId, AttendUserStateEnum state){
        LambdaQueryWrapper<ScheduleAttendUserPO> wrapper = Wrappers.lambdaQuery(ScheduleAttendUserPO.class)
            .eq(ScheduleAttendUserPO::getScheduleId, scheduleId)
            .eq(ScheduleAttendUserPO::getAttendUserState, state);
        return selectList(wrapper);
    }

    default List<ScheduleAttendUserPO> selectScheduleAndRole(Long scheduleId, AttendUserRoleEnum role){
        LambdaQueryWrapper<ScheduleAttendUserPO> wrapper = Wrappers.lambdaQuery(ScheduleAttendUserPO.class)
            .eq(ScheduleAttendUserPO::getScheduleId, scheduleId)
            .eq(ScheduleAttendUserPO::getAttendUserRole, role);
        return selectList(wrapper);
    }

}
