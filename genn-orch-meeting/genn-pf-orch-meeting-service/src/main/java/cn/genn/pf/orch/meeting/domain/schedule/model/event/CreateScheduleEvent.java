package cn.genn.pf.orch.meeting.domain.schedule.model.event;

import cn.genn.pf.orch.meeting.domain.schedule.model.entity.Schedule;
import cn.genn.spring.boot.starter.event.spring.model.SpringBaseEvent;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 创建日程事件
 */
public class CreateScheduleEvent extends SpringBaseEvent {

    public CreateScheduleEvent(CreateScheduleData data) {
        super(data);
    }

    @Data
    @Builder
    public static class CreateScheduleData {

        Schedule schedule;
    }
}
