package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.pf.orch.meeting.application.service.action.MeetingStandardActionService;
import cn.genn.pf.orch.meeting.application.service.query.MeetingStandardQueryService;
import cn.genn.pf.orch.meeting.domain.meetingstandard.model.vo.AttendeeStatistics;
import cn.genn.pf.orch.meeting.domain.meetingstandard.service.MeetingStandardDomainService;
import cn.genn.pf.orch.meeting.interfaces.command.meetingstandard.MeetingStandardCreateCommand;
import cn.genn.pf.orch.meeting.interfaces.command.meetingstandard.MeetingStandardUpdateCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingstandard.MeetingStandardDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.query.meetingstandard.MeetingStandardQuery;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议标准控制器
 * @date 2025-01-24
 */
@Api(tags = "会议标准")
@RestController
@RequestMapping("/meeting-standard")
@RequiredArgsConstructor
public class MeetingStandardController {

    private final MeetingStandardQueryService meetingStandardQueryService;
    private final MeetingStandardActionService meetingStandardActionService;

    @PostMapping("/list")
    @ApiOperation("查询启用的会议标准列表")
    public List<MeetingStandardDTO> list() {
        return meetingStandardQueryService.listEnabled();
    }

    @GetMapping("/detail")
    @ApiOperation("查询会议标准详情")
    public MeetingStandardDTO detail(@RequestParam("id") Long id) {
        return meetingStandardQueryService.getById(id);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询会议标准")
    public IPage<MeetingStandardDTO> pageQuery(@RequestBody MeetingStandardQuery query) {
        return meetingStandardQueryService.pageQuery(query);
    }

    @PostMapping("/create")
    @ApiOperation("创建会议标准")
    public void create(@Valid @RequestBody MeetingStandardCreateCommand command) {
        meetingStandardActionService.createMeetingStandard(command);
    }

    @PutMapping("/update")
    @ApiOperation("更新会议标准")
    public void update(@Valid @RequestBody MeetingStandardUpdateCommand command) {
        meetingStandardActionService.updateMeetingStandard(command);
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除会议标准")
    public void delete(@PathVariable("id") Long id) {
        meetingStandardActionService.deleteMeetingStandard(id);
    }

    @GetMapping("/roles")
    @ApiOperation("获取所有会议角色")
    public List<RoleInfo> getRoles() {
        return Arrays.stream(MeetingRoleEnum.values())
            .map(role -> new RoleInfo(role.getCode(), role.getName()))
            .collect(Collectors.toList());
    }

    @GetMapping("/attendee-statistics/{id}")
    @ApiOperation("获取会议标准的参会人数统计")
    public AttendeeStatistics getAttendeeStatistics(@PathVariable("id") Long id) {
        return meetingStandardQueryService.getAttendeeStatistics(id);
    }

    @GetMapping("/attendee-suggestion/{id}")
    @ApiOperation("获取会议标准的参会人数建议")
    public MeetingStandardDomainService.AttendeeSuggestion getAttendeeSuggestion(@PathVariable("id") Long id) {
        return meetingStandardQueryService.getAttendeeSuggestion(id);
    }

    /**
     * 角色信息
     */
    public static class RoleInfo {
        private String code;
        private String name;

        public RoleInfo(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
