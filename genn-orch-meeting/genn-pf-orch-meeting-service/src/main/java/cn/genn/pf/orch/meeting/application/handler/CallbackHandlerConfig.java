package cn.genn.pf.orch.meeting.application.handler;

import cn.genn.pf.orch.meeting.infrastructure.enums.EventCallbackEnum;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 回调处理器配置
 * @date 2024-12-25
 */
@Configuration
public class CallbackHandlerConfig {

    @Resource
    private List<CallbackHandler> callbackHandlers;

    @Bean
    public Map<EventCallbackEnum, CallbackHandler> callbackHandlerMap() {
        return callbackHandlers.stream().collect(
                java.util.stream.Collectors.toMap(CallbackHandler::event, java.util.function.Function.identity()));
    }
}
