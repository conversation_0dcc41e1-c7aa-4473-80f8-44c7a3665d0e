package cn.genn.pf.orch.meeting.domain.meeting.listener;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.schedule.model.event.DeleteScheduleEvent;
import cn.genn.pf.orch.meeting.domain.schedule.service.MeetingDomainService;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventSyncListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class DeleteScheduleListener extends SpringEventSyncListener<DeleteScheduleEvent> {

    @Resource
    private MeetingDomainService meetingDomainService;
    @Override
    protected void onMessage(DeleteScheduleEvent event) {

        DeleteScheduleEvent.DeleteScheduleData deleteScheduleData = (DeleteScheduleEvent.DeleteScheduleData)event.getSource();

        log.info("receive create schedule event context: {}", JsonUtils.toJson(deleteScheduleData));

        MeetingAgg meetingAgg = meetingDomainService.findByScheduleId(deleteScheduleData.getScheduleId());
        meetingDomainService.delete(meetingAgg);
    }
}
