package cn.genn.pf.orch.meeting.infrastructure.ai.agent.request;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 智能体请求
 * @date 2025-04-10
 */
@Data
@Builder
public class AgentRequest {

    /**
     * 消息列表
     */
    private List<MessageDTO> messages;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 聊天ID
     */
    private String chatId;

    /**
     * 平台（feishu, wx_mp）
     */
    private String platform;

    /**
     * 渠道唯一标识
     */
    private String channelUniqueIdentifier;

    /**
     * 是否返回详情
     */
    private Boolean detail;

    /**
     * 是否流式响应
     */
    private Boolean stream;

    /**
     * 全局变量
     */
    private Map<String, Object> variables;

    // 消息对象定义
    @Data
    @Builder
    public static class MessageDTO {
        /**
         * 消息唯一ID
         */
        private String dataId;

        /**
         * 是否在UI中隐藏
         */
        private Boolean hideInUI;

        /**
         * 角色 (user/assistant)
         */
        private String role;

        /**
         * 消息内容列表
         */
        private List<Content> content;
    }

    @Data
    @Builder
    public static class Content {
        /**
         * 内容类型：text, image_url, file_url
         */
        private String type;

        /**
         * 文本内容（type=text时使用）
         */
        private String text;

        /**
         * 图片URL对象（type=image_url时使用）
         */
        private ImageUrl imageUrl;

        /**
         * 文件URL（type=file_url时使用）
         */
        private String url;

        /**
         * 文件名（type=file_url时使用）
         */
        private String name;

        /**
         * 原始数据信息（type=file_url时使用）
         */
        private RawData rawData;
    }

    @Data
    @Builder
    public static class ImageUrl {

        private String url;
    }

    @Data
    @Builder
    public static class RawData {
        /**
         * 仓库ID
         */
        private Integer repoId;

        /**
         * 集合ID
         */
        private Integer collectionId;

        /**
         * 文件平台
         */
        private String filePlatform;

        /**
         * 外部文件ID
         */
        private String externalFileId;

        /**
         * 外部文件URL
         */
        private String externalFileUrl;

        /**
         * 文件长度
         */
        private Long length;

        /**
         * 文件名
         */
        private String fileName;

        /**
         * 内容类型
         */
        private String contentType;

        /**
         * 元数据
         */
        private Object metadata;
    }
}
