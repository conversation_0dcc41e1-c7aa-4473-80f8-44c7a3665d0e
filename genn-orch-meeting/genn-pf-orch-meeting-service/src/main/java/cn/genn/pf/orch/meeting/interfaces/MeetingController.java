package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.application.dto.card.SendMeetingNotificationDTO;
import cn.genn.pf.orch.meeting.application.service.action.CardSendActionService;
import cn.genn.pf.orch.meeting.application.service.action.MeetingActionService;
import cn.genn.pf.orch.meeting.application.service.query.MeetingQueryService;
import cn.genn.pf.orch.meeting.interfaces.command.*;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingDetailDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingListDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingStatusEnum;
import cn.genn.pf.orch.meeting.interfaces.query.MeetingDetailQuery;
import cn.genn.pf.orch.meeting.interfaces.query.MeetingQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议控制器
 * @date 2024-12-31
 */
@Api(tags = "会议")
@RestController
@RequestMapping("/meeting")
public class MeetingController {

    @Resource
    private MeetingQueryService meetingQueryService;
    @Resource
    private MeetingActionService meetingActionService;
    @Resource
    private CardSendActionService cardSendActionService;

    @PostMapping("/mini/page")
    @ApiOperation(value = "手机端会议列表")
    public PageResultDTO<MeetingListDTO> miniPage(@RequestBody MeetingQuery meetingQuery) {
        return meetingQueryService.miniPage(meetingQuery);
    }

    @PostMapping("page")
    @ApiOperation(value = "会议列表")
    public PageResultDTO<MeetingListDTO> page(@RequestBody MeetingQuery meetingQuery) {
        PageResultDTO<MeetingListDTO> page = meetingQueryService.page(meetingQuery);
        //隐藏未开始和已结束的会议链接,避免一些飞书问题:1.未开始会议点击链接易触发开始会议事件;2.已结束会议点击链接可以重复开始会议
        for (MeetingListDTO record : page.getList()) {
            if(!MeetingStatusEnum.IN_PROCESS.equals(record.getStatus())){
                record.setMeetingUrl(null);
            }
        }
        return page;
    }

    @PostMapping("detail")
    @ApiOperation(value = "会议详情")
    public MeetingDetailDTO detail(@RequestBody MeetingDetailQuery query) {
        return meetingQueryService.detail(query);
    }

    @PostMapping("uploadRecord")
    @ApiOperation(value = "上传会议记录")
    public void uploadRecord(@RequestBody MeetingUploadRecordCommand command) {
        meetingActionService.uploadRecord(command);
    }

    @PostMapping("uploadEvaluate")
    @ApiOperation(value = "上传会议评价")
    public void uploadEvaluate(@RequestBody MeetingUploadEvaluateCommand command) {
        meetingActionService.uploadEvaluate(command);
    }

    @PostMapping("saveRecordDraft")
    @ApiOperation(value = "保存会议记录草稿")
    public void saveRecordDraft(@RequestBody MeetingUploadRecordCommand command) {
        meetingActionService.saveRecordDraft(command);
    }

    @PostMapping("saveEvaluateDraft")
    @ApiOperation(value = "保存会议评价草稿")
    public void saveEvaluateDraft(@RequestBody MeetingUploadEvaluateCommand command) {
        meetingActionService.saveEvaluateDraft(command);
    }

    @PostMapping("deleteRecordDraft")
    @ApiOperation(value = "删除会议记录草稿")
    public void deleteRecordDraft(@RequestBody IdQuery idQuery) {
        meetingActionService.deleteRecordDraft(idQuery.getId());
    }

    @PostMapping("deleteEvaluateDraft")
    @ApiOperation(value = "删除会议评价草稿")
    public void deleteEvaluateDraft(@RequestBody IdQuery idQuery) {
        meetingActionService.deleteEvaluateDraft(idQuery.getId());
    }

    @PostMapping("signIn")
    @ApiOperation(value = "签到")
    public void signIn(@Validated @RequestBody MeetingSignInCommand command) {
        meetingActionService.signIn(command);
    }

    @PostMapping("/notification/test")
    @ApiOperation(value = "测试会议提前通知卡片", notes = "发送模拟会议提前通知卡片消息，用于测试飞书卡片效果")
    public void testMeetingNotification(@RequestParam("openId") String openId) {
        // 创建模拟的会议通知信息
        SendMeetingNotificationDTO mockNotification = SendMeetingNotificationDTO.builder()
                .openId(openId)
                .meetingType("周例会")
                .meetingName("产品研发部周例会")
                .meetingTime("2024-01-15 14:00-15:30")
                .meetingLocation("会议室A101")
                .meetingDuration("1小时30分钟")
                .attendees(Arrays.asList("张三", "李四", "王五", "赵六"))
                .meetingDescription("本周工作总结与下周计划讨论，包括项目进度汇报、问题反馈及解决方案制定。")
                .priorityLevel("高")
                .noticeType("提前通知")
                .noticeTime("提前30分钟")
                .meetingUrl("https://meeting.example.com/detail/123")
                .build();

        cardSendActionService.sendMeetingNotification(mockNotification);
    }

    @PostMapping("/card/test")
    @ApiOperation(value = "测试卡片交互功能", notes = "发送带有交互按钮的测试卡片，用于测试卡片回调和禁用功能")
    public void testCardInteraction(@RequestParam("openId") String openId) {
        cardSendActionService.sendTestInteractiveCard(openId);
    }
}
