package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.pf.orch.meeting.infrastructure.ai.agent.AgentInvokeService;
import cn.genn.pf.orch.meeting.infrastructure.ai.agent.response.AgentCompleteRespDTO;
import cn.genn.pf.orch.meeting.infrastructure.service.ExternalFileUploadService;
import cn.genn.pf.orch.meeting.infrastructure.util.DebugUtils;
import cn.genn.pf.orch.meeting.interfaces.command.FileSummaryCommand;
import cn.genn.pf.orch.meeting.interfaces.command.FileUploadSummaryCommand;
import cn.genn.pf.orch.meeting.interfaces.command.MixedContentAnalysisCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.KbRepoFileDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description AI赋能服务
 */
@Slf4j
@Service
public class AiEmpowermentService {

    @Resource
    private AgentInvokeService agentInvokeService;
    @Resource
    private ExternalFileUploadService externalFileUploadService;

    /**
     * 默认的文件汇总智能体应用ID
     */
    private static final String DEFAULT_FILE_SUMMARY_APP_ID = "6882b543e85ec5e2db287d19";

    /**
     * 文件汇总提炼
     *
     * @param command 文件汇总命令
     * @return 汇总结果
     */
    public String fileSummary(FileSummaryCommand command) {
        log.info("开始文件汇总提炼，文件URL：{}，文件名：{}", command.getFileUrl(), command.getFileName());

        try {
            // 确定使用的智能体应用ID
            String appId = StringUtils.hasText(command.getAppId()) ?
                command.getAppId() : DEFAULT_FILE_SUMMARY_APP_ID;

            // 构建文件名
            String fileName = StringUtils.hasText(command.getFileName()) ?
                command.getFileName() : extractFileNameFromUrl(command.getFileUrl());

            String useQuestion = StringUtils.hasText(command.getCustomPrompt()) ? command.getCustomPrompt() : "请对这个文档进行详细的汇总，重点关注关键信息和要点。";

            // 调用智能体进行文件汇总
            AgentCompleteRespDTO response;

            // 如果有图片，使用混合内容分析
            if (command.getImageUrls() != null && !command.getImageUrls().isEmpty()) {
                response = agentInvokeService.invokeMixedContentStreamGetAnswer(
                        appId,
                        useQuestion,
                        command.getImageUrls(),
                        command.getFileUrl(),
                        fileName
                );
            } else {
                // 只有文件，使用文件分析
                response = agentInvokeService.invokeFileStreamGetAnswer(
                        appId,
                        command.getFileUrl(),
                        fileName,
                        useQuestion
                );
            }

            if (response != null && StringUtils.hasText(response.getAnswer())) {
                log.info("文件汇总提炼成功，文件URL：{}，结果长度：{}",
                    command.getFileUrl(), response.getAnswer().length());
                return response.getAnswer();
            } else {
                log.warn("文件汇总提炼返回空结果，文件URL：{}", command.getFileUrl());
                return "文件汇总提炼失败，请稍后重试";
            }

        } catch (Exception e) {
            log.error("文件汇总提炼失败，文件URL：{}", command.getFileUrl(), e);
            return "文件汇总提炼失败：" + e.getMessage();
        }
    }

    /**
     * 从URL中提取文件名
     *
     * @param fileUrl 文件URL
     * @return 文件名
     */
    private String extractFileNameFromUrl(String fileUrl) {
        if (!StringUtils.hasText(fileUrl)) {
            return "unknown_file";
        }

        try {
            // 从URL中提取文件名
            String[] parts = fileUrl.split("/");
            String fileName = parts[parts.length - 1];

            // 移除查询参数
            if (fileName.contains("?")) {
                fileName = fileName.substring(0, fileName.indexOf("?"));
            }

            return StringUtils.hasText(fileName) ? fileName : "unknown_file";
        } catch (Exception e) {
            log.warn("从URL提取文件名失败：{}", fileUrl, e);
            return "unknown_file";
        }
    }

    /**
     * 混合内容分析（支持文本+图片+文件的组合分析）
     *
     * @param command 混合内容分析命令
     * @return 分析结果
     */
    public String mixedContentAnalysis(MixedContentAnalysisCommand command) {
        log.info("开始混合内容分析，文本：{}，图片数量：{}，文件URL：{}",
                command.getTextMessage(),
                command.getImageUrls() != null ? command.getImageUrls().size() : 0,
                command.getFileUrl());

        try {
            // 确定使用的智能体应用ID
            String appId = StringUtils.hasText(command.getAppId()) ?
                          command.getAppId() : DEFAULT_FILE_SUMMARY_APP_ID;

            // 构建文件名
            String fileName = null;
            if (StringUtils.hasText(command.getFileUrl())) {
                fileName = StringUtils.hasText(command.getFileName()) ?
                          command.getFileName() : extractFileNameFromUrl(command.getFileUrl());
            }

            // 调用智能体进行混合内容分析
            AgentCompleteRespDTO response = agentInvokeService.invokeMixedContentStreamGetAnswer(
                    appId,
                    command.getTextMessage(),
                    command.getImageUrls(),
                    command.getFileUrl(),
                    fileName
            );

            if (response != null && StringUtils.hasText(response.getAnswer())) {
                log.info("混合内容分析成功，结果长度：{}", response.getAnswer().length());
                return response.getAnswer();
            } else {
                log.warn("混合内容分析返回空结果");
                return "混合内容分析失败，请稍后重试";
            }

        } catch (Exception e) {
            log.error("混合内容分析失败", e);
            return "混合内容分析失败：" + e.getMessage();
        }
    }

    /**
     * 文件上传汇总提炼
     *
     * @param command 文件上传汇总命令
     * @return 汇总结果
     */
    public String fileUploadSummary(FileUploadSummaryCommand command) {
        log.info("开始文件上传汇总提炼，文件名：{}", command.getFile().getOriginalFilename());

        try {
            // 调用外部接口上传文件
            KbRepoFileDTO fileInfo = externalFileUploadService.uploadFile(command.getFile());
            if (fileInfo == null) {
                log.error("文件上传失败");
                return "文件上传失败，请稍后重试";
            }

            // 调试：检查上传返回的文件信息
            DebugUtils.printJson("上传返回的KbRepoFileDTO", fileInfo);
            DebugUtils.checkNullFields("KbRepoFileDTO", fileInfo);

            // 确定使用的智能体应用ID
            String appId = StringUtils.hasText(command.getAppId()) ?
                          command.getAppId() : DEFAULT_FILE_SUMMARY_APP_ID;

            // 构建自定义提示词
            String customPrompt = StringUtils.hasText(command.getCustomPrompt()) ?
                                 command.getCustomPrompt() : "请对这个文档进行详细的汇总，重点关注关键信息和要点。";

            // 构建RawData
            AgentInvokeService.RawData rawData = buildRawDataFromKbRepoFileDTO(fileInfo, command.getFilePlatform());
            DebugUtils.printJson("构建的RawData", rawData);

            // 调试：检查传递给智能体的参数
            log.info("传递给智能体的参数：appId={}, fileUrl={}, fileName={}, customPrompt={}",
                    appId, fileInfo.getExternalFileUrl(), fileInfo.getFileName(), customPrompt);

            // 调用智能体进行文件汇总（带rawData信息）
            AgentCompleteRespDTO response = agentInvokeService.invokeFileWithRawDataStreamGetAnswer(
                    appId,
                    fileInfo.getExternalFileUrl(),
                    fileInfo.getFileName(),
                    customPrompt,
                    rawData
            );

            if (response != null && StringUtils.hasText(response.getAnswer())) {
                log.info("文件上传汇总提炼成功，文件ID：{}，结果长度：{}",
                        fileInfo.getExternalFileId(), response.getAnswer().length());
                return response.getAnswer();
            } else {
                log.warn("文件上传汇总提炼返回空结果，文件ID：{}", fileInfo.getExternalFileId());
                return "文件汇总提炼失败，请稍后重试";
            }

        } catch (Exception e) {
            log.error("文件上传汇总提炼失败，文件名：{}", command.getFile().getOriginalFilename(), e);
            return "文件上传汇总提炼失败：" + e.getMessage();
        }
    }

    /**
     * 从KbRepoFileDTO构建RawData
     *
     * @param kbRepoFileDTO 知识库文件信息
     * @param filePlatform 文件平台（可选，用于覆盖）
     * @return RawData对象
     */
    private AgentInvokeService.RawData buildRawDataFromKbRepoFileDTO(KbRepoFileDTO kbRepoFileDTO, String filePlatform) {
        log.info("=== 构建RawData开始 ===");
        log.info("输入的kbRepoFileDTO：{}", kbRepoFileDTO);
        log.info("输入的filePlatform：{}", filePlatform);

        if (kbRepoFileDTO == null) {
            log.error("kbRepoFileDTO为null，无法构建RawData");
            return null;
        }

        // 详细检查每个字段
        log.info("检查kbRepoFileDTO字段：");
        log.info("  repoId: {}", kbRepoFileDTO.getRepoId());
        log.info("  collectionId: {}", kbRepoFileDTO.getCollectionId());
        log.info("  filePlatform: {}", kbRepoFileDTO.getFilePlatform());
        log.info("  externalFileId: {}", kbRepoFileDTO.getExternalFileId());
        log.info("  externalFileUrl: {}", kbRepoFileDTO.getExternalFileUrl());
        log.info("  length: {}", kbRepoFileDTO.getLength());
        log.info("  fileName: {}", kbRepoFileDTO.getFileName());
        log.info("  contentType: {}", kbRepoFileDTO.getContentType());
        log.info("  metadata: {}", kbRepoFileDTO.getMetadata());

        AgentInvokeService.RawData rawData = AgentInvokeService.RawData.builder()
                .repoId(kbRepoFileDTO.getRepoId() != null ? kbRepoFileDTO.getRepoId().intValue() : 0)
                .collectionId(kbRepoFileDTO.getCollectionId() != null ? kbRepoFileDTO.getCollectionId().intValue() : 0)
                .filePlatform(StringUtils.hasText(filePlatform) ? filePlatform : kbRepoFileDTO.getFilePlatform())
                .externalFileId(kbRepoFileDTO.getExternalFileId())
                .externalFileUrl(kbRepoFileDTO.getExternalFileUrl())
                .length(kbRepoFileDTO.getLength() != null ? kbRepoFileDTO.getLength().longValue() : 0L)
                .fileName(kbRepoFileDTO.getFileName())
                .contentType(kbRepoFileDTO.getContentType())
                .metadata(kbRepoFileDTO.getMetadata())
                .build();

        log.info("=== 构建的RawData ===");
        log.info("RawData: {}", rawData);
        log.info("=== 构建RawData完成 ===");
        return rawData;
    }
}
