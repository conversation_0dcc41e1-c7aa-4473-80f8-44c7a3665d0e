package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.core.exception.BusinessException;
import cn.genn.pf.orch.meeting.application.assembler.ScheduleAssembler;
import cn.genn.pf.orch.meeting.application.service.query.BusinessMeetingQueryService;
import cn.genn.pf.orch.meeting.application.service.query.TeamQueryService;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.AttendUser;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.Schedule;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.ScheduleConfig;
import cn.genn.pf.orch.meeting.domain.schedule.model.event.CreateScheduleEvent;
import cn.genn.pf.orch.meeting.domain.schedule.model.event.DeleteScheduleEvent;
import cn.genn.pf.orch.meeting.domain.schedule.model.event.UpdateScheduleEvent;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IMeetingRepository;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IScheduleRepository;
import cn.genn.pf.orch.meeting.domain.schedule.service.ScheduleDomainService;
import cn.genn.pf.orch.meeting.infrastructure.config.CurrentUserHolder;
import cn.genn.pf.orch.meeting.infrastructure.exception.MessageCode;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.ScheduleAttendUserPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.SchedulePO;
import cn.genn.pf.orch.meeting.interfaces.command.ScheduleCommand;
import cn.genn.pf.orch.meeting.interfaces.command.ScheduleConfigCommand;
import cn.genn.pf.orch.meeting.interfaces.command.UpdateScheduleCommand;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingStatusEnum;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventPublish;
import cn.genn.third.feishu.app.FeishuAppClient;
import cn.genn.third.feishu.app.config.FeishuAppContext;
import cn.genn.third.feishu.app.model.*;
import cn.hutool.core.util.ObjUtil;
import com.lark.oapi.service.calendar.v4.model.CreateCalendarEventRespBody;
import com.lark.oapi.service.calendar.v4.model.PatchCalendarEventRespBody;
import com.lark.oapi.service.calendar.v4.model.UserCalendar;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 日程会议操作服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ScheduleActionService {

    private final ScheduleDomainService scheduleDomainService;
    private final IScheduleRepository scheduleRepository;
    private final ScheduleAssembler scheduleAssembler;
    private final IMeetingRepository meetingRepository;
    private final BusinessMeetingQueryService businessMeetingQueryService;
    private final TeamQueryService teamQueryService;
    private final FeishuAppClient feishuAppClient;
    private final SpringEventPublish springEventPublish;
    private final AsyncService asyncService;

    public void addConfig(ScheduleConfigCommand command) {
        ScheduleConfig scheduleConfig = scheduleAssembler.toScheduleConfig(command);
        scheduleDomainService.addConfig(scheduleConfig);
    }

    public void updateConfig(ScheduleConfigCommand command) {
        ScheduleConfig scheduleConfig = scheduleAssembler.toScheduleConfig(command);
        scheduleDomainService.updateConfig(scheduleConfig);
    }

    @Transactional
    public void add(ScheduleCommand command) {
        if (Objects.isNull(command.getAllDay()) && (Objects.isNull(command.getStartTime()) || Objects.isNull(command.getEndTime()))) {
            throw new BusinessException(MessageCode.START_END_TIME_NOT_EXIST);
        }
        // 参会人员重复校验
        HashSet<String> userIds = new HashSet<>();
        command.getAttendUsers().forEach(attendUserCommand -> {
            if (!userIds.add(attendUserCommand.getAttendUserId())) {
                throw new BusinessException(MessageCode.EXIST_PERSONNEL_DUPLICATION, attendUserCommand.getAttendUserName());
            }
        });

        command.setScheduleNamePrefix(teamQueryService.getScheduleName(command.getTeamId(), command.getBusinessMeetingId()));
        Schedule schedule = scheduleAssembler.toSchedule(command);
        // 设置日历Id
        String primaryCalendarId = getPrimaryCalendarId();
        schedule.setFsCalendarId(primaryCalendarId);
        //subscription(primaryCalendarId);

        // 调用飞书创建日程
        CreateCalendarEventRespBody createCalendarEventRespBody = addFSCalendarEvent(schedule);
        schedule.setFsCalendarEventId(createCalendarEventRespBody.getEvent().getEventId());
        schedule.setFsMeetingUrl(createCalendarEventRespBody.getEvent().getVchat().getMeetingUrl());

        // 调用异步方法为日程添加参会人
        asyncService.addFSCalendarEventAttendeesAsync(schedule, FeishuAppContext.get());

        // 日程信息落库
        scheduleDomainService.add(schedule);

        // 发送日程创建事件
        CreateScheduleEvent.CreateScheduleData createScheduleData = CreateScheduleEvent.CreateScheduleData.builder()
            .schedule(schedule)
            .build();
        springEventPublish.publish(new CreateScheduleEvent(createScheduleData));
    }

    private CreateCalendarEventRespBody addFSCalendarEvent(Schedule schedule) {
        try {
            CreateCalendarEventModel createCalendarEventModel = CreateCalendarEventModel.builder()
                .summary(schedule.getScheduleName())
                .description(businessMeetingQueryService.get(schedule.getBusinessMeetingId()).getName())
                .calendarId(schedule.getFsCalendarId())
                .startTime(schedule.getStartTime())
                .endTime(schedule.getEndTime())
                .date(schedule.getAllDay())
                .build();

            schedule.getAttendUsers().stream()
                .filter(attendUser -> schedule.getCreateUserId().equals(attendUser.getAttendUserId()))
                .findFirst()
                .ifPresent(attendUser -> {
                    createCalendarEventModel.setOrganizerId(attendUser.getAttendUserId());
                    createCalendarEventModel.setOrganizerName(attendUser.getAttendUserName());
                });
            return feishuAppClient.createCalendarEvent(createCalendarEventModel);
        } catch (Exception e) {
            log.error("创建飞书日程失败，日程名称：{}", schedule.getScheduleName(), e);
            throw new BusinessException("创建飞书日程失败：" + e.getMessage());
        }
    }

    @Transactional
    public void update(UpdateScheduleCommand command) {
        SchedulePO schedulePO = scheduleRepository.queryScheduleById(command.getScheduleId());
        if (!schedulePO.getCreateUserId().equals(CurrentUserHolder.getCurrentUser().getOpenId())) {
            throw new BusinessException(MessageCode.NO_PERMISSION_TO_UPDATE);
        }

        MeetingAgg meetingAgg = meetingRepository.findByScheduleId(command.getScheduleId());
        if (meetingAgg.getInfo().getStatus().getCode() > MeetingStatusEnum.WAIT_START.getCode()) {
            throw new BusinessException(MessageCode.NO_ALLOW_TO_UPDATE);
        }

        // 参会人员重复校验
        HashSet<String> commandUserIds = new HashSet<>();
        command.getAttendUsers().forEach(attendUserCommand -> {
            if (!commandUserIds.add(attendUserCommand.getAttendUserId())) {
                throw new BusinessException(MessageCode.EXIST_PERSONNEL_DUPLICATION, attendUserCommand.getAttendUserName());
            }
        });
        // 确保有当前用户
        commandUserIds.add(schedulePO.getCreateUserId());

        // 查出当前日程下的参与人ID
        List<ScheduleAttendUserPO> scheduleAttendUserPOS = scheduleRepository.queryUserByScheduleId(command.getScheduleId());
        Set<String> userIds = scheduleAttendUserPOS.stream()
            .map(ScheduleAttendUserPO::getAttendUserId)
            .collect(Collectors.toSet());

        // 分别过滤出 更新、新增、删除的参与人
        HashSet<String> updateUserIds = new HashSet<>(userIds);
        HashSet<String> removeUserIds = new HashSet<>(userIds);
        HashSet<String> addUserIds = new HashSet<>(commandUserIds);
        updateUserIds.retainAll(commandUserIds);
        removeUserIds.removeAll(commandUserIds);
        addUserIds.removeAll(userIds);

        // 重新赋值新的会议名
        command.setScheduleNamePrefix(teamQueryService.getScheduleName(command.getTeamId(), command.getBusinessMeetingId()));
        Schedule schedule = scheduleAssembler.toSchedule(command);
        schedule.setId(command.getScheduleId());

        // 获取需要更新的参会人
        List<AttendUser> updateAttendUsers = schedule.getAttendUsers().stream()
            .filter(attendUser -> updateUserIds.contains(attendUser.getAttendUserId()))
            .collect(Collectors.toList());
        // 飞书日程【更新】
        PatchCalendarEventRespBody patchCalendarEventRespBody = updateFSCalendarEvent(schedulePO, schedule);
        // 落库【更新日程，更新参会人】
        scheduleDomainService.update(schedule, updateAttendUsers);

        // 获取需要新增的参会人
        List<AttendUser> addAttendUsers = schedule.getAttendUsers().stream()
            .filter(attendUser -> addUserIds.contains(attendUser.getAttendUserId()))
            .collect(Collectors.toList());
        // 落库 【数据库添加参会人】
        scheduleDomainService.addAttendUsers(command.getScheduleId(), addAttendUsers);

        // 获取历史状态为请假的人【可能需要邀请入会】
        List<ScheduleAttendUserPO> absents = scheduleAttendUserPOS.stream()
            .filter(scheduleAttendUserPO -> scheduleAttendUserPO.getAttendUserState().equals(AttendUserStateEnum.ABSENT))
            .collect(Collectors.toList());
        absents.forEach(absent -> {
            schedule.getAttendUsers().stream()
                .filter(attendUser -> attendUser.getAttendUserId().equals(absent.getAttendUserId()))
                .findFirst()
                .ifPresent(addAttendUsers::add);
        });
        // 飞书日程【添加参与人】
        addFSCalendarEventAttendees(schedulePO, addAttendUsers);

        // 落库【数据库删除参会人】
        scheduleDomainService.removeAttendUsers(command.getScheduleId(), new ArrayList<>(removeUserIds));
        // 获取新的请假人员
        schedule.getAttendUsers().stream()
            .filter(attendUser -> attendUser.getAttendUserState().equals(AttendUserStateEnum.ABSENT))
            .forEach(attendUser -> removeUserIds.add(attendUser.getAttendUserId()));
        // 飞书日程【删除参与人】
        deleteFSCalendarEventAttendees(schedulePO, new ArrayList<>(removeUserIds));

        // 发送日程更新事件
        UpdateScheduleEvent.UpdateScheduleData updateScheduleData = UpdateScheduleEvent.UpdateScheduleData.builder()
            .schedule(schedule)
            .build();
        springEventPublish.publish(new UpdateScheduleEvent(updateScheduleData));
    }

    private PatchCalendarEventRespBody updateFSCalendarEvent(SchedulePO schedulePO, Schedule schedule) {
        try {
            return feishuAppClient.updateCalendarEvent(UpdateCalendarEventModel.builder()
                .calendarId(schedulePO.getFsCalendarId())
                .eventId(schedulePO.getFsCalendarEventId())
                .summary(schedule.getScheduleName())
                .startTime(schedule.getStartTime())
                .endTime(schedule.getEndTime())
                .date(schedule.getAllDay())
                .build());
        } catch (Exception e) {
            log.error("更新飞书日程失败，日程ID：{}", schedulePO.getId(), e);
            throw new BusinessException("更新飞书日程失败：" + e.getMessage());
        }
    }

    private void addFSCalendarEventAttendees(SchedulePO schedulePO, List<AttendUser> attendUsers) {
        if (!attendUsers.isEmpty()) {
            List<AttendUserModel> attendUserModels = attendUsers.stream()
                .filter(attendUser -> attendUser.getAttendUserState().equals(AttendUserStateEnum.EXPECT))
                .map(attendUser -> AttendUserModel.builder()
                    .userId(attendUser.getAttendUserId())
                    .isOrganizer(schedulePO.getCreateUserId().equals(attendUser.getAttendUserId()))
                    .build())
                .collect(Collectors.toList());

            if (attendUserModels.isEmpty()) {
                return;
            }

            try {
                feishuAppClient.addCalendarEventAttendees(CreateCalendarEventAttendeesModel.builder()
                    .calendarId(schedulePO.getFsCalendarId())
                    .eventId(schedulePO.getFsCalendarEventId())
                    .attendUsers(attendUserModels)
                    .build());
            } catch (Exception e) {
                log.error("添加飞书日程参会人员失败，日程ID：{}", schedulePO.getId(), e);
                throw new BusinessException("添加飞书日程参会人员失败：" + e.getMessage());
            }
        }
    }

    private void deleteFSCalendarEventAttendees(SchedulePO schedulePO, List<String> deleteUserIds) {
        if (!deleteUserIds.isEmpty()) {
            try {
                feishuAppClient.deleteCalendarEventAttendees(DeleteCalendarEventAttendeesModel.builder()
                    .calendarId(schedulePO.getFsCalendarId())
                    .eventId(schedulePO.getFsCalendarEventId())
                    .userIds(deleteUserIds)
                    .build());
            } catch (Exception e) {
                log.error("删除飞书日程参会人员失败，日程ID：{}", schedulePO.getId(), e);
                throw new BusinessException("删除飞书日程参会人员失败：" + e.getMessage());
            }
        }
    }

    public void delete(Long scheduleId) {
        SchedulePO schedulePO = scheduleRepository.queryScheduleById(scheduleId);
        if (schedulePO == null) {
            return;
        }

        if (!schedulePO.getCreateUserId().equals(CurrentUserHolder.getCurrentUser().getOpenId())) {
            throw new BusinessException(MessageCode.NO_PERMISSION_TO_DELETE);
        }

        MeetingAgg meetingAgg = meetingRepository.findByScheduleId(scheduleId);
        if (meetingAgg.getInfo().getStatus().getCode() > MeetingStatusEnum.WAIT_START.getCode()) {
            throw new BusinessException(MessageCode.NO_ALLOW_TO_DELETE);
        }

        // 删除飞书日程
        deleteFSCalendarEvent(schedulePO);

        scheduleDomainService.delete(scheduleId);

        // 发送日程删除事件
        DeleteScheduleEvent.DeleteScheduleData deleteScheduleData = DeleteScheduleEvent.DeleteScheduleData.builder()
            .scheduleId(scheduleId)
            .build();
        springEventPublish.publish(new DeleteScheduleEvent(deleteScheduleData));
    }

    private void deleteFSCalendarEvent(SchedulePO schedule) {
        try {
            feishuAppClient.deleteCalendarEvent(CalendarEventModel.builder()
                .calendarId(schedule.getFsCalendarId())
                .eventId(schedule.getFsCalendarEventId())
                .build());
        } catch (Exception e) {
            log.error("删除飞书日程失败，日程ID：{}", schedule.getId(), e);
            throw new BusinessException("删除飞书日程失败：" + e.getMessage());
        }
    }

    private String getPrimaryCalendarId() {
        UserCalendar userCalendar = feishuAppClient.getCalendarService().getUserMainInfo();
        if (ObjUtil.isNull(userCalendar)) {
            throw new BusinessException(MessageCode.USER_CALENDAR_INFO_ERROR);
        }
        return userCalendar.getCalendar().getCalendarId();
    }

    public void subscription() {
        subscription(getPrimaryCalendarId());
    }

    public void subscription(String feishuCalendarId) {
        feishuAppClient.subscriptionCalendarEvent(feishuCalendarId);
    }
}
