package cn.genn.pf.orch.meeting.domain.meeting.listener;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.schedule.model.event.UpdateScheduleEvent;
import cn.genn.pf.orch.meeting.domain.schedule.service.MeetingDomainService;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventSyncListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class UpdateScheduleListener extends SpringEventSyncListener<UpdateScheduleEvent> {

    @Resource
    private ScheduleQueryService scheduleQueryService;
    @Resource
    private MeetingDomainService meetingDomainService;

    @Override
    protected void onMessage(UpdateScheduleEvent event) {

        UpdateScheduleEvent.UpdateScheduleData updateScheduleData = (UpdateScheduleEvent.UpdateScheduleData)event.getSource();

        log.info("receive update schedule event context: {}", JsonUtils.toJson(updateScheduleData));

        ScheduleDTO scheduleDTO = scheduleQueryService.getByScheduleId(updateScheduleData.getSchedule().getId());

        MeetingAgg meetingAgg = meetingDomainService.findByScheduleId(scheduleDTO.getId());
        meetingDomainService.update(meetingAgg, scheduleDTO);

    }
}
