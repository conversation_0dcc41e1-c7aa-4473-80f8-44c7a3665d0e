package cn.genn.pf.orch.meeting.infrastructure.repository.mapper;

import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPlanPO;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.MeetingPlanCalendarDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.MeetingPlanListDTO;
import cn.genn.pf.orch.meeting.interfaces.query.meetingplan.MeetingPlanCalendarQuery;
import cn.genn.pf.orch.meeting.interfaces.query.meetingplan.MeetingPlanQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划Mapper
 * @date 2025-01-24
 */
public interface MeetingPlanMapper extends BaseMapper<MeetingPlanPO> {

    /**
     * 分页查询会议规划列表
     */
    Page<MeetingPlanListDTO> queryPage(Page<MeetingPlanPO> page, @Param("query") MeetingPlanQuery query);

    /**
     * 查询日历维度的会议规划
     */
    List<MeetingPlanCalendarDTO> queryCalendar(@Param("query") MeetingPlanCalendarQuery query);
}
