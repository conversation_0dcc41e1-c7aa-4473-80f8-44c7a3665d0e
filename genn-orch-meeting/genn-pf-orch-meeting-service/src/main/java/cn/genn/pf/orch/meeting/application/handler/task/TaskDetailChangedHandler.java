package cn.genn.pf.orch.meeting.application.handler.task;

import cn.genn.pf.orch.meeting.domain.task.model.aggregates.TaskAgg;
import cn.genn.pf.orch.meeting.domain.task.service.TaskDomainService;
import cn.genn.pf.orch.meeting.infrastructure.enums.TaskNotificationTypeEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.TaskPriorityEnum;
import com.lark.oapi.service.task.v1.model.P2TaskUpdatedV1Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * 任务详情发生变化
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TaskDetailChangedHandler implements TaskNotificationHandler {

    private final TaskDomainService taskDomainService;

    @Override
    public void handle(P2TaskUpdatedV1Data notification) {
        log.info("处理任务详情变化事件，飞书任务ID：{}", notification.getTaskId());
        try {
            // 查询本地任务
            TaskAgg taskAgg = taskDomainService.findByFeishuTaskId(notification.getTaskId());
            if (taskAgg == null) {
                log.warn("未找到对应的本地任务，飞书任务ID：{}", notification.getTaskId());
                return;
            }

            // 这里可以根据需要从飞书API获取最新的任务详情并更新
            // 由于没有具体的飞书API服务，这里暂时只记录日志
            // 如果需要更新具体字段，可以调用 taskDomainService.updateByFeishuTaskId() 方法

            log.info("任务详情变化处理完成，飞书任务ID：{}", notification.getTaskId());
        } catch (Exception e) {
            log.error("处理任务详情变化事件失败，飞书任务ID：{}", notification.getTaskId(), e);
        }
    }

    @Override
    public TaskNotificationTypeEnum getObjType() {
        return TaskNotificationTypeEnum.TASK_DETAIL_CHANGED;
    }
}
