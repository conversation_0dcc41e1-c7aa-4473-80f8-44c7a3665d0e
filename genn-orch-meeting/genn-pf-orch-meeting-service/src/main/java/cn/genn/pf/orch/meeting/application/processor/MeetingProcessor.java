package cn.genn.pf.orch.meeting.application.processor;

import cn.genn.core.exception.BusinessException;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.infrastructure.exception.MessageCode;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingAttendUserMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingAttendUserPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPO;
import cn.genn.pf.orch.meeting.interfaces.command.MeetingSignInCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.CheckInStatusEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingStatusEnum;
import cn.hutool.core.util.ObjUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Component
public class MeetingProcessor {

    @Resource
    private MeetingMapper meetingMapper;
    @Resource
    private MeetingAttendUserMapper meetingAttendUserMapper;
    @Resource
    private ScheduleQueryService scheduleQueryService;
    public void checkSignIn(MeetingSignInCommand command){
        //会议存在
        MeetingPO meetingPO = meetingMapper.selectById(command.getMeetingId());
        if(ObjUtil.isNull(meetingPO)){
            throw new BusinessException(MessageCode.MEETING_NOT_EXIST);
        }
        if(meetingPO.getStatus().equals(MeetingStatusEnum.END)){
            throw new BusinessException(MessageCode.MEETING_ENT_EXIST);
        }
        if (meetingPO.getStatus().equals(MeetingStatusEnum.WAIT_START)) {
            ScheduleDTO scheduleDTO = scheduleQueryService.getByScheduleId(meetingPO.getScheduleId());
            if (LocalDateTime.now().isBefore(scheduleDTO.getStartTime().minusMinutes(30L))) {
                // 未到达签到时间，不允许
                throw new BusinessException(MessageCode.CHECK_IN_TIME_ERROR);
            }
        }
        //签到码不正确
        if(!command.getSignInCode().equals(meetingPO.getCheckInNumber())){
            throw new BusinessException(MessageCode.SING_IN_CODE_ERROR);
        }
        //重复签到判断
        MeetingAttendUserPO po = meetingAttendUserMapper.selectByMeetingIdAndUserId(command.getMeetingId(),command.getOpenId());
        if(ObjUtil.isNotNull(po) && ObjUtil.isNotNull(po.getCheckInStatus()) && CheckInStatusEnum.CHECKED.equals(po.getCheckInStatus())){
            throw new BusinessException(MessageCode.DO_NOT_SIGN_IN_AGAIN);
        }
    }

}
