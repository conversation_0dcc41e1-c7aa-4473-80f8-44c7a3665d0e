package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.pf.orch.meeting.application.dto.card.SendTaskSupervisionDTO;
import cn.genn.pf.orch.meeting.application.service.query.TaskQueryService;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskSupervisionDTO;
import com.lark.oapi.Client;
import com.lark.oapi.service.im.v1.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务督办服务
 */
@Slf4j
@Service
public class TaskSupervisionService {

    @Resource
    private TaskQueryService taskQueryService;

    @Resource
    private Client larkClient;

    @Value("${app.task.detail.url:http://localhost:3000/task/}")
    private String taskDetailBaseUrl;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    /**
     * 自动督办任务（定时任务调用）
     */
    public void autoSuperviseUrgentTasks() {
        log.info("开始执行自动任务督办");

        try {
            List<TaskSupervisionDTO> urgentTasks = taskQueryService.getTasksForSupervision();

            if (urgentTasks.isEmpty()) {
                log.info("当前没有需要督办的紧急任务");
                return;
            }

            log.info("发现 {} 个需要督办的紧急任务", urgentTasks.size());

            for (TaskSupervisionDTO task : urgentTasks) {
                try {
                    sendTaskSupervisionCard(task, "自动督办");
                    log.info("任务督办消息发送成功，taskId: {}, owner: {}", task.getTaskId(), task.getOwnerName());
                } catch (Exception e) {
                    log.error("任务督办消息发送失败，taskId: {}, owner: {}", task.getTaskId(), task.getOwnerName(), e);
                }
            }

        } catch (Exception e) {
            log.error("自动任务督办执行失败", e);
        }
    }

    /**
     * 手动督办指定任务
     *
     * @param taskId 任务ID
     */
    public void manualSuperviseTask(Long taskId) {
        log.info("开始手动督办任务，taskId: {}", taskId);

        try {
            TaskSupervisionDTO task = taskQueryService.getTaskSupervisionById(taskId);
            sendTaskSupervisionCard(task, "手动督办");
            log.info("手动督办消息发送成功，taskId: {}, owner: {}", taskId, task.getOwnerName());
        } catch (Exception e) {
            log.error("手动督办消息发送失败，taskId: {}", taskId, e);
            throw new RuntimeException("督办消息发送失败", e);
        }
    }

    /**
     * 测试督办功能 - 发送模拟督办卡片
     *
     * @param openId 接收人的OpenId
     */
    public void testSupervision(String openId) {
        log.info("开始测试督办功能，发送给用户: {}", openId);

        try {
            // 创建模拟的督办任务信息
            TaskSupervisionDTO mockTask = createMockTaskSupervisionDTO(openId);

            // 发送督办卡片
            sendTaskSupervisionCard(mockTask, "测试督办");

            log.info("测试督办消息发送成功，接收人: {}", openId);
        } catch (Exception e) {
            log.error("测试督办消息发送失败，接收人: {}", openId, e);
            throw new RuntimeException("测试督办消息发送失败", e);
        }
    }

    /**
     * 发送任务督办飞书卡片消息
     *
     * @param task 任务督办信息
     * @param supervisionType 督办类型
     */
    private void sendTaskSupervisionCard(TaskSupervisionDTO task, String supervisionType) {
        try {
            // 构建发送DTO
            SendTaskSupervisionDTO sendDTO = buildSendTaskSupervisionDTO(task, supervisionType);

            // 构建飞书卡片消息
            String cardContent = buildTaskSupervisionCardContent(sendDTO);

            // 发送消息
            CreateMessageReq req = CreateMessageReq.newBuilder()
                    .receiveIdType("open_id")
                    .createMessageReqBody(CreateMessageReqBody.newBuilder()
                            .receiveId(task.getOwnerOpenId())
                            .msgType("interactive")
                            .content(cardContent)
                            .build())
                    .build();

            CreateMessageResp resp = larkClient.im().v1().message().create(req);

            if (!resp.success()) {
                log.error("飞书消息发送失败，code: {}, msg: {}", resp.getCode(), resp.getMsg());
                throw new RuntimeException("飞书消息发送失败: " + resp.getMsg());
            }

            log.info("飞书督办消息发送成功，messageId: {}", resp.getData().getMessageId());

        } catch (Exception e) {
            log.error("发送任务督办飞书卡片失败", e);
            throw new RuntimeException("发送督办消息失败", e);
        }
    }

    /**
     * 构建发送DTO
     */
    private SendTaskSupervisionDTO buildSendTaskSupervisionDTO(TaskSupervisionDTO task, String supervisionType) {
        // 计算剩余时间描述
        String remainingTime = buildRemainingTimeDescription(task.getRemainingHours());

        // 确定紧急程度和颜色
        String urgencyLevel;
        String priorityColor;

        if (task.getIsUrgent()) {
            urgencyLevel = "🔴 紧急";
            priorityColor = "red";
        } else if (task.getRemainingHours() != null && task.getRemainingHours() <= 24) {
            urgencyLevel = "🟡 较急";
            priorityColor = "orange";
        } else {
            urgencyLevel = "🟢 正常";
            priorityColor = "green";
        }

        return SendTaskSupervisionDTO.builder()
                .openId(task.getOwnerOpenId())
                .taskId(task.getTaskId().toString())
                .taskTitle(task.getTitle())
                .taskDescription(task.getDescription())
                .ownerName(task.getOwnerName())
                .priorityName(task.getPriorityName())
                .priorityColor(priorityColor)
                .dueDate(task.getDueDate() != null ? task.getDueDate().format(DATE_TIME_FORMATTER) : "未设置")
                .remainingTime(remainingTime)
                .urgencyLevel(urgencyLevel)
                .taskDetailUrl(taskDetailBaseUrl + task.getTaskId())
                .supervisionType(supervisionType)
                .supervisionTime(LocalDateTime.now().format(DATE_TIME_FORMATTER))
                .build();
    }

    /**
     * 构建剩余时间描述
     */
    private String buildRemainingTimeDescription(Long remainingHours) {
        if (remainingHours == null) {
            return "未设置截止时间";
        }

        if (remainingHours < 0) {
            return "已超期 " + Math.abs(remainingHours) + " 小时";
        } else if (remainingHours == 0) {
            return "即将到期";
        } else if (remainingHours < 24) {
            return "剩余 " + remainingHours + " 小时";
        } else {
            long days = remainingHours / 24;
            long hours = remainingHours % 24;
            return "剩余 " + days + " 天 " + hours + " 小时";
        }
    }

    /**
     * 构建任务督办飞书卡片内容
     */
    private String buildTaskSupervisionCardContent(SendTaskSupervisionDTO sendDTO) {
        // 根据紧急程度选择卡片颜色主题
        String themeColor = getCardThemeColor(sendDTO.getPriorityColor());
        String urgencyIcon = getUrgencyIcon(sendDTO.getUrgencyLevel());

        String cardJson = String.format(
            "{\n" +
            "  \"config\": {\n" +
            "    \"wide_screen_mode\": true\n" +
            "  },\n" +
            "  \"header\": {\n" +
            "    \"template\": \"%s\",\n" +
            "    \"title\": {\n" +
            "      \"tag\": \"plain_text\",\n" +
            "      \"content\": \"%s 任务督办提醒\"\n" +
            "    }\n" +
            "  },\n" +
            "  \"elements\": [\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**任务标题**\\n%s\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**负责人**\\n%s\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**优先级**\\n%s\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**紧急程度**\\n%s\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**截止时间**\\n%s\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**剩余时间**\\n%s\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"text\": {\n" +
            "        \"tag\": \"lark_md\",\n" +
            "        \"content\": \"**任务描述**\\n%s\"\n" +
            "      }\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"hr\"\n" +
            "    },\n" +
            "    {\n" +
            "      \"tag\": \"div\",\n" +
            "      \"fields\": [\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**督办类型**\\n%s\"\n" +
            "          }\n" +
            "        },\n" +
            "        {\n" +
            "          \"is_short\": true,\n" +
            "          \"text\": {\n" +
            "            \"tag\": \"lark_md\",\n" +
            "            \"content\": \"**督办时间**\\n%s\"\n" +
            "          }\n" +
            "        }\n" +
            "      ]\n" +
            "    }\n" +
            "  ]\n" +
            "}",
            themeColor,
            urgencyIcon,
            escapeJsonString(sendDTO.getTaskTitle()),
            escapeJsonString(sendDTO.getOwnerName()),
            escapeJsonString(sendDTO.getPriorityName()),
            escapeJsonString(sendDTO.getUrgencyLevel()),
            escapeJsonString(sendDTO.getDueDate()),
            escapeJsonString(sendDTO.getRemainingTime()),
            escapeJsonString(sendDTO.getTaskDescription() != null ? sendDTO.getTaskDescription() : "暂无描述"),
            escapeJsonString(sendDTO.getSupervisionType()),
            escapeJsonString(sendDTO.getSupervisionTime())
        );

        return cardJson;
    }

    /**
     * 根据优先级获取卡片主题颜色
     */
    private String getCardThemeColor(String priorityColor) {
        switch (priorityColor) {
            case "red":
                return "red";
            case "orange":
                return "orange";
            case "green":
                return "green";
            default:
                return "blue";
        }
    }

    /**
     * 根据紧急程度获取图标
     */
    private String getUrgencyIcon(String urgencyLevel) {
        if (urgencyLevel.contains("🔴")) {
            return "🚨";
        } else if (urgencyLevel.contains("🟡")) {
            return "⚠️";
        } else {
            return "📋";
        }
    }

    /**
     * 转义JSON字符串
     */
    private String escapeJsonString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }

    /**
     * 创建模拟的督办任务数据
     *
     * @param openId 接收人OpenId
     * @return 模拟的督办任务信息
     */
    private TaskSupervisionDTO createMockTaskSupervisionDTO(String openId) {
        // 随机选择一个模拟场景
        String[] scenarios = {
            "urgent",    // 紧急任务
            "normal",    // 普通任务
            "high_priority"  // 高优先级任务
        };

        String scenario = scenarios[(int) (Math.random() * scenarios.length)];

        switch (scenario) {
            case "urgent":
                return createUrgentMockTask(openId);
            case "high_priority":
                return createHighPriorityMockTask(openId);
            default:
                return createNormalMockTask(openId);
        }
    }

    /**
     * 创建紧急任务模拟数据
     */
    private TaskSupervisionDTO createUrgentMockTask(String openId) {
        return TaskSupervisionDTO.builder()
                .taskId(9999L)
                .title("🚨 紧急：生产环境故障修复")
                .description("生产环境出现严重故障，影响用户正常使用，需要立即修复。故障现象：用户无法登录系统，数据库连接异常。")
                .ownerOpenId(openId)
                .ownerName("测试用户")
                .priority(1)
                .priorityName("高")
                .status(2)
                .statusName("进行中")
                .dueDate(LocalDateTime.now().plusHours(1))  // 1小时后到期
                .remainingHours(1L)
                .isUrgent(true)
                .meetingId(888L)
                .createTime(LocalDateTime.now().minusHours(2))
                .build();
    }

    /**
     * 创建高优先级任务模拟数据
     */
    private TaskSupervisionDTO createHighPriorityMockTask(String openId) {
        return TaskSupervisionDTO.builder()
                .taskId(8888L)
                .title("📋 重要：月度项目汇报准备")
                .description("准备下周一的月度项目汇报材料，包括项目进度、风险评估、下月计划等内容。需要协调各部门提供相关数据。")
                .ownerOpenId(openId)
                .ownerName("测试用户")
                .priority(1)
                .priorityName("高")
                .status(2)
                .statusName("进行中")
                .dueDate(LocalDateTime.now().plusHours(2))  // 2小时后到期
                .remainingHours(2L)
                .isUrgent(true)
                .meetingId(777L)
                .createTime(LocalDateTime.now().minusDays(3))
                .build();
    }

    /**
     * 创建普通任务模拟数据
     */
    private TaskSupervisionDTO createNormalMockTask(String openId) {
        return TaskSupervisionDTO.builder()
                .taskId(7777L)
                .title("📝 常规：用户手册更新")
                .description("根据最新的产品功能更新，修订用户操作手册，确保文档内容与实际功能保持一致。")
                .ownerOpenId(openId)
                .ownerName("测试用户")
                .priority(2)
                .priorityName("中")
                .status(1)
                .statusName("未开始")
                .dueDate(LocalDateTime.now().plusHours(6))  // 6小时后到期
                .remainingHours(6L)
                .isUrgent(false)
                .meetingId(666L)
                .createTime(LocalDateTime.now().minusDays(1))
                .build();
    }
}
