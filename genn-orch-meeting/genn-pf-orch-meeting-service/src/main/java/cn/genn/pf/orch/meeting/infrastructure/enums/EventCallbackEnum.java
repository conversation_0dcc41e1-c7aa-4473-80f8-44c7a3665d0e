package cn.genn.pf.orch.meeting.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 事件回调枚举
 * @date 2024-12-25
 */
@Getter
@AllArgsConstructor
public enum EventCallbackEnum {

    MEETING_START("vc.meeting.meeting_started_v1", "会议开始事件"),
    MEETING_END("vc.meeting.meeting_ended_v1", "会议结束事件"),
    MEETING_RECORD_READY("vc.meeting.recording_ready_v1", "会议录制完成事件"),
    MEETING_EVALUATION_SUBMIT("im.message.receive_v1", "会议评价提交事件"),
    SCHEDULE_CHANGE("calendar.calendar.event.changed_v4", "日程变更事件"),
    TASK_UPDATE("task.task.updated_v1", "任务信息更新事件"),
    CARD_ACTION("card.action.trigger", "卡片交互事件"),
    ;

    private final String eventType;

    private final String desc;

    private static final Map<String, EventCallbackEnum> VALUES = new HashMap<>();
    static {
        for (final EventCallbackEnum item : EventCallbackEnum.values()) {
            VALUES.put(item.getEventType(), item);
        }
    }

    public static EventCallbackEnum of(final String value) {
        return VALUES.get(value);
    }
}
