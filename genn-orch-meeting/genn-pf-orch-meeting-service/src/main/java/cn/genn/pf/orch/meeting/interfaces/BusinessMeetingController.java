package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.pf.orch.meeting.application.service.action.BusinessMeetingActionService;
import cn.genn.pf.orch.meeting.application.service.query.BusinessMeetingQueryService;
import cn.genn.pf.orch.meeting.interfaces.command.AddBusinessMeetingCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.BusinessMeetingLevelDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.SingleBusinessMeetingDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 业务会议
 *
 * <AUTHOR>
 */
@Api(tags = "业务会议")
@RestController
@RequestMapping("/business")
@RequiredArgsConstructor
public class BusinessMeetingController {

    private final BusinessMeetingQueryService queryService;
    private final BusinessMeetingActionService actionService;

    @PostMapping("/level")
    @ApiOperation(value = "业务会议层级列表")
    public BusinessMeetingLevelDTO level() {
        return queryService.level();
    }

    @PostMapping("/add")
    @ApiOperation(value = "添加业务会议")
    public void add(@Validated @RequestBody AddBusinessMeetingCommand addBusinessMeetingCommand) {
        actionService.add(addBusinessMeetingCommand);
    }

    @PostMapping("/list")
    @ApiOperation(value = "获取业务会议列表")
    public List<SingleBusinessMeetingDTO> list() {
        return queryService.list();
    }
}
