package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.pf.orch.meeting.application.assembler.BusinessMeetingAssembler;
import cn.genn.pf.orch.meeting.domain.business.model.entity.BusinessMeeting;
import cn.genn.pf.orch.meeting.domain.business.service.BusinessMeetingDomainService;
import cn.genn.pf.orch.meeting.interfaces.command.AddBusinessMeetingCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 业务会议操作服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessMeetingActionService {

    private final BusinessMeetingDomainService businessMeetingDomainService;

    private final BusinessMeetingAssembler assembler;

    public void add(AddBusinessMeetingCommand addBusinessMeetingCommand) {
        BusinessMeeting businessMeeting = assembler.toBusinessMeeting(addBusinessMeetingCommand);
        businessMeetingDomainService.addBusinessMeeting(businessMeeting);
    }
}
