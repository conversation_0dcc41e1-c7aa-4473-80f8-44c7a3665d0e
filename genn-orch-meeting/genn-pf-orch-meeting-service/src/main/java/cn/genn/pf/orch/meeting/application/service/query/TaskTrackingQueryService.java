package cn.genn.pf.orch.meeting.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskListDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskTrackingStatisticsDTO;
import cn.genn.pf.orch.meeting.interfaces.query.TaskListQuery;
import cn.genn.pf.orch.meeting.interfaces.query.TaskStatisticsQuery;

/**
 * <AUTHOR>
 * @description 任务追踪查询服务接口
 * @date 2025-01-28
 */
public interface TaskTrackingQueryService {

    /**
     * 获取任务列表
     */
    PageResultDTO<TaskListDTO> getTaskList(TaskListQuery query);

    /**
     * 获取任务统计数据
     */
    TaskTrackingStatisticsDTO getTaskStatistics(TaskStatisticsQuery query);
}
