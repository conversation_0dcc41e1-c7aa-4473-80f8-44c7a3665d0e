package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.pf.orch.meeting.application.service.action.MeetingEvaluationActionService;
import cn.genn.pf.orch.meeting.application.service.query.NewMeetingQueryService;
import cn.genn.pf.orch.meeting.interfaces.dto.newmeeting.NewMeetingDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 会议评价测试控制器
 * @date 2025-01-24
 */
@Api(tags = "会议评价测试")
@RestController
@RequestMapping("/meeting-evaluation-test")
@RequiredArgsConstructor
@Slf4j
public class MeetingEvaluationTestController {

    private final NewMeetingQueryService newMeetingQueryService;
    private final MeetingEvaluationActionService evaluationActionService;

    @PostMapping("/test-send-evaluation-cards")
    @ApiOperation("测试会议结束后发送评价卡片")
    public String testSendEvaluationCards(
            @ApiParam(value = "飞书日程事件ID", required = true, example = "calendar_event_123456")
            @RequestParam("calendarEventId") String calendarEventId) {
        
        log.info("开始测试发送会议评价卡片，calendarEventId：{}", calendarEventId);
        
        try {
            // 根据calendarEventId获取新会议信息
            NewMeetingDTO newMeeting = newMeetingQueryService.findByFsCalendarEventId(calendarEventId);
            if (newMeeting != null) {
                log.info("找到会议，会议ID：{}，会议名称：{}", newMeeting.getId(), newMeeting.getMeetingName());
                // 发送新会议系统的评价卡片
                evaluationActionService.sendNewMeetingEvaluationCards(newMeeting.getId());
                return String.format("测试成功！会议ID：%d，会议名称：%s，已向参会人员发送评价卡片", 
                    newMeeting.getId(), 
                    newMeeting.getMeetingName());
            } else {
                log.warn("未找到对应的会议，calendarEventId：{}", calendarEventId);
                return String.format("测试失败！未找到对应的会议，calendarEventId：%s", calendarEventId);
            }
            
        } catch (Exception e) {
            log.error("测试发送会议评价卡片失败，calendarEventId：{}", calendarEventId, e);
            return String.format("测试失败！错误信息：%s", e.getMessage());
        }
    }

    @GetMapping("/test-find-meeting")
    @ApiOperation("测试根据calendarEventId查找会议信息")
    public String testFindMeeting(
            @ApiParam(value = "飞书日程事件ID", required = true, example = "calendar_event_123456")
            @RequestParam("calendarEventId") String calendarEventId) {
        
        log.info("开始测试查找会议信息，calendarEventId：{}", calendarEventId);
        
        try {
            // 根据calendarEventId获取新会议信息
            NewMeetingDTO newMeeting = newMeetingQueryService.findByFsCalendarEventId(calendarEventId);
            if (newMeeting != null) {
                log.info("找到会议，会议ID：{}，会议名称：{}", newMeeting.getId(), newMeeting.getMeetingName());
                return String.format("查找成功！会议ID：%d，会议名称：%s", 
                    newMeeting.getId(), 
                    newMeeting.getMeetingName());
            } else {
                log.warn("未找到对应的会议，calendarEventId：{}", calendarEventId);
                return String.format("查找失败！未找到对应的会议，calendarEventId：%s", calendarEventId);
            }
            
        } catch (Exception e) {
            log.error("测试查找会议信息失败，calendarEventId：{}", calendarEventId, e);
            return String.format("查找失败！错误信息：%s", e.getMessage());
        }
    }
} 