package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.pf.orch.meeting.application.service.action.AiEmpowermentService;
import cn.genn.pf.orch.meeting.interfaces.command.FileSummaryCommand;
import cn.genn.pf.orch.meeting.interfaces.command.FileUploadSummaryCommand;
import cn.genn.pf.orch.meeting.interfaces.command.MixedContentAnalysisCommand;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description AI赋能控制器
 */
@Api(tags = "AI赋能")
@Slf4j
@RestController
@RequestMapping("/ai")
public class AiEmpowermentController {

    @Resource
    private AiEmpowermentService aiEmpowermentService;

    @PostMapping("/file/summary")
    @ApiOperation(value = "文件汇总提炼", notes = "通过AI智能体对文件内容进行汇总提炼，支持文件+图片组合")
    public String fileSummary(@Validated @RequestBody FileSummaryCommand command) {
        log.info("收到文件汇总提炼请求，文件URL：{}，图片数量：{}",
                command.getFileUrl(),
                command.getImageUrls() != null ? command.getImageUrls().size() : 0);
        return aiEmpowermentService.fileSummary(command);
    }

    @PostMapping("/mixed/analysis")
    @ApiOperation(value = "混合内容分析", notes = "通过AI智能体对文本+图片+文件的混合内容进行分析")
    public String mixedContentAnalysis(@RequestBody MixedContentAnalysisCommand command) {
        log.info("收到混合内容分析请求，文本：{}，图片数量：{}，文件URL：{}",
                command.getTextMessage(),
                command.getImageUrls() != null ? command.getImageUrls().size() : 0,
                command.getFileUrl());
        return aiEmpowermentService.mixedContentAnalysis(command);
    }

    @PostMapping("/file/upload-summary")
    @ApiOperation(value = "文件上传汇总", notes = "上传文件并通过AI智能体进行汇总提炼，自动获取文件contentType")
    public String fileUploadSummary(@RequestParam("file") MultipartFile file,
                                   @RequestParam(value = "appId", required = false) String appId,
                                   @RequestParam(value = "customPrompt", required = false) String customPrompt,
                                   @RequestParam(value = "filePlatform", required = false) String filePlatform) {
        log.info("收到文件上传汇总请求，文件名：{}，大小：{} bytes",
                file.getOriginalFilename(), file.getSize());

        FileUploadSummaryCommand command = new FileUploadSummaryCommand();
        command.setFile(file);
        command.setAppId(appId);
        command.setCustomPrompt(customPrompt);
        command.setFilePlatform(filePlatform);

        return aiEmpowermentService.fileUploadSummary(command);
    }
}
