package cn.genn.pf.orch.meeting.application.service.query;

import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.application.assembler.MeetingAssembler;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IMeetingRepository;
import cn.genn.pf.orch.meeting.infrastructure.config.CurrentUserHolder;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.*;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.*;
import cn.genn.pf.orch.meeting.infrastructure.utils.DurationUtil;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingAttachmentDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingAttendUserDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingDetailDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingListDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.AttendUserDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleConfigDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.user.FSUserInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.*;
import cn.genn.pf.orch.meeting.interfaces.query.MeetingDetailQuery;
import cn.genn.pf.orch.meeting.interfaces.query.MeetingQuery;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议查询服务
 * @date 2025-01-02
 */
@Service
public class MeetingQueryService {

    @Resource
    private IMeetingRepository meetingRepository;
    @Resource
    private ScheduleQueryService scheduleQueryService;
    @Resource
    private MeetingMapper meetingMapper;
    @Resource
    private ScheduleAttendUserMapper scheduleAttendUserMapper;
    @Resource
    private MeetingAttendUserMapper meetingAttendUserMapper;
    @Resource
    private MeetingExtendMapper meetingExtendMapper;
    @Resource
    private MeetingAssembler meetingAssembler;
    @Resource
    private TeamLevelMapper teamLevelMapper;

    public MeetingAgg findByFSCalendarEventId(String calendarEventId) {
        // 根据日程事件id获取日程信息
        ScheduleDTO scheduleDTO = scheduleQueryService.getByFSCalendarEventId(calendarEventId);
        if (Objects.isNull(scheduleDTO)) {
            return null;
        }
        return meetingRepository.findByScheduleId(scheduleDTO.getId());
    }

    public MeetingAgg findByFsMeetingId(String fsMeetingId) {
        return meetingRepository.findByFsMeetingId(fsMeetingId);
    }

    public PageResultDTO<MeetingListDTO> page(MeetingQuery meetingQuery) {
        if(ObjUtil.isNotNull(meetingQuery.getCoalMiningRegionId()) && CollUtil.isEmpty(meetingQuery.getLevelIds())){
            List<TeamLevelPO> teamLevelPOS = teamLevelMapper.selectByLevel(2, meetingQuery.getCoalMiningRegionId());
            List<Long> levelIds = teamLevelPOS.stream().map(TeamLevelPO::getId).distinct().collect(Collectors.toList());
            if(CollUtil.isEmpty(levelIds)){
                return PageResultDTO.empty(meetingQuery.getPageNo(), meetingQuery.getPageSize());
            }
            meetingQuery.setLevelIds(levelIds);
        }
        // todo：待优化
        Page<MeetingListDTO> miniMeetingListDTOPage = meetingMapper.miniPage(new Page<>(meetingQuery.getPageNo(), meetingQuery.getPageSize()), meetingQuery);

        List<MeetingListDTO> records = miniMeetingListDTOPage.getRecords();
        if (records.isEmpty()) {
            return PageResultDTO.empty();
        }
        Map<Long, Long> meetingIdScheduleMap = records.stream().collect(Collectors.toMap(MeetingListDTO::getId, MeetingListDTO::getScheduleId));

        // 应参会人数赋值
        List<ScheduleAttendUserPO> scheduleExpectAttendUserPOS = scheduleAttendUserMapper.selectList(
            Wrappers.lambdaQuery(ScheduleAttendUserPO.class).in(ScheduleAttendUserPO::getScheduleId, meetingIdScheduleMap.values()));
        scheduleExpectAttendUserPOS.stream().collect(Collectors.groupingBy(ScheduleAttendUserPO::getScheduleId)).forEach((scheduleId, attendUserPOS) -> {
            MeetingListDTO meetingListDTO = records.stream().filter(record -> record.getScheduleId().equals(scheduleId)).findFirst().orElse(null);
            if (Objects.nonNull(meetingListDTO)) {
                meetingListDTO.setExpectUserCount(attendUserPOS.size());
            }
        });

        // 请假人数赋值
        List<ScheduleAttendUserPO> scheduleAttendUserPOS = scheduleAttendUserMapper.selectList(
            Wrappers.lambdaQuery(ScheduleAttendUserPO.class).in(ScheduleAttendUserPO::getScheduleId, meetingIdScheduleMap.values())
                .eq(ScheduleAttendUserPO::getAttendUserState, AttendUserStateEnum.ABSENT));
        scheduleAttendUserPOS.stream().collect(Collectors.groupingBy(ScheduleAttendUserPO::getScheduleId))
            .forEach((scheduleId, attendUserPOS) -> {
                MeetingListDTO meetingListDTO = records.stream().filter(record -> record.getScheduleId().equals(scheduleId)).findFirst().orElse(null);
                if (Objects.nonNull(meetingListDTO)) {
                    meetingListDTO.setAbsentUserCount(attendUserPOS.size());
                    meetingListDTO.setExpectCheckInUserCount(meetingListDTO.getExpectUserCount() - attendUserPOS.size());
                }
            });

        // 实际参会人数赋值
        List<MeetingAttendUserPO> attendUserPOList = meetingAttendUserMapper.selectList(Wrappers.lambdaQuery(MeetingAttendUserPO.class).in(MeetingAttendUserPO::getMeetingId, meetingIdScheduleMap.keySet())
            .eq(MeetingAttendUserPO::getCheckInStatus, CheckInStatusEnum.CHECKED));
        attendUserPOList.stream().collect(Collectors.groupingBy(MeetingAttendUserPO::getMeetingId)).forEach((meetingId, attendUserPOS) -> {
            MeetingListDTO meetingListDTO = records.stream().filter(record -> record.getId().equals(meetingId)).findFirst().orElse(null);
            if (Objects.nonNull(meetingListDTO)) {
                meetingListDTO.setActualUserCount(attendUserPOS.size());
                meetingListDTO.setCheckInUserCount(attendUserPOS.size());
            }
        });

        // 查找未上传记录的会议
        List<Long> notUploadedMeetingIds = meetingRepository.findNotUploadedMeetingIds();
        // 批量查找日程
        List<Long> scheduleIds = records.stream().map(MeetingListDTO::getScheduleId).collect(Collectors.toList());
        Map<Long, ScheduleDTO> scheduleDTOMap = scheduleQueryService.getByScheduleIds(scheduleIds).stream()
            .collect(Collectors.toMap(ScheduleDTO::getId, Function.identity()));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM月dd日 EEE", Locale.CHINA);
        records.forEach(miniMeetingListDTO -> {
            ScheduleDTO scheduleDTO = scheduleDTOMap.getOrDefault(miniMeetingListDTO.getScheduleId(), null);
            if (scheduleDTO != null) {
                List<MeetingAttendUserDTO> attendUserList = new ArrayList<>(scheduleDTO.getAttendUsers().size());
                scheduleDTO.getAttendUsers().forEach(attendUser -> {
                    MeetingAttendUserDTO attendUserDTO = meetingAssembler.toMeetingAttendUserDTO(attendUser);
                    List<MeetingAttendUserPO> meetingAttendUserPOS = meetingAttendUserMapper.selectList(Wrappers.lambdaQuery(MeetingAttendUserPO.class).eq(MeetingAttendUserPO::getMeetingId, miniMeetingListDTO.getId()));
                    MeetingAttendUserPO meetingAttendUserPO = meetingAttendUserPOS.stream().filter(a -> a.getFsUserId().equals(attendUser.getAttendUserId())).findFirst().orElse(null);
                    if (Objects.nonNull(meetingAttendUserPO)) {
                        meetingAssembler.fillMeetingAttendUserDTO(meetingAttendUserPO, attendUserDTO);
                    }
                    attendUserList.add(attendUserDTO);
                });
                miniMeetingListDTO.setAttendUserList(attendUserList);
                if (miniMeetingListDTO.getTeamInfoDTO() == null) {
                    miniMeetingListDTO.setTeamInfoDTO(scheduleDTO.getTeamInfoDTO());
                }

                // 是否是会议的评价人
                Set<String> reviewers = miniMeetingListDTO.getTeamInfoDTO().getReviewerInfos().stream().map(FSUserInfoDTO::getOpenId).collect(Collectors.toSet());
                miniMeetingListDTO.setIsEvaluator(reviewers.contains(CurrentUserHolder.getCurrentUser().getOpenId()) ? 1 : 0);
                // 设置是否是会议的拥有者
                miniMeetingListDTO.setIsOwner(scheduleDTO.getCreateUserId().equals(CurrentUserHolder.getCurrentUser().getOpenId()) ? 1 : 0);
                // 设置会议记录者
                List<FSUserInfoDTO> recorders = scheduleDTO.getAttendUsers().stream()
                    .filter(attendUserDTO -> AttendUserRoleEnum.RECORDER.equals(attendUserDTO.getAttendUserRole()))
                    .map(attendUserDTO -> FSUserInfoDTO.builder()
                        .name(attendUserDTO.getAttendUserName())
                        .openId(attendUserDTO.getAttendUserId())
                        .avatarUrl(attendUserDTO.getAvatarUrl())
                        .build())
                    .collect(Collectors.toList());
                miniMeetingListDTO.setRecorders(recorders);
                // 设置是否是会议记录者
                miniMeetingListDTO.setIsRecorder(recorders.stream().map(FSUserInfoDTO::getOpenId).collect(Collectors.toSet()).contains(CurrentUserHolder.getCurrentUser().getOpenId()) ? 1 : 0);
            }

            if(miniMeetingListDTO.getIsRecorder() == 1) {
                // 设置是否允许上传会议记录
                if (miniMeetingListDTO.getStatus().equals(MeetingStatusEnum.WAIT_START)) {
                    // 会议未开始
                    miniMeetingListDTO.setAllowUpload(0);
                } else if (miniMeetingListDTO.getStatus().equals(MeetingStatusEnum.IN_PROCESS)) {
                    // 会议进行中
                    miniMeetingListDTO.setAllowUpload(1);
                } else {
                    // 结束时间两小时内
                    if (miniMeetingListDTO.getEndTime().plusHours(2L).isAfter(LocalDateTime.now())) {
                        miniMeetingListDTO.setAllowUpload(1);
                    } else {
                        // 查找是否已经上传
                        if (notUploadedMeetingIds.contains(miniMeetingListDTO.getId())) {
                            // 未上传 允许
                            miniMeetingListDTO.setAllowUpload(1);
                        } else {
                            // 已经上传 不允许
                            miniMeetingListDTO.setAllowUpload(0);
                        }
                    }
                }
            } else {
                miniMeetingListDTO.setAllowUpload(0);
            }

            // 会议时长赋值
            miniMeetingListDTO.setDuration(DurationUtil.calculateDuration(miniMeetingListDTO.getStartTime(), miniMeetingListDTO.getEndTime()));
            miniMeetingListDTO.setScheduleDuration(DurationUtil.calculateDuration(miniMeetingListDTO.getScheduleStartTime(), miniMeetingListDTO.getScheduleEndTime()));
            miniMeetingListDTO.setScheduleStartTimeDayOfWeek(miniMeetingListDTO.getScheduleStartTime().format(formatter));

            // 设置是否允许签到
            if (miniMeetingListDTO.getStatus().equals(MeetingStatusEnum.WAIT_START)) {
                if (LocalDateTime.now().isAfter(miniMeetingListDTO.getScheduleStartTime().minusMinutes(30L))) {
                    // 会议未开始，前30分钟内允许签到
                    miniMeetingListDTO.setAllowCheckIn(1);
                } else {
                    miniMeetingListDTO.setAllowCheckIn(0);
                }
            } else if (miniMeetingListDTO.getStatus().equals(MeetingStatusEnum.IN_PROCESS)) {
                // 会议进行中，允许签到
                miniMeetingListDTO.setAllowCheckIn(1);
            } else {
                // 会议已结束, 不允许签到
                miniMeetingListDTO.setAllowCheckIn(0);
            }
            // 已经签到，不必显示签到按钮
            miniMeetingListDTO.getAttendUserList().stream()
                .filter(attendUser -> attendUser.getAttendUserId().equals(CurrentUserHolder.getCurrentUser().getOpenId()))
                .findFirst()
                .ifPresent(attendUser -> {
                    if (attendUser.getCheckInStatus() != null && attendUser.getCheckInStatus().equals(CheckInStatusEnum.CHECKED)) {
                        miniMeetingListDTO.setAllowCheckIn(0);
                    }
                });
            // 评价人不需要签到
            if (miniMeetingListDTO.getIsEvaluator() == 1 && miniMeetingListDTO.getIsRecorder() == 0 && miniMeetingListDTO.getIsOwner() == 0) {
                miniMeetingListDTO.setAllowCheckIn(0);
            }
        });
        return meetingAssembler.toMiniPageResult(miniMeetingListDTOPage);
    }

    public MeetingDetailDTO detail(MeetingDetailQuery query) {
        // todo：待优化
        MeetingPO meetingPO = meetingMapper.selectById(query.getId());
        MeetingDetailDTO meetingDetailDTO = meetingAssembler.toMeetingDetailDTO(meetingPO);

        List<MeetingAttendUserPO> meetingAttendUserPOS = meetingAttendUserMapper.selectList(Wrappers.lambdaQuery(MeetingAttendUserPO.class).eq(MeetingAttendUserPO::getMeetingId, query.getId()));

        ScheduleDTO scheduleDTO = scheduleQueryService.getByScheduleId(meetingPO.getScheduleId());
        meetingDetailDTO.setScheduleId(scheduleDTO.getId());
        meetingDetailDTO.setScheduleStartTime(scheduleDTO.getStartTime());
        meetingDetailDTO.setScheduleEndTime(scheduleDTO.getEndTime());

        // 赋值应参会，请假人数，实际参会人数, 应签到人数, 实际签到人数
        List<AttendUserDTO> attendUsers = scheduleDTO.getAttendUsers();
        long absentUserCount = attendUsers.stream().filter(a -> a.getAttendUserState().equals(AttendUserStateEnum.ABSENT)).count();
        meetingDetailDTO.setExpectUserCount(attendUsers.size());
        meetingDetailDTO.setAbsentUserCount((int) absentUserCount);
        meetingDetailDTO.setExpectCheckInUserCount(attendUsers.size() - (int) absentUserCount);
        long checkInUserCount = meetingAttendUserPOS.stream().filter(a -> CheckInStatusEnum.CHECKED.equals(a.getCheckInStatus())).count();
        meetingDetailDTO.setActualUserCount((int) checkInUserCount);
        meetingDetailDTO.setCheckInUserCount((int) checkInUserCount);

        // 会议记录人
        List<String> recorders = attendUsers.stream().filter(a -> a.getAttendUserRole().equals(AttendUserRoleEnum.RECORDER)).map(AttendUserDTO::getAttendUserName).collect(Collectors.toList());
        meetingDetailDTO.setRecorders(recorders);

        // 参会人列表
        List<MeetingAttendUserDTO> attendUserList = new ArrayList<>(scheduleDTO.getAttendUsers().size());
        scheduleDTO.getAttendUsers().forEach(attendUserCommand -> {
            MeetingAttendUserDTO attendUserDTO = meetingAssembler.toMeetingAttendUserDTO(attendUserCommand);
            MeetingAttendUserPO meetingAttendUserPO = meetingAttendUserPOS.stream().filter(a -> a.getFsUserId().equals(attendUserCommand.getAttendUserId())).findFirst().orElse(null);
            if (Objects.nonNull(meetingAttendUserPO)) {
                meetingAssembler.fillMeetingAttendUserDTO(meetingAttendUserPO, attendUserDTO);
            } else {
                attendUserDTO.setCheckInStatus(CheckInStatusEnum.WAIT);
            }
            if (Objects.nonNull(attendUserDTO.getInMeetingDuration())) {
                attendUserDTO.setInMeetingDuration(DurationUtil.calculateDuration(attendUserDTO.getFirstJoinTime(), attendUserDTO.getFinalLeaveTime()));
            }
            attendUserList.add(attendUserDTO);
        });
        meetingDetailDTO.setAttendUserList(attendUserList);

        // 会议附件
        MeetingExtendPO meetingExtendPO = meetingExtendMapper.selectOne(Wrappers.lambdaQuery(MeetingExtendPO.class).eq(MeetingExtendPO::getMeetingId, query.getId()));

        // 当前用户身份
        boolean isRecorder = scheduleDTO.getAttendUsers().stream()
            .filter(attendUserDTO -> AttendUserRoleEnum.RECORDER.equals(attendUserDTO.getAttendUserRole()))
            .map(AttendUserDTO::getAttendUserId)
            .collect(Collectors.toSet()).contains(CurrentUserHolder.getCurrentUser().getOpenId());
        boolean isReviewer = meetingExtendPO != null && meetingExtendPO.getTeamInfo().getReviewerInfos().stream()
            .map(FSUserInfoDTO::getOpenId)
            .collect(Collectors.toSet())
            .contains(CurrentUserHolder.getCurrentUser().getOpenId());

        // 设置会议记录和会议评价
        if (meetingExtendPO != null) {
            // 存在会议记录草稿，并且是记录人，展示草稿
            if (query.isDraft() && StrUtil.isNotBlank(meetingExtendPO.getRecordDraft())
                && isRecorder && CurrentUserHolder.getCurrentUser().getClientType().equals(ClientTypeEnum.MINI)) {
                meetingDetailDTO.setRecord(meetingExtendPO.getRecordDraft());
                meetingDetailDTO.setIsRecordDraft(1);
            } else {
                meetingDetailDTO.setRecord(meetingExtendPO.getRecord());
                meetingDetailDTO.setIsRecordDraft(0);
            }
            // 存在会议评价草稿，并且是评价人，展示草稿
            if (query.isDraft() && StrUtil.isNotBlank(meetingExtendPO.getEvaluateDraft())
                && isReviewer && CurrentUserHolder.getCurrentUser().getClientType().equals(ClientTypeEnum.MINI)) {
                meetingDetailDTO.setEvaluate(meetingExtendPO.getEvaluateDraft());
                meetingDetailDTO.setIsEvaluateDraft(1);
            } else {
                meetingDetailDTO.setEvaluate(meetingExtendPO.getEvaluate());
                meetingDetailDTO.setIsEvaluateDraft(0);
            }
        }
        if (meetingExtendPO != null && meetingExtendPO.getTeamInfo() != null) {
            meetingDetailDTO.setTeamInfoDTO(meetingExtendPO.getTeamInfo());
        } else {
            meetingDetailDTO.setTeamInfoDTO(scheduleDTO.getTeamInfoDTO());
        }

        // 初始化 dataCatalog 和 achievementCatalog 列表
        List<MeetingAttachmentDTO> dataCatalogList = new ArrayList<>();
        List<MeetingAttachmentDTO> achievementCatalogList = new ArrayList<>();

        // 如果 meetingExtendPO 为 null，初始化 dataCatalogDTOS 和 achievementCatalogDTOS 为空列表
        List<MeetingAttachmentDTO> achievementCatalogDTOS;
        List<MeetingAttachmentDTO> dataCatalogDTOS;
        if (meetingExtendPO != null) {
            // 存在草稿，展示草稿
            if (query.isDraft() && meetingExtendPO.getAchievementCatalogDraft() != null
                && !meetingExtendPO.getAchievementCatalogDraft().isEmpty()
                && isRecorder && CurrentUserHolder.getCurrentUser().getClientType().equals(ClientTypeEnum.MINI)) {
                achievementCatalogDTOS = meetingExtendPO.getAchievementCatalogDraft();
            } else {
                achievementCatalogDTOS = meetingExtendPO.getAchievementCatalog() == null ? new ArrayList<>() : meetingExtendPO.getAchievementCatalog();
            }
            // 存在草稿，展示草稿
            if (query.isDraft() && meetingExtendPO.getDataCatalogDraft() != null
                && !meetingExtendPO.getDataCatalogDraft().isEmpty()
                && isRecorder && CurrentUserHolder.getCurrentUser().getClientType().equals(ClientTypeEnum.MINI)) {
                dataCatalogDTOS = meetingExtendPO.getDataCatalogDraft();
            } else {
                dataCatalogDTOS = meetingExtendPO.getDataCatalog() == null ? new ArrayList<>() : meetingExtendPO.getDataCatalog();
            }
        } else {
            achievementCatalogDTOS = new ArrayList<>();
            dataCatalogDTOS = new ArrayList<>();
        }

        // 处理 dataCatalog
        ScheduleConfigDTO scheduleConfig = null;
        if (meetingExtendPO != null) {
            scheduleConfig = meetingExtendPO.getScheduleConfig();
        }
        if (scheduleConfig == null) {
            scheduleConfig = scheduleDTO.getScheduleConfigDTO();
        }
        scheduleConfig.getDataCatalog().forEach(dataCatalog -> {
            MeetingAttachmentDTO meetingAttachmentDTO = dataCatalogDTOS.stream()
                .filter(a -> a.getKey().equals(dataCatalog))
                .findFirst()
                .orElse(null);

            if (meetingAttachmentDTO == null) {
                meetingAttachmentDTO = new MeetingAttachmentDTO();
                meetingAttachmentDTO.setKey(dataCatalog);
                meetingAttachmentDTO.setValue(new ArrayList<>());
            }

            dataCatalogList.add(meetingAttachmentDTO);
        });

        // 处理 achievementCatalog
        if (!achievementCatalogDTOS.isEmpty()) {
            achievementCatalogDTOS.forEach(achievement -> {
                if (achievement != null) {
                    achievementCatalogList.add(achievement);
                }
            });
        } else {
            // 如果没有会议成果资料，创建空的 DTO
            scheduleConfig.getAchievementCatalog().forEach(achievementKey -> {
                MeetingAttachmentDTO achievementDTO = new MeetingAttachmentDTO();
                achievementDTO.setKey(achievementKey);
                achievementDTO.setValue(new ArrayList<>());
                achievementCatalogList.add(achievementDTO);
            });
        }

        // 设置会议详情 DTO 的数据目录和会议成果资料
        meetingDetailDTO.setDataCatalog(dataCatalogList);
        meetingDetailDTO.setAchievementCatalog(achievementCatalogList);
        return meetingDetailDTO;
    }

    public List<String> queryValidCheckInNumbers() {
        return meetingMapper.queryValidCheckInNumbers();
    }


    public PageResultDTO<MeetingListDTO> miniPage(MeetingQuery meetingQuery) {
        meetingQuery.setCurrentUserId(CurrentUserHolder.getCurrentUser().getOpenId());
        return page(meetingQuery);
    }
}
