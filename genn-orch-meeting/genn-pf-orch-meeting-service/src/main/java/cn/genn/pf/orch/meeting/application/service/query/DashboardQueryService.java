package cn.genn.pf.orch.meeting.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.dashboard.DashboardKpiDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.dashboard.DashboardTrendDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.dashboard.RecentMeetingDTO;
import cn.genn.pf.orch.meeting.interfaces.query.DashboardKpiQuery;
import cn.genn.pf.orch.meeting.interfaces.query.DashboardTrendQuery;
import cn.genn.pf.orch.meeting.interfaces.query.RecentMeetingQuery;

/**
 * <AUTHOR>
 * @description 仪表板查询服务接口
 * @date 2025-01-24
 */
public interface DashboardQueryService {

    /**
     * 获取KPI数据
     */
    DashboardKpiDTO getKpi(DashboardKpiQuery query);

    /**
     * 获取趋势分析数据
     */
    DashboardTrendDTO getTrend(DashboardTrendQuery query);

    /**
     * 获取近期会议列表
     */
    PageResultDTO<RecentMeetingDTO> getRecentMeetings(RecentMeetingQuery query);
} 