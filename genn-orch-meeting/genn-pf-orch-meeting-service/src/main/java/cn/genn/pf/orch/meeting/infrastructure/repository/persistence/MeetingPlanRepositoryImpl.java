package cn.genn.pf.orch.meeting.infrastructure.repository.persistence;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.pf.orch.meeting.domain.meetingplan.model.entity.MeetingPlan;
import cn.genn.pf.orch.meeting.domain.meetingplan.repository.IMeetingPlanRepository;
import cn.genn.pf.orch.meeting.infrastructure.converter.MeetingPlanConverter;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingPlanMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPlanPO;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingPlanStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议规划仓储实现
 * @date 2025-01-24
 */
@Repository
public class MeetingPlanRepositoryImpl implements IMeetingPlanRepository {

    @Resource
    private MeetingPlanMapper meetingPlanMapper;
    @Resource
    private MeetingPlanConverter meetingPlanConverter;

    @Override
    public void save(MeetingPlan meetingPlan) {
        MeetingPlanPO po = meetingPlanConverter.toPO(meetingPlan);
        meetingPlanMapper.insert(po);
        meetingPlan.setId(po.getId());
    }

    @Override
    public void update(MeetingPlan meetingPlan) {
        MeetingPlanPO po = meetingPlanConverter.toPO(meetingPlan);
        meetingPlanMapper.updateById(po);
    }

    @Override
    public MeetingPlan findById(Long id) {
        MeetingPlanPO po = meetingPlanMapper.selectById(id);
        return po != null ? meetingPlanConverter.toEntity(po) : null;
    }

    @Override
    public void deleteById(Long id) {
        meetingPlanMapper.deleteById(id);
    }

    @Override
    public List<MeetingPlan> findConflictPlans(LocalDateTime startTime, LocalDateTime endTime,
                                              String location, List<String> attendees, Long excludeId) {
        // 先查询所有时间冲突的会议
        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
            .ne(excludeId != null, MeetingPlanPO::getId, excludeId)
            .eq(MeetingPlanPO::getStatus, MeetingPlanStatusEnum.NOT_STARTED)
            .and(w -> w
                .and(w1 -> w1
                    .le(MeetingPlanPO::getPlannedStartTime, startTime)
                    .gt(MeetingPlanPO::getPlannedEndTime, startTime))
                .or(w2 -> w2
                    .lt(MeetingPlanPO::getPlannedStartTime, endTime)
                    .ge(MeetingPlanPO::getPlannedEndTime, endTime))
                .or(w3 -> w3
                    .ge(MeetingPlanPO::getPlannedStartTime, startTime)
                    .le(MeetingPlanPO::getPlannedEndTime, endTime)));

        List<MeetingPlanPO> timeConflictPOs = meetingPlanMapper.selectList(wrapper);

        return timeConflictPOs.stream()
            .map(meetingPlanConverter::toEntity)
            .filter(plan -> hasLocationConflict(plan.getMeetingLocation(), location) ||
                           hasAttendeeConflict(plan.getAttendees(), attendees))
            .collect(Collectors.toList());
    }

    @Override
    public List<MeetingPlan> findUpcomingPlans(int advanceNoticeDays) {
        LocalDateTime targetDate = LocalDateTime.now().plusDays(advanceNoticeDays);
        LocalDateTime startOfDay = targetDate.toLocalDate().atStartOfDay();
        LocalDateTime endOfDay = targetDate.toLocalDate().atTime(23, 59, 59);

        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
            .eq(MeetingPlanPO::getStatus, MeetingPlanStatusEnum.NOT_STARTED)
            .between(MeetingPlanPO::getPlannedStartTime, startOfDay, endOfDay);

        List<MeetingPlanPO> pos = meetingPlanMapper.selectList(wrapper);
        return pos.stream()
            .map(meetingPlanConverter::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    public List<MeetingPlan> findOverduePlans() {
        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
            .eq(MeetingPlanPO::getStatus, MeetingPlanStatusEnum.NOT_STARTED)
            .lt(MeetingPlanPO::getPlannedStartTime, LocalDateTime.now());

        List<MeetingPlanPO> pos = meetingPlanMapper.selectList(wrapper);
        return pos.stream()
            .map(meetingPlanConverter::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    public List<MeetingPlan> findPlansForNotification(Long standardId, LocalDateTime startTime, LocalDateTime endTime, int noticeMinutes) {
        // 计算会议开始时间应该在哪个时间窗口内
        LocalDateTime meetingStartTime = LocalDateTime.now().plusMinutes(noticeMinutes);
        LocalDateTime windowStart = meetingStartTime.minusMinutes(1);
        LocalDateTime windowEnd = meetingStartTime.plusMinutes(1);
        
        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
            .eq(MeetingPlanPO::getMeetingStandardId, standardId)
            .eq(MeetingPlanPO::getStatus, MeetingPlanStatusEnum.NOT_STARTED)
            .eq(MeetingPlanPO::getAdvanceNoticeSent, 0) // 只查询未发送通知的会议规划
            .between(MeetingPlanPO::getPlannedStartTime, windowStart, windowEnd)
            .eq(MeetingPlanPO::getDeleted, DeletedEnum.NOT_DELETED);
        
        List<MeetingPlanPO> pos = meetingPlanMapper.selectList(wrapper);
        return pos.stream()
            .map(meetingPlanConverter::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    public void updateAdvanceNoticeSent(Long planId, Integer sent) {
        MeetingPlanPO po = new MeetingPlanPO();
        po.setId(planId);
        po.setAdvanceNoticeSent(sent);
        po.setUpdateTime(LocalDateTime.now());
        meetingPlanMapper.updateById(po);
    }

    /**
     * 检查会议室是否有冲突
     */
    private boolean hasLocationConflict(String location1, String location2) {
        if (location1 == null || location2 == null) {
            return false; // 任一方没有指定会议室，不算冲突
        }
        return location1.equals(location2);
    }

    /**
     * 检查参会人员是否有冲突
     */
    private boolean hasAttendeeConflict(List<String> attendees1, List<String> attendees2) {
        if (attendees1 == null || attendees2 == null ||
            attendees1.isEmpty() || attendees2.isEmpty()) {
            return false; // 任一方没有参会人员，不算冲突
        }
        return attendees1.stream().anyMatch(attendees2::contains);
    }
}
