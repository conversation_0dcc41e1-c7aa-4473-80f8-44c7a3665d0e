package cn.genn.pf.orch.meeting.application.assembler;

import cn.genn.pf.orch.meeting.domain.business.model.entity.BusinessMeeting;
import cn.genn.pf.orch.meeting.interfaces.command.AddBusinessMeetingCommand;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface BusinessMeetingAssembler {

    BusinessMeeting toBusinessMeeting(AddBusinessMeetingCommand addBusinessMeetingCommand);
}
