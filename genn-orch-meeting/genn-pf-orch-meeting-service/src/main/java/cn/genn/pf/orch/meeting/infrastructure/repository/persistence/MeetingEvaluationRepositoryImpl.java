package cn.genn.pf.orch.meeting.infrastructure.repository.persistence;

import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingEvaluationAgg;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingEvaluationInfo;
import cn.genn.pf.orch.meeting.domain.meeting.repository.IMeetingEvaluationRepository;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingEvaluationMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingEvaluationPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议评价仓储实现
 * @date 2025-01-24
 */
@Slf4j
@Repository
public class MeetingEvaluationRepositoryImpl implements IMeetingEvaluationRepository {
    
    @Resource
    private MeetingEvaluationMapper meetingEvaluationMapper;
    
    @Override
    public void save(MeetingEvaluationAgg evaluationAgg) {
        MeetingEvaluationInfo info = evaluationAgg.getInfo();
        
        MeetingEvaluationPO po = MeetingEvaluationPO.builder()
            .meetingId(info.getMeetingId())
            .evaluatorOpenId(info.getEvaluatorOpenId())
            .evaluatorName(info.getEvaluatorName())
            .meetingScore(info.getMeetingScore())
            .contentScore(info.getContentScore())
            .durationScore(info.getDurationScore())
            .effectivenessScore(info.getEffectivenessScore())
            .suggestions(info.getSuggestions())
            .evaluationTime(info.getEvaluationTime())
            .deleted(false)
            .build();
        
        meetingEvaluationMapper.insert(po);
        info.setId(po.getId());
    }
    
    @Override
    public MeetingEvaluationAgg findByMeetingIdAndEvaluator(Long meetingId, String evaluatorOpenId) {
        MeetingEvaluationPO po = meetingEvaluationMapper.selectByMeetingIdAndEvaluator(meetingId, evaluatorOpenId);
        
        if (po == null) {
            return null;
        }
        
        MeetingEvaluationInfo info = MeetingEvaluationInfo.builder()
            .id(po.getId())
            .meetingId(po.getMeetingId())
            .evaluatorOpenId(po.getEvaluatorOpenId())
            .evaluatorName(po.getEvaluatorName())
            .meetingScore(po.getMeetingScore())
            .contentScore(po.getContentScore())
            .durationScore(po.getDurationScore())
            .effectivenessScore(po.getEffectivenessScore())
            .suggestions(po.getSuggestions())
            .evaluationTime(po.getEvaluationTime())
            .createTime(po.getCreateTime())
            .updateTime(po.getUpdateTime())
            .deleted(po.getDeleted())
            .build();
        
        return MeetingEvaluationAgg.builder()
            .info(info)
            .build();
    }
} 