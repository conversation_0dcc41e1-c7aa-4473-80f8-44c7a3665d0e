package cn.genn.pf.orch.meeting.application.handler;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.infrastructure.enums.EventCallbackEnum;
import cn.genn.pf.orch.meeting.infrastructure.feishu.FeishuApiService;
import cn.genn.pf.orch.meeting.interfaces.command.CardActionEventCommand;
import cn.genn.third.feishu.app.model.callback.EventCallbackCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Assistant
 * @description 卡片交互事件处理器
 */
@Slf4j
@Component
public class CardActionHandler implements CallbackHandler<CardActionEventCommand> {

    @Resource
    private FeishuApiService feishuApiService;

    @Override
    public void handle(EventCallbackCommand<CardActionEventCommand> command) {
        try {
            // 直接使用传入的事件数据
            CardActionEventCommand cardEvent = command.getEvent();

            log.info("收到卡片交互事件，messageId: {}, userId: {}, action: {}",
                    cardEvent.getContext().getOpenMessageId(),
                    cardEvent.getOperator().getOpenId(),
                    cardEvent.getAction().getValue().getAction());

            // 处理卡片交互逻辑
            handleCardAction(cardEvent);

            // 异步5秒后禁用卡片
            disableCardAfterDelay(cardEvent.getContext().getOpenMessageId());

        } catch (Exception e) {
            log.error("处理卡片交互事件失败", e);
        }
    }

    /**
     * 处理卡片交互逻辑
     */
    private void handleCardAction(CardActionEventCommand cardEvent) {
        String actionValue = cardEvent.getAction().getValue().getAction();
        String userId = cardEvent.getOperator().getOpenId();

        log.info("处理卡片交互，用户: {}, 操作: {}", userId, actionValue);

        // 根据不同的操作值处理不同的业务逻辑
        switch (actionValue) {
            case "view_task_detail":
                handleViewTaskDetail(cardEvent);
                break;
            case "mark_task_complete":
                handleMarkTaskComplete(cardEvent);
                break;
            case "view_meeting_detail":
                handleViewMeetingDetail(cardEvent);
                break;
            case "test_action_1":
                handleTestAction1(cardEvent);
                break;
            case "test_action_2":
                handleTestAction2(cardEvent);
                break;
            default:
                log.warn("未知的卡片操作: {}", actionValue);
                break;
        }
    }

    /**
     * 处理查看任务详情
     */
    private void handleViewTaskDetail(CardActionEventCommand cardEvent) {
        log.info("用户 {} 查看任务详情", cardEvent.getOperator().getOpenId());
        // 这里可以记录用户行为、统计点击等
    }

    /**
     * 处理标记任务完成
     */
    private void handleMarkTaskComplete(CardActionEventCommand cardEvent) {
        log.info("用户 {} 标记任务完成", cardEvent.getOperator().getOpenId());
        // 这里可以调用任务完成的业务逻辑
        // 例如：taskActionService.markComplete(taskId, userId);
    }

    /**
     * 处理查看会议详情
     */
    private void handleViewMeetingDetail(CardActionEventCommand cardEvent) {
        log.info("用户 {} 查看会议详情", cardEvent.getOperator().getOpenId());
        // 这里可以记录用户行为、统计点击等
    }

    /**
     * 处理测试按钮1
     */
    private void handleTestAction1(CardActionEventCommand cardEvent) {
        log.info("用户 {} 点击了测试按钮1", cardEvent.getOperator().getOpenId());
        // 模拟一些业务处理
        log.info("执行测试操作1的业务逻辑...");
    }

    /**
     * 处理测试按钮2
     */
    private void handleTestAction2(CardActionEventCommand cardEvent) {
        log.info("用户 {} 点击了测试按钮2", cardEvent.getOperator().getOpenId());
        // 模拟一些业务处理
        log.info("执行测试操作2的业务逻辑...");
    }

    /**
     * 异步5秒后禁用卡片
     */
    private void disableCardAfterDelay(String messageId) {
        CompletableFuture.runAsync(() -> {
            try {
                // 等待5秒
                TimeUnit.SECONDS.sleep(5);

                // 构建禁用状态的卡片内容
                String disabledCardContent = buildDisabledCardContent();

                // 更新卡片
                feishuApiService.updateMessage(messageId, disabledCardContent);

                log.info("卡片已禁用，messageId: {}", messageId);

            } catch (Exception e) {
                log.error("禁用卡片失败，messageId: {}", messageId, e);
            }
        });
    }

    /**
     * 构建禁用状态的卡片内容
     */
    private String buildDisabledCardContent() {
        return "{\n" +
               "  \"config\": {\n" +
               "    \"wide_screen_mode\": true\n" +
               "  },\n" +
               "  \"header\": {\n" +
               "    \"template\": \"grey\",\n" +
               "    \"title\": {\n" +
               "      \"tag\": \"plain_text\",\n" +
               "      \"content\": \"⚪ 卡片已失效\"\n" +
               "    }\n" +
               "  },\n" +
               "  \"elements\": [\n" +
               "    {\n" +
               "      \"tag\": \"div\",\n" +
               "      \"text\": {\n" +
               "        \"tag\": \"lark_md\",\n" +
               "        \"content\": \"此卡片已失效，请通过其他方式查看详情。\"\n" +
               "      }\n" +
               "    }\n" +
               "  ]\n" +
               "}";
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.CARD_ACTION;
    }
}
