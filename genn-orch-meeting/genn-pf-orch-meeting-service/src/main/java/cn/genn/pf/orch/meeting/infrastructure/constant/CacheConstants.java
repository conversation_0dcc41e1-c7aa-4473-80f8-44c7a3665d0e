package cn.genn.pf.orch.meeting.infrastructure.constant;

/**
 * 缓存相关key定义
 *
 * <AUTHOR>
 */
public class CacheConstants {

    /**
     * 统一的缓存前缀
     */
    public static final String CACHE_PRE = "GENN:MEETING:CORE";
    public static final String LOCK_PRE = "GENN:MEETING:LOCK";


    /**
     * 具体的缓存key
     */
    // 认证token
    public static final String AUTH_TOKEN = CACHE_PRE + ":AUTH_TOKEN:";
    // 飞书openId
    public static final String FEISHU_OPENID = CACHE_PRE + ":FEISHU_OPENID:";

    /**
     * 分布式锁相关key
     */
    //发送签到码通知
    public static final String SIGN_IN_CODE_LOCK = LOCK_PRE + ":SIGN_IN_CODE_LOCK:";
    // 飞书回调事件
    public static final String FS_CALLBACK_EVENT_LOCK = LOCK_PRE + ":FS_CALLBACK_EVENT:";


    public static String getAuthToken(String token){
        return AUTH_TOKEN + token;
    }

    public static String getFeishuOpenId(String clientType,String openId) {
        return FEISHU_OPENID + clientType+"_"+openId;
    }

    public static String getSignInCodeLock(Long meetingId) {
        return SIGN_IN_CODE_LOCK + meetingId;
    }

    public static String getFsCallbackEvent(String eventId){
        return FS_CALLBACK_EVENT_LOCK + eventId;
    }


}
