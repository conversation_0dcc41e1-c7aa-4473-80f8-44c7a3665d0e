package cn.genn.pf.orch.meeting.infrastructure.config;

import com.lark.oapi.Client;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 飞书配置
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "genn.third.feishu.enabled", havingValue = "true", matchIfMissing = true)
public class LarkConfig {

    @Resource
    private LarkConfigProperties larkConfigProperties;

    @Bean
    public Client larkClient() {
        // 直接读取third下的飞书配置，虽然是数组但只有一个
        LarkConfigProperties.AppConfig appConfig = larkConfigProperties.getFirstAppConfig();

        if (appConfig == null) {
            log.error("未找到飞书应用配置");
            throw new RuntimeException("未找到飞书应用配置");
        }

        String appId = appConfig.getAppId();
        String appSecret = appConfig.getAppSecret();
        String code = appConfig.getCode();

        log.info("初始化飞书客户端，code: {}, appId: {}", code, appId);

        if (appId == null || appSecret == null) {
            log.error("飞书应用配置不完整，appId: {}, appSecret: {}", appId, appSecret != null ? "***" : null);
            throw new RuntimeException("飞书应用配置不完整");
        }

        try {
            Client client = Client.newBuilder(appId, appSecret)
                    .logReqAtDebug(true) // 在debug模式下会打印http请求和响应的headers,body等信息.
                    .build();

            log.info("飞书客户端初始化成功");
            return client;

        } catch (Exception e) {
            log.error("飞书客户端初始化失败", e);
            throw new RuntimeException("飞书客户端初始化失败", e);
        }
    }

    /**
     * 飞书配置属性类
     */
    @Data
    @Component
    @ConfigurationProperties(prefix = "genn.third.feishu")
    public static class LarkConfigProperties {

        /**
         * 是否启用飞书功能
         */
        private boolean enabled = true;

        /**
         * 小程序配置
         */
        private MiniConfig mini;

        @Data
        public static class MiniConfig {
            /**
             * 配置列表
             */
            private List<AppConfig> config;
        }

        @Data
        public static class AppConfig {
            /**
             * 应用代码
             */
            private String code;

            /**
             * 应用ID
             */
            private String appId;

            /**
             * 应用密钥
             */
            private String appSecret;
        }

        /**
         * 获取第一个应用配置（因为虽然是数组，但只有一个）
         *
         * @return 应用配置
         */
        public AppConfig getFirstAppConfig() {
            if (mini == null || mini.getConfig() == null || mini.getConfig().isEmpty()) {
                return null;
            }
            return mini.getConfig().get(0);
        }

        /**
         * 根据code获取应用配置
         *
         * @param code 应用代码
         * @return 应用配置
         */
        public AppConfig getAppConfigByCode(String code) {
            if (mini == null || mini.getConfig() == null) {
                return null;
            }

            return mini.getConfig().stream()
                    .filter(config -> code.equals(config.getCode()))
                    .findFirst()
                    .orElse(null);
        }
    }
}
