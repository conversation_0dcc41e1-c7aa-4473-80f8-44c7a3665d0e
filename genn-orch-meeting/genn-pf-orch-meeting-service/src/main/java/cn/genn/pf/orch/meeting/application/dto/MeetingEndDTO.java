package cn.genn.pf.orch.meeting.application.dto;

import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import com.lark.oapi.service.vc.v1.model.GetMeetingRespBody;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议结束对象
 * @date 2024-12-31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingEndDTO {

    private Long scheduleId;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String meetingNo;

    private String calendarEventId;

    private ScheduleDTO schedule;

    private GetMeetingRespBody feishuMeeting;
}
