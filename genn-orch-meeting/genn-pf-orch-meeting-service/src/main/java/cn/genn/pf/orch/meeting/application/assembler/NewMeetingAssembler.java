package cn.genn.pf.orch.meeting.application.assembler;

import cn.genn.pf.orch.meeting.domain.newmeeting.model.entity.NewMeeting;
import cn.genn.pf.orch.meeting.interfaces.command.newmeeting.NewMeetingCreateCommand;
import cn.genn.pf.orch.meeting.interfaces.command.newmeeting.NewMeetingUpdateCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.newmeeting.NewMeetingDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.newmeeting.NewMeetingListDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议装配器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring")
public interface NewMeetingAssembler {

    /**
     * Command转Entity
     */
    NewMeeting toEntity(NewMeetingCreateCommand command);

    /**
     * Command转Entity（更新）
     */
    @Mapping(target = "id", source = "id")
    NewMeeting toEntity(NewMeetingUpdateCommand command);

    /**
     * Entity转DTO
     */
    NewMeetingDTO toDTO(NewMeeting entity);

    /**
     * Entity列表转DTO列表
     */
    List<NewMeetingDTO> toDTOList(List<NewMeeting> entityList);

    /**
     * Entity转分页列表DTO
     */
    NewMeetingListDTO toListDTO(NewMeeting entity);

    /**
     * Entity列表转分页列表DTO列表
     */
    List<NewMeetingListDTO> toListDTOList(List<NewMeeting> entityList);
} 