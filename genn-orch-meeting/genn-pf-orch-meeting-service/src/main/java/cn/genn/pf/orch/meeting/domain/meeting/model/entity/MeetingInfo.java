package cn.genn.pf.orch.meeting.domain.meeting.model.entity;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议信息
 * @date 2024-12-31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingInfo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 日程id
     */
    private Long scheduleId;

    /**
     * 飞书会议id
     */
    private String fsMeetingId;

    /**
     * 会议名称
     */
    private String name;

    /**
     * 会议号
     */
    private String meetingNo;

    /**
     * 会议签到码
     */
    private String checkInNumber;

    /**
     * 会议开始时间
     */
    private LocalDateTime startTime;

    /**
     * 会议结束时间
     */
    private LocalDateTime endTime;

    /**
     * 会议状态 0-未开始, 1-进行中, 2-已结束, 3-已取消
     */
    private MeetingStatusEnum status;

    /**
     * 会议链接
     */
    private String meetingUrl;

    /**
     * 妙计链接
     */
    private String minuteUrl;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人id
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标识
     */
    private DeletedEnum deleted;
}
