package cn.genn.pf.orch.meeting.infrastructure.config;

import cn.genn.core.exception.BusinessException;
import cn.genn.third.feishu.app.FeishuAppClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
public class FeishuAppClientConfig {

    @Bean
    public FeishuAppClient feishuAppClient(Map<String, FeishuAppClient> feishuAppClientMap) {
        if(!feishuAppClientMap.isEmpty()){
            return feishuAppClientMap.entrySet().iterator().next().getValue();
        }
        throw new BusinessException("请完善飞书配置");
    }
}
