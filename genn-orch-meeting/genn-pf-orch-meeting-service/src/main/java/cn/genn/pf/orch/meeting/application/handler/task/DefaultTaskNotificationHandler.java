package cn.genn.pf.orch.meeting.application.handler.task;

import cn.genn.pf.orch.meeting.infrastructure.enums.TaskNotificationTypeEnum;
import com.lark.oapi.service.task.v1.model.P2TaskUpdatedV1Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 默认任务通知处理器
 */
@Slf4j
@Component
public class DefaultTaskNotificationHandler implements TaskNotificationHandler {

    public DefaultTaskNotificationHandler() {

    }

    @Override
    public void handle(P2TaskUpdatedV1Data notification) {
        log.info("收到默认任务通知，飞书任务ID：{}，通知类型：{}",
                notification.getTaskId(), notification.getObjType());
    }

    @Override
    public TaskNotificationTypeEnum getObjType() {
        return null;
    }
}
