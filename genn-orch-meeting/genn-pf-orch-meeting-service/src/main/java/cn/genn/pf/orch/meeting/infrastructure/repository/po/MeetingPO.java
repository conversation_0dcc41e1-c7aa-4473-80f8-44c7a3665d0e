package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingStatusEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * MeetingPO对象
 *
 * <AUTHOR>
 * @desc 会议表
 */
@Data
@Accessors(chain = true)
@TableName(value = "meeting", autoResultMap = true)
public class MeetingPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 日程id
     */
    @TableField("schedule_id")
    private Long scheduleId;

    /**
     * 飞书会议id
     */
    @TableField("fs_meeting_id")
    private String fsMeetingId;

    /**
     * 会议名称
     */
    @TableField("name")
    private String name;

    /**
     * 会议号
     */
    @TableField("meeting_no")
    private String meetingNo;

    /**
     * 会议签到码
     */
    @TableField("check_in_number")
    private String checkInNumber;

    /**
     * 会议开始时间
     */
    @TableField("start_time")
    private LocalDateTime startTime;

    /**
     * 会议结束时间
     */
    @TableField("end_time")
    private LocalDateTime endTime;

    /**
     * 会议状态 0-未开始, 1-进行中, 2-已结束, 3-已取消
     */
    @TableField("status")
    private MeetingStatusEnum status;

    /**
     * 会议链接
     */
    @TableField("meeting_url")
    private String meetingUrl;

    /**
     * 妙计链接
     */
    @TableField("minute_url")
    private String minuteUrl;

    /**
     * 创建人id
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人名称
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @TableField("deleted")
    private DeletedEnum deleted;

}

