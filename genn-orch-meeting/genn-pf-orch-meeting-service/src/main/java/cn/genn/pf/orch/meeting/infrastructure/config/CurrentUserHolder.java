package cn.genn.pf.orch.meeting.infrastructure.config;

import cn.genn.core.exception.BusinessException;
import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.interfaces.dto.user.UserInfoDTO;
import cn.genn.pf.orch.meeting.infrastructure.exception.AuthException;
import cn.genn.pf.orch.meeting.infrastructure.exception.MessageCode;
import cn.hutool.core.util.ObjUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 线程上下文存储用户信息 SsoAuthFilter赋值
 *
 * <AUTHOR>
 */
@Slf4j
public class CurrentUserHolder {
    public static final ThreadLocal<UserInfoDTO> CURRENT_USER_THREAD_LOCAL = new ThreadLocal<>();

    public static UserInfoDTO getCurrentUserAndLog() {
        log.info("threadInfo :{}", JsonUtils.toJson(CURRENT_USER_THREAD_LOCAL.get()));
        return CURRENT_USER_THREAD_LOCAL.get();
    }

    public static UserInfoDTO getCurrentUser(){
        UserInfoDTO userInfoDTO = CURRENT_USER_THREAD_LOCAL.get();
        if(userInfoDTO == null){
            throw new AuthException(MessageCode.USER_INFO_ERROR);
        }
        return userInfoDTO;
    }

    public static String getToken() {
        String token = getCurrentUser().getToken();
        if (ObjUtil.isNull(token)) {
            throw new BusinessException(MessageCode.TOKEN_GET_ERROR);
        }
        return token;
    }

    public static String getOpenId(){
        String openId = getCurrentUser().getOpenId();
        if (ObjUtil.isNull(openId)) {
            throw new BusinessException(MessageCode.OPEN_ID_GET_ERROR);
        }
        return openId;
    }


}
