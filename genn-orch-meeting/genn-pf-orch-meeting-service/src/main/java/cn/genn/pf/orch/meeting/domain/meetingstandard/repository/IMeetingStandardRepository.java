package cn.genn.pf.orch.meeting.domain.meetingstandard.repository;

import cn.genn.pf.orch.meeting.domain.meetingstandard.model.entity.MeetingStandard;
import cn.genn.pf.orch.meeting.domain.meetingstandard.model.vo.AttendeeStatistics;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准仓储接口
 * @date 2025-01-24
 */
public interface IMeetingStandardRepository {

    /**
     * 保存会议标准
     */
    void save(MeetingStandard meetingStandard);

    /**
     * 更新会议标准
     */
    void update(MeetingStandard meetingStandard);

    /**
     * 根据ID查询会议标准
     */
    MeetingStandard findById(Long id);

    /**
     * 删除会议标准
     */
    void deleteById(Long id);

    /**
     * 检查名称是否存在
     */
    boolean existsByName(String standardName, Long excludeId);

    /**
     * 检查是否被会议规划使用
     */
    boolean isUsedByMeetingPlan(Long standardId);

    /**
     * 查询使用该标准的会议规划ID列表
     */
    List<Long> findMeetingPlanIdsByStandardId(Long standardId);

    /**
     * 获取会议标准的参会人数统计
     */
    AttendeeStatistics getAttendeeStatisticsByStandardId(Long standardId);

    /**
     * 查询所有启用的会议标准
     */
    List<MeetingStandard> findAllEnabled();
}
