package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.pf.orch.meeting.application.service.action.ToolActionService;
import cn.genn.pf.orch.meeting.interfaces.command.TeamNotifyUserCommand;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 运维工具
 */
@Api(tags = "运维工具")
@RequestMapping("/white/tool")
@RestController
public class ToolController {

    @Resource
    private ToolActionService toolActionService;

    @PostMapping("/addTeamNotifyUser")
    @ApiOperation(value = "添加班组通知人员")
    public void addTeamNotifyUser(@Validated @RequestBody TeamNotifyUserCommand command) {
        toolActionService.addTeamNotifyUser(command);
    }

}
