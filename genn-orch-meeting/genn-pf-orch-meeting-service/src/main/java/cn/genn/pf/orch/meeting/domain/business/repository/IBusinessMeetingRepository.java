package cn.genn.pf.orch.meeting.domain.business.repository;

import cn.genn.pf.orch.meeting.domain.business.model.entity.BusinessMeeting;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.BusinessMeetingPO;

import java.util.List;

/**
 * 业务会议仓储接口
 *
 * <AUTHOR>
 */
public interface IBusinessMeetingRepository {

    void save(BusinessMeeting businessMeeting);

    BusinessMeetingPO queryByBusinessMeetingName(String name);

    List<Long> queryParentIds(Long sonId);

    void insertSonLevel(Long parentId, Long id);
}
