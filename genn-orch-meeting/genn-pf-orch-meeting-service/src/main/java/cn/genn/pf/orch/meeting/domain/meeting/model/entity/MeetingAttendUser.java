package cn.genn.pf.orch.meeting.domain.meeting.model.entity;

import cn.genn.pf.orch.meeting.interfaces.enums.AbsentStatusEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendStatusEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.CheckInStatusEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 参会人
 * @date 2024-12-31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingAttendUser {

    /**
     * 主键
     */
    private Long id;

    /**
     * 会议id
     */
    private Long meetingId;

    /**
     * 飞书用户id
     */
    private String fsUserId;

    /**
     * 第一次入会时间
     */
    private LocalDateTime firstJoinTime;

    /**
     * 最终离会时间
     */
    private LocalDateTime finalLeaveTime;

    /**
     * 累计在会中时间，时间单位：秒
     */
    private Integer inMeetingDuration;

    /**
     * 签到状态 0-未签到, 1-已签到
     */
    private CheckInStatusEnum checkInStatus;

    /**
     * 签到地点
     */
    private String checkInAddress;

    /**
     * 签到时间
     */
    private LocalDateTime checkInTime;
}
