package cn.genn.pf.orch.meeting.application.assembler;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingAttendUserPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.AttendUserDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingAttendUserDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingDetailDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingListDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description 会议组装器
 * @date 2025-01-03
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingAssembler {

    @Mapping(
        target = "pageNo",
        source = "current"
    )
    @Mapping(
        target = "pageSize",
        source = "size"
    )
    @Mapping(
        target = "list",
        source = "records"
    )
    @Mapping(
        target = "totalPages",
        source = "pages"
    )
    PageResultDTO<MeetingListDTO> toMiniPageResult(Page<MeetingListDTO> cargoInfoDTOPage);

    MeetingDetailDTO toMeetingDetailDTO(MeetingPO meetingPO);

    MeetingAttendUserDTO toMeetingAttendUserDTO(AttendUserDTO attendUserCommand);

    void fillMeetingAttendUserDTO(MeetingAttendUserPO attendUserPO, @MappingTarget MeetingAttendUserDTO meetingAttendUserDTO);
}
