package cn.genn.pf.orch.meeting.application.dto.card;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendSignInRemindDTO {

    /**
     * 送达人员
     */
    private String openId;

    /**
     * 会议类型
     */
    private String meetingType;
    /**
     * 会议名称
     */
    private String meetingName;
    /**
     * 会议开始时间
     */
    private String signTime;
    /**
     * 签到链接
     */
    private String signUrl;
}
