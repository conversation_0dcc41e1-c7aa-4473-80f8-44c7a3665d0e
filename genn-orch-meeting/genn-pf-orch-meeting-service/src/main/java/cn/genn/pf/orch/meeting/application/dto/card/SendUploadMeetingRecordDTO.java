package cn.genn.pf.orch.meeting.application.dto.card;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendUploadMeetingRecordDTO {

    /**
     * 送达人员
     */
    private String openId;

    /**
     * 会议类型
     */
    private String meetingType;
    /**
     * 会议名称
     */
    private String meetingName;
    /**
     * 会议时间
     */
    private String meetingTime;
    /**
     * 上传链接
     */
    private String uploadUrl;
}
