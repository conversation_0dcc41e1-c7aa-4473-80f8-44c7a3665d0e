package cn.genn.pf.orch.meeting.application.handler;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.AttendUser;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.Schedule;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IScheduleRepository;
import cn.genn.pf.orch.meeting.domain.schedule.service.MeetingDomainService;
import cn.genn.pf.orch.meeting.domain.schedule.service.ScheduleDomainService;
import cn.genn.pf.orch.meeting.infrastructure.enums.EventCallbackEnum;
import cn.genn.pf.orch.meeting.interfaces.command.ScheduleChangeEventCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.AttendUserDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingStatusEnum;
import cn.genn.third.feishu.app.FeishuAppClient;
import cn.genn.third.feishu.app.model.CalendarEventModel;
import cn.genn.third.feishu.app.model.callback.EventCallbackCommand;
import com.lark.oapi.service.calendar.v4.model.CalendarEvent;
import com.lark.oapi.service.calendar.v4.model.CalendarEventAttendee;
import com.lark.oapi.service.contact.v3.model.GetUserRespBody;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 日程变更回调处理器
 * @date 2024-12-25
 */
@Component
@RequiredArgsConstructor
public class ScheduleChangeHandler implements CallbackHandler<ScheduleChangeEventCommand>{

    private final ScheduleQueryService queryService;
    private final IScheduleRepository scheduleRepository;
    private final ScheduleDomainService scheduleDomainService;
    private final MeetingDomainService meetingDomainService;

    private final FeishuAppClient feishuAppClient;

    @Override
    public void handle(EventCallbackCommand command) {
//        String eventJson = JsonUtils.toJson(command.getEvent());
//        ScheduleChangeEventCommand scheduleChange = JsonUtils.parse(eventJson, ScheduleChangeEventCommand.class);
//
//        if (scheduleChange.getUserIdList().length == 0) {
//            return;
//        }
//
//        ScheduleDTO scheduleDTO = queryService.getByFSCalendarEventId(scheduleChange.getCalendarEventId());
//        // 非应用数据
//        if (scheduleDTO == null) {
//            return;
//        }
//
//        switch (scheduleChange.getChangeType()) {
//            case "create":
//                handleCreate(scheduleChange, scheduleDTO);
//                break;
//
//            case "rsvp":
//                break;
//
//            case "update":
//                handleUpdate(scheduleChange, scheduleDTO);
//                break;
//
//            case "delete":
//                handleDelete(scheduleChange, scheduleDTO);
//                break;
//        }
    }

    private void handleCreate(ScheduleChangeEventCommand scheduleChange, ScheduleDTO scheduleDTO) {
        Optional<AttendUserDTO> user = scheduleDTO.getAttendUsers().stream()
            .filter(attendUserDTO -> attendUserDTO.getAttendUserId().equals(scheduleChange.getUserIdList()[0].getOpenId()))
            .findFirst();
        // 改人员已在日程中
        if (user.isPresent()) {
            return;
        }

        GetUserRespBody userInfo = feishuAppClient.getContactService().getUserInfo(scheduleChange.getUserIdList()[0].getOpenId());
        if (userInfo == null || userInfo.getUser() == null) {
            return;
        }
        // 将该人员信息入库
        List<AttendUser> attendUsers = Collections.singletonList(AttendUser.builder()
            .attendUserState(AttendUserStateEnum.EXPECT)
            .attendUserRole(AttendUserRoleEnum.NORMAL)
            .attendUserId(scheduleChange.getUserIdList()[0].getOpenId())
            .attendUserName(userInfo.getUser().getName())
            .build());
        scheduleDomainService.addAttendUsers(scheduleDTO.getId(), attendUsers);
    }

    private void handleUpdate(ScheduleChangeEventCommand scheduleChange, ScheduleDTO scheduleDTO) {
        // 组织者更新
        if (scheduleChange.getUserIdList()[0].getOpenId().equals(scheduleDTO.getCreateUserId())) {
            // 获取飞书日程
            CalendarEvent event = feishuAppClient.getCalendarEvent(CalendarEventModel.builder()
                .calendarId(scheduleChange.getCalendarId())
                .eventId(scheduleChange.getCalendarEventId())
                .build()
            ).getEvent();

            // 更新日程
            scheduleRepository.update(Schedule.builder()
                .id(scheduleDTO.getId())
                .scheduleName(event.getSummary())
                .startTime(LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(event.getStartTime().getTimestamp())),
                    ZoneId.of(event.getStartTime().getTimezone())))
                .endTime(LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(event.getEndTime().getTimestamp())),
                    ZoneId.of(event.getEndTime().getTimezone())))
                .build()
            );

            // 当前飞书 参与人
            Set<String> attendeeIds = Arrays.stream(event.getAttendees())
                .map(CalendarEventAttendee::getUserId)
                .collect(Collectors.toSet());
            // 数据库中保存的 参与人
            Set<String> databaseUserIds = scheduleDTO.getAttendUsers().stream()
                .map(AttendUserDTO::getAttendUserId)
                .collect(Collectors.toSet());

            // 数据库 Set - 当前飞书 Set -> toDelete
            HashSet<String> removeUserIds = new HashSet<>(databaseUserIds);
            removeUserIds.removeAll(attendeeIds);
            scheduleDomainService.removeAttendUsers(scheduleDTO.getId(), new ArrayList<>(removeUserIds));

            // 当前飞书 Set - 数据库 Set -> toAdd
            HashSet<String> addUserIds = new HashSet<>(attendeeIds);
            addUserIds.removeAll(databaseUserIds);
            List<CalendarEventAttendee> addAttendees = Arrays.stream(event.getAttendees())
                .filter(attendee -> addUserIds.contains(attendee.getUserId()))
                .collect(Collectors.toList());
            scheduleDomainService.addAttendUsers(scheduleDTO.getId(), addAttendees.stream()
                .map(attendee -> AttendUser.builder()
                    .attendUserId(attendee.getUserId())
                    .attendUserName(attendee.getDisplayName())
                    .attendUserState(AttendUserStateEnum.EXPECT)
                    .attendUserRole(AttendUserRoleEnum.NORMAL)
                    .build()
                )
                .collect(Collectors.toList())
            );
        }
    }

    private void handleDelete(ScheduleChangeEventCommand scheduleChange, ScheduleDTO scheduleDTO) {
        // 组织者删除日程
        if (scheduleChange.getUserIdList()[0].getOpenId().equals(scheduleDTO.getCreateUserId())) {
            MeetingAgg meetingAgg = meetingDomainService.findByScheduleId(scheduleDTO.getId());
            // 会议进行中、已结束 不允许删除
            if (meetingAgg != null && meetingAgg.getInfo().getStatus().getCode() > MeetingStatusEnum.WAIT_START.getCode()) {
                return;
            }

            scheduleDomainService.delete(scheduleDTO.getId());
            if (meetingAgg != null) {
                meetingDomainService.delete(meetingAgg);
            }
            return;
        }

        // 参会人退出日程
        scheduleDomainService.removeAttendUsers(scheduleDTO.getId(),
            Collections.singletonList(scheduleChange.getUserIdList()[0].getOpenId()));
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.SCHEDULE_CHANGE;
    }
}
