package cn.genn.pf.orch.meeting.domain.newmeeting.service;

import cn.genn.pf.orch.meeting.domain.newmeeting.model.entity.NewMeeting;
import cn.genn.pf.orch.meeting.domain.newmeeting.repository.INewMeetingRepository;
import cn.genn.pf.orch.meeting.interfaces.enums.NewMeetingStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议领域服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NewMeetingDomainService {

    private final INewMeetingRepository newMeetingRepository;

    /**
     * 创建会议
     */
    public void createMeeting(NewMeeting meeting) {
        // 设置初始状态
        meeting.setStatus(NewMeetingStatusEnum.NOT_STARTED);
        
        // 保存会议
        newMeetingRepository.save(meeting);
    }

    /**
     * 更新会议
     */
    public void updateMeeting(NewMeeting meeting) {
        newMeetingRepository.update(meeting);
    }

    /**
     * 删除会议
     */
    public void deleteMeeting(Long id) {
        newMeetingRepository.deleteById(id);
    }

    /**
     * 根据ID查找会议
     */
    public NewMeeting findById(Long id) {
        return newMeetingRepository.findById(id);
    }

    /**
     * 根据飞书日程事件ID查找会议
     */
    public NewMeeting findByFsCalendarEventId(String fsCalendarEventId) {
        return newMeetingRepository.findByFsCalendarEventId(fsCalendarEventId);
    }

    /**
     * 根据飞书会议ID查找会议
     */
    public NewMeeting findByFsMeetingId(String fsMeetingId) {
        return newMeetingRepository.findByFsMeetingId(fsMeetingId);
    }

    /**
     * 更新会议状态
     */
    public void updateMeetingStatus(Long id, NewMeetingStatusEnum status) {
        newMeetingRepository.updateStatus(id, status);
    }

    /**
     * 更新飞书相关信息
     */
    public void updateFeishuInfo(Long id, String fsCalendarEventId, String fsMeetingId, String meetingUrl) {
        newMeetingRepository.updateFeishuInfo(id, fsCalendarEventId, fsMeetingId, meetingUrl);
    }

    /**
     * 更新会议编号和飞书会议ID
     */
    public void updateMeetingNoAndFsMeetingId(Long id, String meetingNo, String fsMeetingId) {
        newMeetingRepository.updateMeetingNoAndFsMeetingId(id, meetingNo, fsMeetingId);
    }

    /**
     * 更新会议编号、飞书会议ID和会议链接
     */
    public void updateMeetingInfo(Long id, String meetingNo, String fsMeetingId, String meetingUrl) {
        newMeetingRepository.updateMeetingInfo(id, meetingNo, fsMeetingId, meetingUrl);
    }

    /**
     * 更新妙计链接
     */
    public void updateMinuteUrl(Long id, String minuteUrl) {
        newMeetingRepository.updateMinuteUrl(id, minuteUrl);
    }

    /**
     * 分页查询会议
     */
    public List<NewMeeting> findPage(String meetingName, Integer status, Integer priorityLevel, 
                                   Long meetingPlanId, Long meetingStandardId, String startTimeFrom, 
                                   String startTimeTo, String createUserId, Integer pageNum, Integer pageSize) {
        return newMeetingRepository.findPage(meetingName, status, priorityLevel, meetingPlanId, 
                                           meetingStandardId, startTimeFrom, startTimeTo, createUserId, 
                                           pageNum, pageSize);
    }

    /**
     * 统计会议数量
     */
    public Long count(String meetingName, Integer status, Integer priorityLevel, 
                     Long meetingPlanId, Long meetingStandardId, String startTimeFrom, 
                     String startTimeTo, String createUserId) {
        return newMeetingRepository.count(meetingName, status, priorityLevel, meetingPlanId, 
                                        meetingStandardId, startTimeFrom, startTimeTo, createUserId);
    }
} 