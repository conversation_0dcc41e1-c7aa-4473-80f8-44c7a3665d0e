package cn.genn.pf.orch.meeting.domain.meeting.service.impl;

import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingEvaluationAgg;
import cn.genn.pf.orch.meeting.domain.meeting.repository.IMeetingEvaluationRepository;
import cn.genn.pf.orch.meeting.domain.meeting.service.MeetingEvaluationDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议评价领域服务实现
 * @date 2025-01-24
 */
@Slf4j
@Service
public class MeetingEvaluationDomainServiceImpl implements MeetingEvaluationDomainService {
    
    @Resource
    private IMeetingEvaluationRepository meetingEvaluationRepository;
    
    @Override
    public void saveEvaluation(MeetingEvaluationAgg evaluationAgg) {
        meetingEvaluationRepository.save(evaluationAgg);
    }
    
    @Override
    public MeetingEvaluationAgg findByMeetingIdAndEvaluator(Long meetingId, String evaluatorOpenId) {
        return meetingEvaluationRepository.findByMeetingIdAndEvaluator(meetingId, evaluatorOpenId);
    }
} 