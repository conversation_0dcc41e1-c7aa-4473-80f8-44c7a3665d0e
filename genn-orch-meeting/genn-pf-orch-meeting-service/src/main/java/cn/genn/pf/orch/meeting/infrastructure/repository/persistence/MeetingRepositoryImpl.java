package cn.genn.pf.orch.meeting.infrastructure.repository.persistence;

import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingAttendUser;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingExtend;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingInfo;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IMeetingRepository;
import cn.genn.pf.orch.meeting.infrastructure.converter.MeetingConverter;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingAttendUserMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingExtendMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingAttendUserPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingExtendPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPO;
import cn.genn.pf.orch.meeting.interfaces.enums.EvaluateReminderEnum;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议仓储层实现
 * @date 2025-01-02
 */
@Repository
public class MeetingRepositoryImpl implements IMeetingRepository {

    @Resource
    private MeetingMapper meetingMapper;
    @Resource
    private MeetingExtendMapper meetingExtendMapper;
    @Resource
    private MeetingAttendUserMapper meetingAttendUserMapper;
    @Resource
    private MeetingConverter meetingConverter;

    @Override
    public void insert(MeetingAgg meetingAgg) {
        MeetingPO meetingPO = meetingConverter.toMeetingPO(meetingAgg.getInfo());
        meetingMapper.insert(meetingPO);

        MeetingExtendPO meetingExtendPO = meetingConverter.toMeetingExtendPO(meetingAgg.getExtend());
        meetingExtendPO.setMeetingId(meetingPO.getId());
        meetingExtendMapper.insert(meetingExtendPO);
    }

    @Override
    public void start(MeetingAgg meetingAgg) {
        MeetingPO meetingPO = meetingConverter.toMeetingPO(meetingAgg.getInfo());
        meetingMapper.update(Wrappers.lambdaUpdate(MeetingPO.class)
            .eq(MeetingPO::getId, meetingPO.getId())
            .set(meetingPO.getMeetingNo() != null, MeetingPO::getMeetingNo, meetingPO.getMeetingNo())
            .set(meetingPO.getFsMeetingId() != null, MeetingPO::getFsMeetingId, meetingPO.getFsMeetingId())
            .set(meetingPO.getStartTime() != null, MeetingPO::getStartTime, meetingPO.getStartTime())
            .set(meetingPO.getStatus() != null, MeetingPO::getStatus, meetingPO.getStatus())
            .set(MeetingPO::getEndTime, null));
    }

    @Override
    public void end(MeetingAgg meetingAgg) {
        MeetingPO meetingPO = meetingConverter.toMeetingPO(meetingAgg.getInfo());
        meetingMapper.updateById(meetingPO);

        List<MeetingAttendUserPO> meetingAttendUserPOList = meetingConverter.toMeetingAttendUserPOList(meetingAgg.getAttendUsers());
        for (MeetingAttendUserPO userPO : meetingAttendUserPOList) {
            if (userPO.getId() == null) {
                meetingAttendUserMapper.insert(userPO);
            } else {
                meetingAttendUserMapper.updateById(userPO);
            }
        }

    }

    @Override
    public void uploadRecord(MeetingAgg meetingAgg) {
        meetingExtendMapper.updateById(meetingConverter.toMeetingExtendPO(meetingAgg.getExtend()));
    }

    @Override
    public void uploadEvaluate(MeetingAgg meetingAgg) {
        meetingExtendMapper.updateById(meetingConverter.toMeetingExtendPO(meetingAgg.getExtend()));
    }

    @Override
    public void delete(MeetingAgg meetingAgg) {
        meetingMapper.deleteById(meetingAgg.getInfo().getId());
        meetingExtendMapper.delete(Wrappers.lambdaQuery(MeetingExtendPO.class).eq(MeetingExtendPO::getMeetingId, meetingAgg.getInfo().getId()));
    }

    @Override
    public MeetingAgg findByFsMeetingId(String fsMeetingId) {
        MeetingPO meetingPO = meetingMapper.selectOne(Wrappers.lambdaQuery(MeetingPO.class).eq(MeetingPO::getFsMeetingId, fsMeetingId));
        return buildMeetingAgg(meetingPO);
    }

    @Override
    public void recordReady(MeetingAgg meetingAgg) {
        meetingMapper.updateById(meetingConverter.toMeetingPO(meetingAgg.getInfo()));
    }

    @Override
    public MeetingAgg findById(Long id) {
        MeetingPO meetingPO = meetingMapper.selectById(id);
        return buildMeetingAgg(meetingPO);
    }

    @Override
    public List<MeetingAgg> findEvaluateReminders() {
        List<MeetingExtendPO> meetingExtendPOS = meetingExtendMapper.selectList(Wrappers.lambdaQuery(MeetingExtendPO.class)
            .eq(MeetingExtendPO::getEvaluateReminder, EvaluateReminderEnum.NOT_REMINDED)
            .isNotNull(MeetingExtendPO::getRecordTime));

        return meetingExtendPOS.stream()
            .map(this::buildMeetingAgg)
            .collect(Collectors.toList());
    }

    @Override
    public MeetingAgg findByScheduleId(Long scheduleId) {
        MeetingPO meetingPO = meetingMapper.selectOne(Wrappers.lambdaQuery(MeetingPO.class).eq(MeetingPO::getScheduleId, scheduleId));
        return buildMeetingAgg(meetingPO);
    }

    private MeetingAgg buildMeetingAgg(MeetingPO meetingPO) {
        if (Objects.isNull(meetingPO)) {
            return null;
        }
        MeetingInfo meetingInfo = meetingConverter.toMeetingInfo(meetingPO);
        MeetingExtendPO meetingExtendPO = meetingExtendMapper.selectOne(Wrappers.lambdaQuery(MeetingExtendPO.class).eq(MeetingExtendPO::getMeetingId, meetingPO.getId()));
        MeetingExtend meetingExtend = meetingConverter.toMeetingExtend(meetingExtendPO);
        List<MeetingAttendUserPO> attendUserPOList = meetingAttendUserMapper.selectList(Wrappers.lambdaQuery(MeetingAttendUserPO.class).eq(MeetingAttendUserPO::getMeetingId, meetingPO.getId()));
        List<MeetingAttendUser> meetingAttendUserList = meetingConverter.toMeetingAttendUserList(attendUserPOList);
        return MeetingAgg.builder().info(meetingInfo).extend(meetingExtend).attendUsers(meetingAttendUserList).build();
    }

    private MeetingAgg buildMeetingAgg(MeetingExtendPO meetingExtendPO) {
        if (Objects.isNull(meetingExtendPO)) {
            return null;
        }
        MeetingPO meetingPO = meetingMapper.selectById(meetingExtendPO.getMeetingId());
        MeetingInfo meetingInfo = meetingConverter.toMeetingInfo(meetingPO);
        MeetingExtend meetingExtend = meetingConverter.toMeetingExtend(meetingExtendPO);
        List<MeetingAttendUserPO> attendUserPOList = meetingAttendUserMapper.selectList(Wrappers.lambdaQuery(MeetingAttendUserPO.class).eq(MeetingAttendUserPO::getMeetingId, meetingPO.getId()));
        List<MeetingAttendUser> meetingAttendUserList = meetingConverter.toMeetingAttendUserList(attendUserPOList);
        return MeetingAgg.builder().info(meetingInfo).extend(meetingExtend).attendUsers(meetingAttendUserList).build();
    }

    @Override
    public void signIn(MeetingAttendUser meetingAttendUser) {
        //添加或编辑
        MeetingAttendUserPO meetingAttendUserPO = meetingConverter.toMeetingAttendUserPO(meetingAttendUser);
        MeetingAttendUserPO po = meetingAttendUserMapper.selectByMeetingIdAndUserId(meetingAttendUser.getMeetingId(),meetingAttendUser.getFsUserId());
        if(ObjUtil.isNotNull(po)){
            po.setCheckInStatus(meetingAttendUser.getCheckInStatus());
            po.setCheckInAddress(meetingAttendUser.getCheckInAddress());
            po.setCheckInTime(meetingAttendUser.getCheckInTime());
            meetingAttendUserMapper.updateById(po);
        } else {
            meetingAttendUserMapper.insert(meetingAttendUserPO);
        }
    }

    @Override
    public void evaluateReminder(Long meetingId) {
        meetingExtendMapper.update(Wrappers.lambdaUpdate(MeetingExtendPO.class)
            .eq(MeetingExtendPO::getMeetingId, meetingId)
            .set(MeetingExtendPO::getEvaluateReminder, EvaluateReminderEnum.REMINDED));
    }

    @Override
    public void updateExtend(MeetingAgg meetingAgg) {
        meetingExtendMapper.updateById(meetingConverter.toMeetingExtendPO(meetingAgg.getExtend()));
    }

    @Override
    public void updateInfo(MeetingAgg meetingAgg) {
        meetingMapper.updateById(meetingConverter.toMeetingPO(meetingAgg.getInfo()));
    }

    @Override
    public List<Long> findNotUploadedMeetingIds() {
        return meetingExtendMapper.selectList(Wrappers.lambdaQuery(MeetingExtendPO.class)
            .isNull(MeetingExtendPO::getRecordTime))
            .stream()
            .map(MeetingExtendPO::getMeetingId)
            .collect(Collectors.toList());
    }

    @Override
    public void saveRecordDraft(MeetingAgg meetingAgg) {
        meetingExtendMapper.updateById(meetingConverter.toMeetingExtendPO(meetingAgg.getExtend()));
    }

    @Override
    public void saveEvaluateDraft(MeetingAgg meetingAgg) {
        meetingExtendMapper.updateById(meetingConverter.toMeetingExtendPO(meetingAgg.getExtend()));
    }

    @Override
    public void deleteRecordDraft(Long meetingId) {
        meetingExtendMapper.update(Wrappers.lambdaUpdate(MeetingExtendPO.class)
            .set(MeetingExtendPO::getRecordDraft, null)
            .set(MeetingExtendPO::getDataCatalogDraft, null)
            .set(MeetingExtendPO::getAchievementCatalogDraft, null)
            .eq(MeetingExtendPO::getMeetingId, meetingId));
    }

    @Override
    public void deleteEvaluateDraft(Long meetingId) {
        meetingExtendMapper.update(Wrappers.lambdaUpdate(MeetingExtendPO.class)
            .set(MeetingExtendPO::getEvaluateDraft, null)
            .eq(MeetingExtendPO::getMeetingId, meetingId));
    }
}
