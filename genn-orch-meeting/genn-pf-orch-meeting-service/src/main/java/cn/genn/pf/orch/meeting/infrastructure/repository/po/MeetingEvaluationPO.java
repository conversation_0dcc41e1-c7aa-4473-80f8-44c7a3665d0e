package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议评价PO
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName(value = "meeting_evaluation", autoResultMap = true)
public class MeetingEvaluationPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 会议id
     */
    @TableField("meeting_id")
    private Long meetingId;

    /**
     * 评价人OpenID
     */
    @TableField("evaluator_open_id")
    private String evaluatorOpenId;

    /**
     * 评价人姓名
     */
    @TableField("evaluator_name")
    private String evaluatorName;

    /**
     * 会议评分(1-5)
     */
    @TableField("meeting_score")
    private Integer meetingScore;

    /**
     * 内容评分(1-5)
     */
    @TableField("content_score")
    private Integer contentScore;

    /**
     * 时长评分(1-5)
     */
    @TableField("duration_score")
    private Integer durationScore;

    /**
     * 效果评分(1-5)
     */
    @TableField("effectiveness_score")
    private Integer effectivenessScore;

    /**
     * 改进建议
     */
    @TableField("suggestions")
    private String suggestions;

    /**
     * 评价时间
     */
    @TableField("evaluation_time")
    private LocalDateTime evaluationTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Boolean deleted;
} 