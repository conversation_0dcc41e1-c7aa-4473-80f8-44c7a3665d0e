package cn.genn.pf.orch.meeting.infrastructure.repository.persistence;

import cn.genn.database.mybatisplus.plugin.tenant.IgnoreTenant;
import cn.genn.pf.orch.meeting.domain.business.model.entity.BusinessMeeting;
import cn.genn.pf.orch.meeting.domain.business.repository.IBusinessMeetingRepository;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.BusinessMeetingLevelMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.BusinessMeetingMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.BusinessMeetingLevelPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.BusinessMeetingPO;
import cn.genn.pf.orch.meeting.interfaces.enums.TopBusinessEnum;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务会议仓储实现
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class BusinessMeetingRepositoryImpl implements IBusinessMeetingRepository {

    private final BusinessMeetingMapper businessMeetingMapper;
    private final BusinessMeetingLevelMapper businessMeetingLevelMapper;

    @Override
    public void save(BusinessMeeting businessMeeting) {
        BusinessMeetingPO businessMeetingPO = new BusinessMeetingPO();
        businessMeetingPO.setName(businessMeeting.getName());

        if (ObjectUtil.isNull(businessMeeting.getParentId())) {
            businessMeetingPO.setTopBusiness(TopBusinessEnum.TOP_BUSINESS);
            businessMeetingMapper.insert(businessMeetingPO);
            return;
        }

        businessMeetingPO.setTopBusiness(TopBusinessEnum.NOT_TOP_BUSINESS);
        businessMeetingMapper.insertAndGetId(businessMeetingPO);

        BusinessMeetingLevelPO businessMeetingLevelPO = new BusinessMeetingLevelPO();
        businessMeetingLevelPO.setSonId(businessMeetingPO.getId());
        businessMeetingLevelPO.setParentId(businessMeeting.getParentId());
        businessMeetingLevelMapper.insert(businessMeetingLevelPO);
    }

    @Override
    @IgnoreTenant
    public BusinessMeetingPO queryByBusinessMeetingName(String name) {
        return businessMeetingMapper.selectOne(Wrappers.lambdaQuery(BusinessMeetingPO.class)
            .eq(BusinessMeetingPO::getName, name));
    }

    @Override
    @IgnoreTenant
    public List<Long> queryParentIds(Long sonId) {
        return businessMeetingLevelMapper.selectList(Wrappers.lambdaQuery(BusinessMeetingLevelPO.class)
            .eq(BusinessMeetingLevelPO::getSonId, sonId))
            .stream()
            .map(BusinessMeetingLevelPO::getParentId)
            .collect(Collectors.toList());
    }

    @Override
    public void insertSonLevel(Long parentId, Long id) {
        BusinessMeetingLevelPO businessMeetingLevelPO = new BusinessMeetingLevelPO();
        businessMeetingLevelPO.setParentId(parentId);
        businessMeetingLevelPO.setSonId(id);
        businessMeetingLevelMapper.insert(businessMeetingLevelPO);
    }
}
