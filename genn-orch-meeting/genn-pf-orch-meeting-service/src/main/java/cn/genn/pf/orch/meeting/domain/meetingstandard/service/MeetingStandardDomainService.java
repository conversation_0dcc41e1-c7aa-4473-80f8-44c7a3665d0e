package cn.genn.pf.orch.meeting.domain.meetingstandard.service;

import cn.genn.core.exception.BusinessException;
import cn.genn.pf.orch.meeting.domain.meetingstandard.model.entity.MeetingStandard;
import cn.genn.pf.orch.meeting.domain.meetingstandard.model.vo.AttendeeStatistics;
import cn.genn.pf.orch.meeting.domain.meetingstandard.repository.IMeetingStandardRepository;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.PriorityLevelEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准领域服务
 * @date 2025-01-24
 */
@Service
@RequiredArgsConstructor
public class MeetingStandardDomainService {

    private final IMeetingStandardRepository meetingStandardRepository;

    /**
     * 创建会议标准
     */
    public void createMeetingStandard(MeetingStandard meetingStandard) {
        // 1. 基础验证
        validateMeetingStandard(meetingStandard);
        
        // 2. 检查名称唯一性
        if (meetingStandardRepository.existsByName(meetingStandard.getStandardName(), null)) {
            throw new BusinessException("标准名称已存在");
        }
        
        // 3. 保存
        meetingStandardRepository.save(meetingStandard);
    }

    /**
     * 更新会议标准
     */
    public void updateMeetingStandard(MeetingStandard meetingStandard) {
        // 1. 检查是否存在
        MeetingStandard existing = meetingStandardRepository.findById(meetingStandard.getId());
        if (existing == null) {
            throw new BusinessException("会议标准不存在");
        }
        
        // 2. 基础验证
        validateMeetingStandard(meetingStandard);
        
        // 3. 检查名称唯一性（排除自己）
        if (meetingStandardRepository.existsByName(meetingStandard.getStandardName(), meetingStandard.getId())) {
            throw new BusinessException("标准名称已存在");
        }
        
        // 4. 更新
        meetingStandardRepository.update(meetingStandard);
    }

    /**
     * 删除会议标准
     */
    public void deleteMeetingStandard(Long id) {
        // 1. 检查是否存在
        MeetingStandard existing = meetingStandardRepository.findById(id);
        if (existing == null) {
            throw new BusinessException("会议标准不存在");
        }
        
        // 2. 检查是否被使用
        if (meetingStandardRepository.isUsedByMeetingPlan(id)) {
            throw new BusinessException("该会议标准已被会议规划使用，无法删除");
        }
        
        // 3. 删除
        meetingStandardRepository.deleteById(id);
    }

    /**
     * 验证会议标准基础数据
     */
    private void validateMeetingStandard(MeetingStandard meetingStandard) {
        // 1. 标准名称验证
        if (meetingStandard.getStandardName() == null || meetingStandard.getStandardName().trim().isEmpty()) {
            throw new BusinessException("标准名称不能为空");
        }
        
        // 2. 持续时长验证（只在有值的情况下验证）
        if (meetingStandard.getDefaultDuration() != null && meetingStandard.getDefaultDuration() <= 0) {
            throw new BusinessException("默认持续时长必须大于0");
        }
        
        // 3. 提前通知时间验证
        if (meetingStandard.getAdvanceNoticeValue() != null && meetingStandard.getAdvanceNoticeValue() < 0) {
            throw new BusinessException("提前通知时间不能为负数");
        }
        
        // 4. 迟到容许时间验证
        if (meetingStandard.getLateToleranceMinutes() != null && meetingStandard.getLateToleranceMinutes() < 0) {
            throw new BusinessException("迟到容许时间不能为负数");
        }
        
        // 5. 参会人数验证（只在有值的情况下验证）
        if (meetingStandard.getMinAttendees() != null && meetingStandard.getMinAttendees() <= 0) {
            throw new BusinessException("最少参会人数必须大于0");
        }
        
        if (meetingStandard.getMaxAttendees() != null && meetingStandard.getMaxAttendees() <= 0) {
            throw new BusinessException("最多参会人数必须大于0");
        }
        
        if (meetingStandard.getMinAttendees() != null && meetingStandard.getMaxAttendees() != null) {
            if (meetingStandard.getMinAttendees() > meetingStandard.getMaxAttendees()) {
                throw new BusinessException("最少参会人数不能大于最多参会人数");
            }
        }
        
        // 6. 必要角色验证
        if (meetingStandard.getRequiredRoles() != null && !meetingStandard.getRequiredRoles().isEmpty()) {
            for (MeetingRoleEnum role : meetingStandard.getRequiredRoles()) {
                if (role == null) {
                    throw new BusinessException("必要角色不能为空");
                }
            }
        }
    }

    /**
     * 获取会议标准的参会人数统计
     */
    public AttendeeStatistics getAttendeeStatistics(Long standardId) {
        MeetingStandard standard = meetingStandardRepository.findById(standardId);
        if (standard == null) {
            throw new BusinessException("会议标准不存在");
        }
        
        return meetingStandardRepository.getAttendeeStatisticsByStandardId(standardId);
    }

    /**
     * 获取会议标准的建议参会人数范围
     */
    public AttendeeSuggestion getAttendeeSuggestion(Long standardId) {
        AttendeeStatistics statistics = getAttendeeStatistics(standardId);
        MeetingStandard standard = meetingStandardRepository.findById(standardId);
        
        if (standard == null) {
            throw new BusinessException("会议标准不存在");
        }
        
        return AttendeeSuggestion.builder()
            .standardId(standardId)
            .standardName(standard.getStandardName())
            .currentMinAttendees(standard.getMinAttendees())
            .currentMaxAttendees(standard.getMaxAttendees())
            .statistics(statistics)
            .suggestedMinAttendees(statistics.getMinAttendees() > 0 ? statistics.getMinAttendees() : 1)
            .suggestedMaxAttendees(statistics.getMaxAttendees() > 0 ? statistics.getMaxAttendees() : 50)
            .build();
    }

    /**
     * 参会人数建议
     */
    public static class AttendeeSuggestion {
        private Long standardId;
        private String standardName;
        private Integer currentMinAttendees;
        private Integer currentMaxAttendees;
        private AttendeeStatistics statistics;
        private Integer suggestedMinAttendees;
        private Integer suggestedMaxAttendees;
        
        // 使用Builder模式
        public static AttendeeSuggestionBuilder builder() {
            return new AttendeeSuggestionBuilder();
        }
        
        public static class AttendeeSuggestionBuilder {
            private Long standardId;
            private String standardName;
            private Integer currentMinAttendees;
            private Integer currentMaxAttendees;
            private AttendeeStatistics statistics;
            private Integer suggestedMinAttendees;
            private Integer suggestedMaxAttendees;
            
            public AttendeeSuggestionBuilder standardId(Long standardId) {
                this.standardId = standardId;
                return this;
            }
            
            public AttendeeSuggestionBuilder standardName(String standardName) {
                this.standardName = standardName;
                return this;
            }
            
            public AttendeeSuggestionBuilder currentMinAttendees(Integer currentMinAttendees) {
                this.currentMinAttendees = currentMinAttendees;
                return this;
            }
            
            public AttendeeSuggestionBuilder currentMaxAttendees(Integer currentMaxAttendees) {
                this.currentMaxAttendees = currentMaxAttendees;
                return this;
            }
            
            public AttendeeSuggestionBuilder statistics(AttendeeStatistics statistics) {
                this.statistics = statistics;
                return this;
            }
            
            public AttendeeSuggestionBuilder suggestedMinAttendees(Integer suggestedMinAttendees) {
                this.suggestedMinAttendees = suggestedMinAttendees;
                return this;
            }
            
            public AttendeeSuggestionBuilder suggestedMaxAttendees(Integer suggestedMaxAttendees) {
                this.suggestedMaxAttendees = suggestedMaxAttendees;
                return this;
            }
            
            public AttendeeSuggestion build() {
                AttendeeSuggestion suggestion = new AttendeeSuggestion();
                suggestion.standardId = this.standardId;
                suggestion.standardName = this.standardName;
                suggestion.currentMinAttendees = this.currentMinAttendees;
                suggestion.currentMaxAttendees = this.currentMaxAttendees;
                suggestion.statistics = this.statistics;
                suggestion.suggestedMinAttendees = this.suggestedMinAttendees;
                suggestion.suggestedMaxAttendees = this.suggestedMaxAttendees;
                return suggestion;
            }
        }
        
        // Getters
        public Long getStandardId() { return standardId; }
        public String getStandardName() { return standardName; }
        public Integer getCurrentMinAttendees() { return currentMinAttendees; }
        public Integer getCurrentMaxAttendees() { return currentMaxAttendees; }
        public AttendeeStatistics getStatistics() { return statistics; }
        public Integer getSuggestedMinAttendees() { return suggestedMinAttendees; }
        public Integer getSuggestedMaxAttendees() { return suggestedMaxAttendees; }
    }
} 