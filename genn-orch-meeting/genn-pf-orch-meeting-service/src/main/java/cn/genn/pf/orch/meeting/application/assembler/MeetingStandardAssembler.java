package cn.genn.pf.orch.meeting.application.assembler;

import cn.genn.pf.orch.meeting.domain.meetingstandard.model.entity.MeetingStandard;
import cn.genn.pf.orch.meeting.interfaces.command.meetingstandard.MeetingStandardCreateCommand;
import cn.genn.pf.orch.meeting.interfaces.command.meetingstandard.MeetingStandardUpdateCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingstandard.MeetingStandardDTO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准组装器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingStandardAssembler {

    MeetingStandardDTO toDTO(MeetingStandard meetingStandard);

    List<MeetingStandardDTO> toDTOList(List<MeetingStandard> meetingStandards);

    MeetingStandard toEntity(MeetingStandardCreateCommand command);

    MeetingStandard toEntity(MeetingStandardUpdateCommand command);
}
