package cn.genn.pf.orch.meeting.application.handler;

import cn.genn.pf.orch.meeting.infrastructure.enums.EventCallbackEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 回调工厂
 * @date 2024-12-25
 */
@Component
public class CallbackHandlerFactory {

    @Resource
    private Map<EventCallbackEnum, CallbackHandler> callbackHandlerMap;

    public <T> CallbackHandler<T> getCallbackHandler(EventCallbackEnum event) {
        return callbackHandlerMap.get(event);
    }
}
