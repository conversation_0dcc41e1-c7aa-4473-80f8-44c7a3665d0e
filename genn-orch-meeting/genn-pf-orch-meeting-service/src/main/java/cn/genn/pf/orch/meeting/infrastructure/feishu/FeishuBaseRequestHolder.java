package cn.genn.pf.orch.meeting.infrastructure.feishu;

import cn.genn.spring.boot.starter.third.feishu.properties.FeishuProperties;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 飞书基础请求holder
 * @date 2025-06-03
 */
@Component
public class FeishuBaseRequestHolder {

    @Resource
    private FeishuProperties feishuProperties;

    private FeishuBaseRequest feishuBaseRequest;

    @PostConstruct
    public void init() {
        if (CollUtil.isNotEmpty(feishuProperties.getMini().getConfig())) {
            this.feishuBaseRequest = FeishuBaseRequest.builder()
                .appId(feishuProperties.getMini().getConfig().get(0).getAppId())
                .appSecret(feishuProperties.getMini().getConfig().get(0).getAppSecret())
                .build();
        }
    }

    public FeishuBaseRequest get() {
        return feishuBaseRequest;
    }
}
