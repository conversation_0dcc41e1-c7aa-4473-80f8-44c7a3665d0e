package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.core.model.ddd.IdQuery;
import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.application.service.action.TaskActionService;
import cn.genn.pf.orch.meeting.application.service.action.TaskSupervisionService;
import cn.genn.pf.orch.meeting.application.service.query.TaskQueryService;
import cn.genn.pf.orch.meeting.interfaces.command.TaskCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskListDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskStatisticsDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskSupervisionDTO;
import cn.genn.pf.orch.meeting.interfaces.query.TaskQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务控制器
 */
@Api(tags = "任务管理")
@RestController
@RequestMapping("/task")
public class TaskController {

    @Resource
    private TaskQueryService taskQueryService;
    @Resource
    private TaskActionService taskActionService;
    @Resource
    private TaskSupervisionService taskSupervisionService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询任务列表")
    public PageResultDTO<TaskListDTO> page(@RequestBody TaskQuery taskQuery) {
        return taskQueryService.page(taskQuery);
    }

    @PostMapping("/detail")
    @ApiOperation(value = "查询任务详情")
    public TaskDTO detail(@RequestBody IdQuery idQuery) {
        return taskQueryService.detail(idQuery);
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建任务")
    public Long create(@Validated @RequestBody TaskCommand command) {
        return taskActionService.create(command);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新任务")
    public void update(@Validated @RequestBody TaskCommand command) {
        taskActionService.update(command);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除任务")
    public void delete(@RequestBody IdQuery idQuery) {
        taskActionService.delete(idQuery);
    }

    @PostMapping("/complete")
    @ApiOperation(value = "完成任务")
    public void complete(@RequestBody IdQuery idQuery) {
        taskActionService.complete(idQuery);
    }

    @PostMapping("/start")
    @ApiOperation(value = "开始任务")
    public void start(@RequestBody IdQuery idQuery) {
        taskActionService.start(idQuery);
    }

    @GetMapping("/listByMeetingId")
    @ApiOperation(value = "根据会议ID查询任务列表")
    public List<TaskListDTO> listByMeetingId(@RequestParam Long meetingId) {
        return taskQueryService.listByMeetingId(meetingId);
    }

    @GetMapping("/listByOwner")
    @ApiOperation(value = "根据负责人查询任务列表")
    public List<TaskListDTO> listByOwner(@RequestParam String ownerOpenId) {
        return taskQueryService.listByOwner(ownerOpenId);
    }

    @PostMapping("/myTasks")
    @ApiOperation(value = "查询我的任务列表")
    public PageResultDTO<TaskListDTO> myTasks(@RequestBody TaskQuery taskQuery) {
        return taskQueryService.myTasks(taskQuery);
    }

    @PostMapping("/processOverdue")
    @ApiOperation(value = "处理超期任务")
    public void processOverdue() {
        taskActionService.processOverdueTasks();
    }

    @GetMapping("/statistics")
    @ApiOperation(value = "获取任务统计信息", notes = "获取任务数据统计面板信息，包括总任务数、各状态任务数、完成率等")
    public TaskStatisticsDTO getTaskStatistics() {
        return taskQueryService.getTaskStatistics();
    }

    @GetMapping("/supervision/list")
    @ApiOperation(value = "获取督办任务列表", notes = "获取距离截止时间3小时内需要督办的任务列表")
    public List<TaskSupervisionDTO> getTasksForSupervision() {
        return taskQueryService.getTasksForSupervision();
    }

    @PostMapping("/supervision/manual/{taskId}")
    @ApiOperation(value = "手动督办任务", notes = "手动触发指定任务的督办消息发送")
    public void manualSuperviseTask(@PathVariable("taskId") Long taskId) {
        taskSupervisionService.manualSuperviseTask(taskId);
    }

    @PostMapping("/supervision/auto")
    @ApiOperation(value = "自动督办紧急任务", notes = "自动督办所有距离截止时间3小时内的任务（定时任务调用）")
    public void autoSuperviseUrgentTasks() {
        taskSupervisionService.autoSuperviseUrgentTasks();
    }

    @GetMapping("/supervision/{taskId}")
    @ApiOperation(value = "获取任务督办信息", notes = "根据任务ID获取督办相关信息")
    public TaskSupervisionDTO getTaskSupervisionById(@PathVariable("taskId") Long taskId) {
        return taskQueryService.getTaskSupervisionById(taskId);
    }

    @PostMapping("/supervision/test")
    @ApiOperation(value = "测试督办功能", notes = "发送模拟督办卡片消息，用于测试飞书卡片效果")
    public void testSupervision(@RequestParam("openId") String openId) {
        taskSupervisionService.testSupervision(openId);
    }
}
