package cn.genn.pf.orch.meeting.domain.meeting.repository;

import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingEvaluationAgg;

/**
 * <AUTHOR>
 * @description 会议评价仓储接口
 * @date 2025-01-24
 */
public interface IMeetingEvaluationRepository {
    
    /**
     * 保存评价
     */
    void save(MeetingEvaluationAgg evaluationAgg);
    
    /**
     * 根据会议ID和评价人查询评价
     */
    MeetingEvaluationAgg findByMeetingIdAndEvaluator(Long meetingId, String evaluatorOpenId);
} 