package cn.genn.pf.orch.meeting.application.assembler;

import cn.genn.pf.orch.meeting.infrastructure.repository.po.FileDetailPO;
import cn.genn.pf.orch.meeting.interfaces.dto.FileInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FileAssembler {


    List<FileInfoDTO> PO2DTO(List<FileDetailPO> pos);

    @Mapping(source = "id", target = "fileKey")
    FileInfoDTO PO2DTO(FileDetailPO pos);
}
