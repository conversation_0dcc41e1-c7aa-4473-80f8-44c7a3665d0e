package cn.genn.pf.orch.meeting.domain.business.service;

import cn.genn.pf.orch.meeting.domain.business.model.entity.BusinessMeeting;
import cn.genn.pf.orch.meeting.domain.business.repository.IBusinessMeetingRepository;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.BusinessMeetingPO;
import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务会议领域服务
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class BusinessMeetingDomainService {

    private final IBusinessMeetingRepository businessMeetingRepository;

    public void addBusinessMeeting(BusinessMeeting businessMeeting) {
        BusinessMeetingPO businessMeetingPO = businessMeetingRepository.queryByBusinessMeetingName(businessMeeting.getName());
        if (ObjectUtil.isNull(businessMeetingPO)) {
            businessMeetingRepository.save(businessMeeting);
            return;
        }

        List<Long> parentIds = businessMeetingRepository.queryParentIds(businessMeetingPO.getId());
        if (parentIds.contains(businessMeeting.getParentId())) {
            return;
        }

        businessMeetingRepository.insertSonLevel(businessMeeting.getParentId(), businessMeetingPO.getId());
    }
}
