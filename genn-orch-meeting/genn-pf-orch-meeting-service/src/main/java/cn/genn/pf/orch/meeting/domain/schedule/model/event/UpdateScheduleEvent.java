package cn.genn.pf.orch.meeting.domain.schedule.model.event;

import cn.genn.pf.orch.meeting.domain.schedule.model.entity.Schedule;
import cn.genn.spring.boot.starter.event.spring.model.SpringBaseEvent;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 更新日程事件
 */
public class UpdateScheduleEvent extends SpringBaseEvent {

    public UpdateScheduleEvent(UpdateScheduleData data) {
        super(data);
    }

    @Data
    @Builder
    public static class UpdateScheduleData {

        Schedule schedule;
    }
}
