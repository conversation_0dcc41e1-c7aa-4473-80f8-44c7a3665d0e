package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import cn.genn.database.mybatisplus.typehandler.JacksonTypeHandler;
import cn.genn.pf.orch.meeting.application.handler.ListMeetingAttachmentTypeHandler;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingAttachmentDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleConfigDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.EvaluateReminderEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;


/**
 * MeetingExtendPO对象
 *
 * <AUTHOR>
 * @desc 会议扩展信息表
 */
@Data
@Accessors(chain = true)
@TableName(value = "meeting_extend", autoResultMap = true)
public class MeetingExtendPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 会议id
     */
    @TableField("meeting_id")
    private Long meetingId;

    /**
     * 会议记录
     */
    @TableField("record")
    private String record;

    /**
     * 会议记录草稿
     */
    @TableField(value = "record_draft", updateStrategy = FieldStrategy.ALWAYS)
    private String recordDraft;

    /**
     * 会前会议资料
     */
    @TableField(value = "data_catalog",typeHandler = ListMeetingAttachmentTypeHandler.class)
    private List<MeetingAttachmentDTO> dataCatalog;

    /**
     * 会前会议资料草稿
     */
    @TableField(value = "data_catalog_draft",typeHandler = ListMeetingAttachmentTypeHandler.class)
    private List<MeetingAttachmentDTO> dataCatalogDraft;

    /**
     * 成果目录资料
     */
    @TableField(value = "achievement_catalog",typeHandler = ListMeetingAttachmentTypeHandler.class)
    private List<MeetingAttachmentDTO> achievementCatalog;

    /**
     * 成果目录资料草稿
     */
    @TableField(value = "achievement_catalog_draft",typeHandler = ListMeetingAttachmentTypeHandler.class)
    private List<MeetingAttachmentDTO> achievementCatalogDraft;

    /**
     * 记录时间
     */
    @TableField("record_time")
    private LocalDateTime recordTime;

    /**
     * 评价
     */
    @TableField("evaluate")
    private String evaluate;

    /**
     * 评价草稿
     */
    @TableField("evaluate_draft")
    private String evaluateDraft;

    /**
     * 评价提醒 0-未提醒, 1-已提醒
     */
    @TableField("evaluate_reminder")
    private EvaluateReminderEnum evaluateReminder;

    /**
     * 班主快照
     */
    @TableField(value = "team_info", typeHandler = JacksonTypeHandler.class)
    private TeamInfoDTO teamInfo;

    /**
     * 日程配置快照
     */
    @TableField(value = "schedule_config", typeHandler = JacksonTypeHandler.class)
    private ScheduleConfigDTO scheduleConfig;

}

