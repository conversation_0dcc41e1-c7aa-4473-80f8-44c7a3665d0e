package cn.genn.pf.orch.meeting.application.handler;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.application.service.action.MeetingEvaluationActionService;
import cn.genn.pf.orch.meeting.application.service.action.NewMeetingActionService;
import cn.genn.pf.orch.meeting.application.service.query.NewMeetingQueryService;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.domain.schedule.service.MeetingDomainService;
import cn.genn.pf.orch.meeting.infrastructure.enums.EventCallbackEnum;
import cn.genn.pf.orch.meeting.interfaces.command.MeetingStartEndEventCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.newmeeting.NewMeetingDTO;
import cn.genn.third.feishu.app.FeishuAppClient;
import cn.genn.third.feishu.app.model.callback.EventCallbackCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 会议结束事件处理器
 * @date 2025-01-02
 */
@Slf4j
@Component
public class MeetingEndHandler implements CallbackHandler<MeetingStartEndEventCommand> {

    @Resource
    private NewMeetingQueryService newMeetingQueryService;
    @Resource
    private MeetingDomainService meetingDomainService;
    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private ScheduleQueryService scheduleQueryService;
    @Resource
    private NewMeetingActionService newMeetingActionService;
    @Resource
    private MeetingEvaluationActionService evaluationActionService;

    @Override
    public void handle(EventCallbackCommand command) {
        String meetingJson = JsonUtils.toJson(command.getEvent());
        MeetingStartEndEventCommand fsCommand = JsonUtils.parse(meetingJson, MeetingStartEndEventCommand.class);
        MeetingStartEndEventCommand.Meeting endCommand = fsCommand.getMeeting();
        // 新会议同步
        newMeetingActionService.handleFeishuMeetingEndCallback(
            endCommand.getCalendarEventId(),
            endCommand.getEndTime()
        );

        // 会议结束后发送评价卡片
        try {
            // 根据calendarEventId获取新会议信息
            NewMeetingDTO newMeeting = newMeetingQueryService.findByFsCalendarEventId(endCommand.getCalendarEventId());
            if (newMeeting != null) {
                log.info("找到会议，会议ID：{}，会议名称：{}", newMeeting.getId(), newMeeting.getMeetingName());
                // 发送新会议系统的评价卡片
                evaluationActionService.sendNewMeetingEvaluationCards(newMeeting.getId());
                log.info("新会议系统评价卡片发送成功，会议ID：{}", newMeeting.getId());
            } else {
                log.warn("未找到对应的会议，calendarEventId：{}", endCommand.getCalendarEventId());
            }
        } catch (Exception e) {
            log.error("发送会议评价卡片失败，calendarEventId：{}", endCommand.getCalendarEventId(), e);
        }
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.MEETING_END;
    }
}
