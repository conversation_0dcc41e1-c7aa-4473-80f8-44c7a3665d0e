package cn.genn.pf.orch.meeting.domain.meeting.model.entity;

import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleConfigDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingAttachmentDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.EvaluateReminderEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议扩展信息
 * @date 2024-12-31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingExtend {

    /**
     * 主键
     */
    private Long id;

    /**
     * 会议id
     */
    private Long meetingId;

    /**
     * 会议记录
     */
    private String record;

    /**
     * 会议记录草稿
     */
    private String recordDraft;

    /**
     * 会前会议资料
     */
    private List<MeetingAttachmentDTO> dataCatalog;

    /**
     * 会前会议资料草稿
     */
    private List<MeetingAttachmentDTO> dataCatalogDraft;

    /**
     * 成果目录资料
     */
    private List<MeetingAttachmentDTO> achievementCatalog;

    /**
     * 成果目录资料草稿
     */
    private List<MeetingAttachmentDTO> achievementCatalogDraft;

    /**
     * 记录时间
     */
    private LocalDateTime recordTime;

    /**
     * 评价
     */
    private String evaluate;

    /**
     * 评价草稿
     */
    private String evaluateDraft;

    /**
     * 评价提醒 0-未提醒, 1-已提醒
     */
    private EvaluateReminderEnum evaluateReminder;

    /**
     * 班主快照
     */
    private TeamInfoDTO teamInfo;

    /**
     * 日程配置快照
     */
    private ScheduleConfigDTO scheduleConfig;
}
