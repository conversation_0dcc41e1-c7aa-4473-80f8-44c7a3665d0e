package cn.genn.pf.orch.meeting.infrastructure.feishu.req;

import com.lark.oapi.service.task.v2.model.Member;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 创建任务请求
 * @date 2025-06-03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateTaskRequest {

    /**
     * 任务标题
     */
    private String summary;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务结束时间
     */
    private LocalDateTime dueTime;

    /**
     * 扩展信息
     */
    private String extra;

    /**
     * 任务成员列表
     */
    private List<Member> members;
}
