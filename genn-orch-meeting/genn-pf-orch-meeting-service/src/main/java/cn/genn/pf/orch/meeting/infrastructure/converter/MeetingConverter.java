package cn.genn.pf.orch.meeting.infrastructure.converter;

import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingAttendUser;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingExtend;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingInfo;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingAttendUserPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingExtendPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议转换器
 * @date 2025-01-02
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingConverter {

    MeetingPO toMeetingPO(MeetingInfo meeting);

    MeetingExtendPO toMeetingExtendPO(MeetingExtend meetingExtend);

    MeetingInfo toMeetingInfo(MeetingPO meetingPO);

    MeetingExtend toMeetingExtend(MeetingExtendPO meetingExtendPO);

    List<MeetingAttendUserPO> toMeetingAttendUserPOList(List<MeetingAttendUser> attendUsers);

    List<MeetingAttendUser> toMeetingAttendUserList(List<MeetingAttendUserPO> attendUserPOS);

    MeetingAttendUserPO toMeetingAttendUserPO(MeetingAttendUser attendUser);
}
