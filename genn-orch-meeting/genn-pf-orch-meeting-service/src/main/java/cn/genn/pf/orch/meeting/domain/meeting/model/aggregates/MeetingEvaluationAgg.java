package cn.genn.pf.orch.meeting.domain.meeting.model.aggregates;

import cn.genn.pf.orch.meeting.application.dto.MeetingEvaluationDTO;
import cn.genn.pf.orch.meeting.domain.meeting.model.entity.MeetingEvaluationInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议评价聚合根
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingEvaluationAgg {
    
    private MeetingEvaluationInfo info;
    
    /**
     * 提交评价
     */
    public void submitEvaluation(String evaluatorOpenId, String evaluatorName, 
                                MeetingEvaluationDTO evaluationDTO) {
        // 更新评价信息
        info.setEvaluatorOpenId(evaluatorOpenId);
        info.setEvaluatorName(evaluatorName);
        info.setMeetingScore(evaluationDTO.getMeetingScore());
        info.setContentScore(evaluationDTO.getContentScore());
        info.setDurationScore(evaluationDTO.getDurationScore());
        info.setEffectivenessScore(evaluationDTO.getEffectivenessScore());
        info.setSuggestions(evaluationDTO.getSuggestions());
        info.setEvaluationTime(LocalDateTime.now());
    }
} 