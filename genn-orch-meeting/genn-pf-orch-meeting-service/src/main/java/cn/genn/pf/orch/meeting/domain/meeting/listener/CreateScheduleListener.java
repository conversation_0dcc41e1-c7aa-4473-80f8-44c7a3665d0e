package cn.genn.pf.orch.meeting.domain.meeting.listener;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.application.dto.MeetingCreateDTO;
import cn.genn.pf.orch.meeting.application.service.query.MeetingQueryService;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.application.service.query.TeamQueryService;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.schedule.model.event.CreateScheduleEvent;
import cn.genn.pf.orch.meeting.domain.schedule.service.MeetingDomainService;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventSyncListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class CreateScheduleListener extends SpringEventSyncListener<CreateScheduleEvent> {

    @Resource
    private MeetingDomainService meetingDomainService;
    @Resource
    private MeetingQueryService meetingQueryService;
    @Resource
    private ScheduleQueryService scheduleQueryService;
    @Resource
    private TeamQueryService teamQueryService;

    @Override
    protected void onMessage(CreateScheduleEvent event) {

        CreateScheduleEvent.CreateScheduleData createScheduleData = (CreateScheduleEvent.CreateScheduleData)event.getSource();

        log.info("receive create schedule event context: {}", JsonUtils.toJson(createScheduleData));

        List<String> checkInNumbers = meetingQueryService.queryValidCheckInNumbers();

        ScheduleDTO scheduleDTO = scheduleQueryService.getByScheduleId(createScheduleData.getSchedule().getId());
        MeetingCreateDTO meetingCreateDTO = MeetingCreateDTO.builder()
            .existCheckInNumbers(checkInNumbers)
            .schedule(scheduleDTO)
            .build();
        MeetingAgg meetingAgg = MeetingAgg.create(meetingCreateDTO);
        meetingDomainService.create(meetingAgg);
    }
}
