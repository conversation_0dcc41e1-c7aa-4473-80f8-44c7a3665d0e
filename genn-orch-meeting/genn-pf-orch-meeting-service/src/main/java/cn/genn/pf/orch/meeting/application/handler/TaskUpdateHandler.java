package cn.genn.pf.orch.meeting.application.handler;

import cn.genn.pf.orch.meeting.application.handler.task.TaskNotificationService;
import cn.genn.pf.orch.meeting.infrastructure.enums.EventCallbackEnum;
import cn.genn.pf.orch.meeting.infrastructure.feishu.FeishuEventParser;
import cn.genn.third.feishu.app.model.callback.EventCallbackCommand;
import com.lark.oapi.service.task.v1.model.P2TaskUpdatedV1Data;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @description 任务更新处理器
 * @date 2025-06-06
 */
@Component
@RequiredArgsConstructor
public class TaskUpdateHandler implements CallbackHandler {

    private final TaskNotificationService notificationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handle(EventCallbackCommand command) {
        P2TaskUpdatedV1Data event = FeishuEventParser.parseEvent(
            command.getEvent(),
            P2TaskUpdatedV1Data.class
        );
        notificationService.processNotification(event);
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.TASK_UPDATE;
    }
}
