package cn.genn.pf.orch.meeting.infrastructure.schedule;

import cn.genn.pf.orch.meeting.application.service.action.TaskSupervisionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Assistant
 * @description 任务督办定时任务
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "app.schedule.task-supervision.enabled", havingValue = "true", matchIfMissing = true)
public class TaskSupervisionSchedule {

    @Resource
    private TaskSupervisionService taskSupervisionService;

    /**
     * 自动督办紧急任务
     * 每小时执行一次，检查距离截止时间3小时内的任务
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void autoSuperviseUrgentTasks() {
        log.info("开始执行任务督办定时任务");
        
        try {
            taskSupervisionService.autoSuperviseUrgentTasks();
            log.info("任务督办定时任务执行完成");
        } catch (Exception e) {
            log.error("任务督办定时任务执行失败", e);
        }
    }

    /**
     * 高频督办检查（可选）
     * 每30分钟执行一次，用于更及时的督办
     */
    @Scheduled(cron = "0 */30 * * * ?")
    @ConditionalOnProperty(name = "app.schedule.task-supervision.high-frequency", havingValue = "true")
    public void highFrequencySupervision() {
        log.info("开始执行高频任务督办检查");
        
        try {
            taskSupervisionService.autoSuperviseUrgentTasks();
            log.info("高频任务督办检查执行完成");
        } catch (Exception e) {
            log.error("高频任务督办检查执行失败", e);
        }
    }
}
