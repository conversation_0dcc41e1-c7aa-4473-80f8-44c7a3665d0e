package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.application.service.action.MeetingPlanActionService;
import cn.genn.pf.orch.meeting.application.service.query.MeetingPlanQueryService;
import cn.genn.pf.orch.meeting.interfaces.command.meetingplan.MeetingPlanCreateCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.MeetingPlanListDTO;
import cn.genn.pf.orch.meeting.interfaces.command.meetingplan.MeetingPlanUpdateCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.MeetingPlanDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.MeetingPlanCalendarDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.ConflictInfo;
import cn.genn.pf.orch.meeting.interfaces.command.meetingplan.MeetingPlanConflictCheckCommand;
import cn.genn.pf.orch.meeting.interfaces.query.meetingplan.MeetingPlanQuery;
import cn.genn.pf.orch.meeting.interfaces.query.meetingplan.MeetingPlanCalendarQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划控制器
 * @date 2025-01-24
 */
@Api(tags = "会议规划")
@RestController
@RequestMapping("/meeting-plan")
@RequiredArgsConstructor
public class MeetingPlanController {

    private final MeetingPlanActionService meetingPlanActionService;
    private final MeetingPlanQueryService meetingPlanQueryService;

    @PostMapping("/create")
    @ApiOperation("创建会议规划")
    public void create(@Validated @RequestBody MeetingPlanCreateCommand command) {
        meetingPlanActionService.create(command);
    }

    @PostMapping("/update")
    @ApiOperation("更新会议规划")
    public void update(@Validated @RequestBody MeetingPlanUpdateCommand command) {
        meetingPlanActionService.update(command);
    }

    @PostMapping("/delete")
    @ApiOperation("删除会议规划")
    public void delete(@RequestParam("id") Long id) {
        meetingPlanActionService.delete(id);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询会议规划")
    public PageResultDTO<MeetingPlanListDTO> page(@RequestBody MeetingPlanQuery query) {
        return meetingPlanQueryService.queryPage(query);
    }

    @GetMapping("/detail")
    @ApiOperation("查询会议规划详情")
    public MeetingPlanDTO detail(@RequestParam("id") Long id) {
        return meetingPlanQueryService.getById(id);
    }

    @PostMapping("/calendar")
    @ApiOperation("查询日历维度的会议规划")
    public List<MeetingPlanCalendarDTO> calendar(@Validated @RequestBody MeetingPlanCalendarQuery query) {
        return meetingPlanQueryService.queryCalendar(query);
    }

    @PostMapping("/check-conflicts")
    @ApiOperation("检查会议规划冲突")
    public List<ConflictInfo> checkConflicts(@Validated @RequestBody MeetingPlanConflictCheckCommand command) {
        return meetingPlanQueryService.checkConflicts(
            command.getPlannedStartTime(),
            command.getPlannedEndTime(),
            command.getMeetingLocation(),
            command.getAttendees(),
            command.getExcludeId()
        );
    }
}
