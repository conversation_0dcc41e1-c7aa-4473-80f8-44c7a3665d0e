package cn.genn.pf.orch.meeting.infrastructure.utils;

import cn.genn.core.utils.thread.ContextAwareThreadPoolExecutor;
import cn.genn.core.utils.thread.decorator.GennRequestContextDecorator;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 自定义线程池,将不同任务归类
 * <AUTHOR>
 */
public class AsyncTaskExecutor {

    public final static ExecutorService execute;

    static {
        execute = new ContextAwareThreadPoolExecutor(
            4,
            8,
            60 * 1000L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(Integer.MAX_VALUE),
            new GennRequestContextDecorator());
    }

    /**
     * @param task
     */
    public static ExecutorService getExecutorService() {
        return execute;
    }


}
