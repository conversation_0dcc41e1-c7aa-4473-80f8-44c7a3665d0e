package cn.genn.pf.orch.meeting.application.handler.task;

import cn.genn.pf.orch.meeting.application.service.action.TaskSyncService;
import cn.genn.pf.orch.meeting.infrastructure.enums.TaskNotificationTypeEnum;
import com.lark.oapi.service.task.v1.model.P2TaskUpdatedV1Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 任务删除
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class TaskDeletedChangedHandler implements TaskNotificationHandler {

    private final TaskSyncService taskSyncService;

    @Override
    public void handle(P2TaskUpdatedV1Data notification) {
        log.info("处理任务删除事件，飞书任务ID：{}", notification.getTaskId());
        try {
            taskSyncService.deleteByFeishuTaskId(notification.getTaskId());
            log.info("任务删除处理成功，飞书任务ID：{}", notification.getTaskId());
        } catch (Exception e) {
            log.error("处理任务删除事件失败，飞书任务ID：{}", notification.getTaskId(), e);
        }
    }

    @Override
    public TaskNotificationTypeEnum getObjType() {
        return TaskNotificationTypeEnum.TASK_DELETED;
    }
}
