package cn.genn.pf.orch.meeting.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.application.assembler.MeetingPlanAssembler;
import cn.genn.pf.orch.meeting.domain.meetingplan.model.entity.MeetingPlan;
import cn.genn.pf.orch.meeting.domain.meetingplan.repository.IMeetingPlanRepository;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingPlanMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPlanPO;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.ConflictInfo;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.MeetingPlanCalendarDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.MeetingPlanDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.MeetingPlanListDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.user.FSUserInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.query.meetingplan.MeetingPlanCalendarQuery;
import cn.genn.pf.orch.meeting.interfaces.query.meetingplan.MeetingPlanQuery;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议规划查询服务
 * @date 2025-01-24
 */
@Service
@RequiredArgsConstructor
public class MeetingPlanQueryService {

    private final MeetingPlanMapper meetingPlanMapper;
    private final IMeetingPlanRepository meetingPlanRepository;
    private final MeetingPlanAssembler meetingPlanAssembler;
    private final UserInfoQueryService userInfoQueryService;

    /**
     * 分页查询会议规划列表
     */
    public PageResultDTO<MeetingPlanListDTO> queryPage(MeetingPlanQuery query) {
        Page<MeetingPlanPO> page = new Page<>(query.getPageNo(), query.getPageSize());
        Page<MeetingPlanListDTO> resultPage = meetingPlanMapper.queryPage(page, query);

        // 填充参会人员详细信息
        fillAttendeeDetails(resultPage.getRecords());

        return meetingPlanAssembler.toPageResult(resultPage);
    }

    /**
     * 根据ID查询会议规划详情
     */
    public MeetingPlanDTO getById(Long id) {
        MeetingPlan meetingPlan = meetingPlanRepository.findById(id);
        return meetingPlan != null ? meetingPlanAssembler.toDTO(meetingPlan) : null;
    }

    /**
     * 查询日历维度的会议规划
     */
    public List<MeetingPlanCalendarDTO> queryCalendar(MeetingPlanCalendarQuery query) {
        return meetingPlanMapper.queryCalendar(query);
    }

    /**
     * 检查会议规划冲突
     */
    public List<ConflictInfo> checkConflicts(LocalDateTime startTime, LocalDateTime endTime,
                                           String location, List<String> attendees, Long excludeId) {
        List<MeetingPlan> conflicts = meetingPlanRepository.findConflictPlans(
            startTime, endTime, location, attendees, excludeId);

        return conflicts.stream()
            .map(conflict -> buildConflictInfo(conflict, location, attendees))
            .collect(Collectors.toList());
    }

    /**
     * 构建冲突信息
     */
    private ConflictInfo buildConflictInfo(MeetingPlan conflict, String newLocation, List<String> newAttendees) {
        boolean locationConflict = hasLocationConflict(newLocation, conflict.getMeetingLocation());
        boolean attendeeConflict = hasAttendeeConflict(newAttendees, conflict.getAttendees());

        List<String> conflictAttendees = null;
        if (attendeeConflict && newAttendees != null && conflict.getAttendees() != null) {
            conflictAttendees = newAttendees.stream()
                .filter(attendee -> conflict.getAttendees().contains(attendee))
                .collect(Collectors.toList());
        }

        String message = buildConflictMessage(conflict, locationConflict, attendeeConflict);

        return ConflictInfo.builder()
            .conflictPlanId(conflict.getId())
            .conflictPlanName(conflict.getPlanName())
            .conflictStartTime(conflict.getPlannedStartTime())
            .conflictEndTime(conflict.getPlannedEndTime())
            .hasLocationConflict(locationConflict)
            .conflictLocation(locationConflict ? conflict.getMeetingLocation() : null)
            .hasAttendeeConflict(attendeeConflict)
            .conflictAttendees(conflictAttendees)
            .conflictMessage(message)
            .build();
    }

    private String buildConflictMessage(MeetingPlan conflict, boolean locationConflict, boolean attendeeConflict) {
        StringBuilder message = new StringBuilder(conflict.getPlanName());
        message.append("(").append(conflict.getPlannedStartTime()).append(")");

        if (locationConflict && attendeeConflict) {
            message.append(" [会议室冲突+人员冲突]");
        } else if (locationConflict) {
            message.append(" [会议室冲突: ").append(conflict.getMeetingLocation()).append("]");
        } else if (attendeeConflict) {
            message.append(" [人员冲突]");
        }

        return message.toString();
    }

    private boolean hasLocationConflict(String location1, String location2) {
        return location1 != null && location2 != null && location1.equals(location2);
    }

    private boolean hasAttendeeConflict(List<String> attendees1, List<String> attendees2) {
        if (attendees1 == null || attendees2 == null ||
            attendees1.isEmpty() || attendees2.isEmpty()) {
            return false;
        }
        return attendees1.stream().anyMatch(attendees2::contains);
    }

    /**
     * 填充参会人员详细信息
     */
    private void fillAttendeeDetails(List<MeetingPlanListDTO> meetingPlans) {
        if (CollUtil.isEmpty(meetingPlans)) {
            return;
        }

        // 收集所有参会人员ID
        List<String> allAttendeeIds = meetingPlans.stream()
                .filter(plan -> CollUtil.isNotEmpty(plan.getAttendees()))
                .flatMap(plan -> plan.getAttendees().stream())
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(allAttendeeIds)) {
            return;
        }

        // 批量获取用户信息
        Map<String, FSUserInfoDTO> userInfoMap = userInfoQueryService.getUserInfos(allAttendeeIds);

        // 为每个会议规划填充参会人员详细信息
        meetingPlans.forEach(plan -> {
            if (CollUtil.isNotEmpty(plan.getAttendees())) {
                List<FSUserInfoDTO> attendeeDetails = plan.getAttendees().stream()
                        .map(userInfoMap::get)
                        .filter(userInfo -> userInfo != null)
                        .collect(Collectors.toList());
                plan.setAttendeeDetails(attendeeDetails);
            }
        });
    }
}
