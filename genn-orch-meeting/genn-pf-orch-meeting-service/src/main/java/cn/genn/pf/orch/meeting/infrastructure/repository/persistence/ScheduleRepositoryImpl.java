package cn.genn.pf.orch.meeting.infrastructure.repository.persistence;

import cn.genn.pf.orch.meeting.application.assembler.ScheduleAssembler;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.AttendUser;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.Schedule;
import cn.genn.pf.orch.meeting.domain.schedule.model.entity.ScheduleConfig;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IScheduleRepository;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.ScheduleAttendUserMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.ScheduleConfigMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.ScheduleMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.ScheduleAttendUserPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.ScheduleConfigPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.SchedulePO;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * 日程仓储实现
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class ScheduleRepositoryImpl implements IScheduleRepository {

    private final ScheduleMapper scheduleMapper;
    private final ScheduleAttendUserMapper scheduleAttendUserMapper;
    private final ScheduleConfigMapper scheduleConfigMapper;

    private final ScheduleAssembler scheduleAssembler;

    @Override
    public void addConfig(ScheduleConfig scheduleConfig) {
        ScheduleConfigPO scheduleConfigPO = scheduleAssembler.toScheduleConfigPO(scheduleConfig);

        scheduleConfigMapper.insert(scheduleConfigPO);
    }

    @Override
    public void updateConfig(ScheduleConfig scheduleConfig) {
        ScheduleConfigPO scheduleConfigPO = scheduleAssembler.toScheduleConfigPO(scheduleConfig);

        scheduleConfigMapper.update(scheduleConfigPO, Wrappers.lambdaUpdate(ScheduleConfigPO.class)
            .eq(ScheduleConfigPO::getBusinessMeetingId, scheduleConfigPO.getBusinessMeetingId()));
    }

    @Override
    public ScheduleConfigPO queryConfigByBusinessMeetingId(Long businessMeetingId) {
        return scheduleConfigMapper.selectOne(Wrappers.<ScheduleConfigPO>lambdaQuery()
            .eq(ScheduleConfigPO::getBusinessMeetingId, businessMeetingId));
    }

    @Override
    public List<ScheduleConfigPO> queryConfigByBusinessMeetingIds(List<Long> businessMeetingIds) {
        return scheduleConfigMapper.selectList(Wrappers.<ScheduleConfigPO>lambdaQuery()
            .in(ScheduleConfigPO::getBusinessMeetingId, businessMeetingIds));
    }

    @Override
    public void add(Schedule schedule) {
        // 添加日程
        SchedulePO schedulePO = scheduleAssembler.toSchedulePO(schedule);
        scheduleMapper.insertAndGetId(schedulePO);
        schedule.setId(schedulePO.getId());

        // 添加参会人
        List<ScheduleAttendUserPO> attendUserPOS = scheduleAssembler.toAttendUserPOS(schedulePO.getId(), schedule.getAttendUsers());
        scheduleAttendUserMapper.batchInsertAttendUser(attendUserPOS);
    }

    @Override
    public SchedulePO queryScheduleById(Long scheduleId) {
        return scheduleMapper.selectById(scheduleId);
    }

    @Override
    public List<SchedulePO> queryScheduleByIds(List<Long> scheduleIds) {
        return scheduleMapper.selectBatchIds(scheduleIds);
    }

    @Override
    public SchedulePO queryScheduleByFSCalendarEventId(String fsCalendarEventId) {
        return scheduleMapper.selectOne(Wrappers.lambdaQuery(SchedulePO.class)
            .eq(SchedulePO::getFsCalendarEventId, fsCalendarEventId));
    }

    @Override
    public List<ScheduleAttendUserPO> queryUserByScheduleId(Long scheduleId) {
        return scheduleAttendUserMapper.selectList(Wrappers.lambdaQuery(ScheduleAttendUserPO.class)
            .eq(ScheduleAttendUserPO::getScheduleId, scheduleId));
    }

    @Override
    public List<ScheduleAttendUserPO> queryUserByScheduleIds(List<Long> scheduleIds) {
        return scheduleAttendUserMapper.selectList(Wrappers.lambdaQuery(ScheduleAttendUserPO.class)
            .in(ScheduleAttendUserPO::getScheduleId, scheduleIds));
    }

    @Override
    public void update(Schedule schedule) {
        SchedulePO schedulePO = scheduleAssembler.toSchedulePO(schedule);

        scheduleMapper.update(Wrappers.lambdaUpdate(SchedulePO.class)
            .eq(SchedulePO::getId, schedulePO.getId())
            .set(schedulePO.getTeamId() != null, SchedulePO::getTeamId, schedulePO.getTeamId())
            .set(schedulePO.getBusinessMeetingId() != null, SchedulePO::getBusinessMeetingId, schedulePO.getBusinessMeetingId())
            .set(schedulePO.getStartTime() != null, SchedulePO::getStartTime, schedulePO.getStartTime())
            .set(schedulePO.getEndTime() != null, SchedulePO::getEndTime, schedulePO.getEndTime())
            .set(schedulePO.getAllDay() != null, SchedulePO::getAllDay, schedulePO.getAllDay())
            .set(schedulePO.getFsMeetingUrl() != null, SchedulePO::getFsMeetingUrl, schedulePO.getFsMeetingUrl())
            .set(schedulePO.getScheduleName() != null, SchedulePO::getScheduleName, schedulePO.getScheduleName())
            .set(schedulePO.getCreateUserId() != null, SchedulePO::getCreateUserId, schedulePO.getCreateUserId())
            .set(schedulePO.getUpdateUserId() != null, SchedulePO::getUpdateUserId, schedulePO.getUpdateUserId())
        );
    }

    @Override
    public void addAttendUsers(Long scheduleId, List<AttendUser> addAttendUsers) {
        if (!addAttendUsers.isEmpty()) {
            List<ScheduleAttendUserPO> scheduleAttendUserPOS = scheduleAssembler.toScheduleAttendUserPOS(addAttendUsers);
            for (ScheduleAttendUserPO scheduleAttendUserPO : scheduleAttendUserPOS) {
                scheduleAttendUserPO.setScheduleId(scheduleId);
            }

            scheduleAttendUserMapper.batchInsertAttendUser(scheduleAttendUserPOS);
        }
    }

    @Override
    public void removeAttendUsers(Long scheduleId, List<String> removeAttendUserIds) {
        if (!removeAttendUserIds.isEmpty()) {
            scheduleAttendUserMapper.delete(Wrappers.lambdaQuery(ScheduleAttendUserPO.class)
                .eq(ScheduleAttendUserPO::getScheduleId, scheduleId)
                .in(ScheduleAttendUserPO::getAttendUserId, removeAttendUserIds));
        }
    }

    @Override
    public void updateAttendUsers(Long scheduleId, List<AttendUser> attendUsers) {
        if (!attendUsers.isEmpty()) {
            List<ScheduleAttendUserPO> scheduleAttendUserPOS = scheduleAssembler.toScheduleAttendUserPOS(attendUsers);

            scheduleAttendUserMapper.batchUpdateAttendUser(scheduleId, scheduleAttendUserPOS);
        }
    }

    @Override
    public void delete(Long scheduleId) {
        scheduleMapper.deleteById(scheduleId);

        scheduleAttendUserMapper.delete(Wrappers.lambdaQuery(ScheduleAttendUserPO.class)
            .eq(ScheduleAttendUserPO::getScheduleId, scheduleId));
    }
}
