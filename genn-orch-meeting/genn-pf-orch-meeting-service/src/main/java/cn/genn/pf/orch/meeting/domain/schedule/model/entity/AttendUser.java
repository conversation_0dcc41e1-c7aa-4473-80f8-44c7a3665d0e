package cn.genn.pf.orch.meeting.domain.schedule.model.entity;

import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * Schedule
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendUser {

    private String attendUserId;
    private String attendUserName;

    private AttendUserStateEnum attendUserState;
    private String absentReason;

    private AttendUserRoleEnum attendUserRole;
}
