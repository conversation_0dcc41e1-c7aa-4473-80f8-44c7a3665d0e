package cn.genn.pf.orch.meeting.infrastructure.converter;

import cn.genn.pf.orch.meeting.domain.newmeeting.model.entity.NewMeeting;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.NewMeetingPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议转换器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring")
public interface NewMeetingConverter {

    /**
     * PO转Entity
     */
    NewMeeting toEntity(NewMeetingPO po);

    /**
     * Entity转PO
     */
    NewMeetingPO toPO(NewMeeting entity);

    /**
     * PO列表转Entity列表
     */
    List<NewMeeting> toEntityList(List<NewMeetingPO> poList);
} 