package cn.genn.pf.orch.meeting.infrastructure.feishu;

import com.lark.oapi.Client;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class FeishuClientManager {

    private static final ConcurrentMap<String, Client> CLIENT_CACHE = new ConcurrentHashMap<>();

    private FeishuClientManager() {}

    public static Client getClient(String appId, String appSecret) {
        String key = appId + ":" + appSecret;
        return CLIENT_CACHE.computeIfAbsent(key, k -> Client.newBuilder(appId, appSecret).build());
    }
}
