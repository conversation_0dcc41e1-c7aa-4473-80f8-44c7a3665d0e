package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.application.service.query.TaskTrackingQueryService;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskListDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.task.TaskTrackingStatisticsDTO;
import cn.genn.pf.orch.meeting.interfaces.query.TaskListQuery;
import cn.genn.pf.orch.meeting.interfaces.query.TaskStatisticsQuery;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 任务追踪控制器
 * @date 2025-01-28
 */
@Api(tags = "任务追踪")
@RestController
@RequestMapping("/task-tracking")
@RequiredArgsConstructor
public class TaskTrackingController {

    private final TaskTrackingQueryService taskTrackingQueryService;

    @PostMapping("/list")
    @ApiOperation("获取任务列表")
    public PageResultDTO<TaskListDTO> getTaskList(@Validated @RequestBody TaskListQuery query) {
        return taskTrackingQueryService.getTaskList(query);
    }

    @PostMapping("/statistics")
    @ApiOperation("获取任务统计数据")
    public TaskTrackingStatisticsDTO getTaskStatistics(@Validated @RequestBody TaskStatisticsQuery query) {
        return taskTrackingQueryService.getTaskStatistics(query);
    }
}
