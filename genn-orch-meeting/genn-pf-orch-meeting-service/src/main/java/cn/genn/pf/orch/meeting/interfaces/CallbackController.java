package cn.genn.pf.orch.meeting.interfaces;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.application.service.action.CallbackActionService;
import cn.genn.third.feishu.app.model.callback.EventCallBackDTO;
import cn.genn.third.feishu.app.model.callback.EventCallbackCommand;
import cn.genn.web.spring.annotation.ResponseResultWrapper;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 回调控制器
 * @date 2024-12-25
 */
@Slf4j
@RestController
@RequestMapping("/white/event")
public class CallbackController {

    @Resource
    private CallbackActionService callbackActionService;

    @PostMapping("/callback")
    @ResponseResultWrapper(ignore = true)
    public <T> EventCallBackDTO callback(@RequestBody EventCallbackCommand<T> command){
        if (StrUtil.isNotBlank(command.getChallenge())){
            return EventCallBackDTO.builder().challenge(command.getChallenge()).build();
        }
        return callbackActionService.callback(command);
    }

    @PostMapping("/callback/config")
    @ResponseResultWrapper(ignore = true)
    public <T> EventCallBackDTO callbackConfig(@RequestBody EventCallbackCommand<T> command){
        log.info("command:{}", JsonUtils.toJson(command));
        if (StrUtil.isNotBlank(command.getChallenge())){
            return EventCallBackDTO.builder().challenge(command.getChallenge()).build();
        }
        return EventCallBackDTO.builder().build();
    }

}
