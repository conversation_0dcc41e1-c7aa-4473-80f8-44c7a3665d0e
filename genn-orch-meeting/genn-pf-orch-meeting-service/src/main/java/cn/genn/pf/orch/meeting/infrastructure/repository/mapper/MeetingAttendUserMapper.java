package cn.genn.pf.orch.meeting.infrastructure.repository.mapper;

import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingAttendUserPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

/**
 * <AUTHOR>
 */
public interface MeetingAttendUserMapper extends BaseMapper<MeetingAttendUserPO> {

    default MeetingAttendUserPO selectByMeetingIdAndUserId(Long meetingId, String openId) {
        LambdaQueryWrapper<MeetingAttendUserPO> queryWrapper = Wrappers.lambdaQuery(MeetingAttendUserPO.class)
            .eq(MeetingAttendUserPO::getMeetingId, meetingId)
            .eq(MeetingAttendUserPO::getFsUserId, openId)
            .last("limit 1");
        return selectOne(queryWrapper);
    }
}
