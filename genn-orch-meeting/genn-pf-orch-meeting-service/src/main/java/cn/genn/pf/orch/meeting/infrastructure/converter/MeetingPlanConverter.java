package cn.genn.pf.orch.meeting.infrastructure.converter;

import cn.genn.pf.orch.meeting.domain.meetingplan.model.entity.MeetingPlan;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPlanPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description 会议规划转换器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingPlanConverter {

    MeetingPlanPO toPO(MeetingPlan meetingPlan);

    MeetingPlan toEntity(MeetingPlanPO meetingPlanPO);
}
