package cn.genn.pf.orch.meeting.domain.team.service;

import cn.genn.core.context.BaseRequestContext;
import cn.genn.pf.orch.meeting.domain.team.model.entity.TeamInfo;
import cn.genn.pf.orch.meeting.domain.team.model.entity.TeamLevel;
import cn.genn.pf.orch.meeting.infrastructure.converter.TeamConverter;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TeamInfoMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TeamLevelMapper;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class TeamDomainService {

    @Resource
    private TeamLevelMapper teamLevelMapper;
    @Resource
    private TeamInfoMapper teamInfoMapper;
    @Resource
    private TeamConverter teamConverter;

    public Boolean saveOrUpdateLevel(TeamLevel teamLevel) {
        if (ObjUtil.isNull(teamLevel.getId())) {
            teamLevelMapper.insert(teamConverter.toPO(teamLevel));
        } else {
            teamLevelMapper.updateById(teamConverter.toPO(teamLevel));
        }
        return true;
    }

    public Boolean removeLevel(Long id) {
        teamLevelMapper.deleteById(id);
        return true;
    }


    public Boolean saveOrUpdate(TeamInfo teamInfo) {
        //去重
        teamInfo.setMembers(CollUtil.distinct(teamInfo.getMembers()));
        teamInfo.setTechnicians(CollUtil.distinct(teamInfo.getTechnicians()));
        teamInfo.setReviewers(CollUtil.distinct(teamInfo.getReviewers()));

        if (ObjUtil.isNull(teamInfo.getId())) {
            teamInfoMapper.insert(teamConverter.toPO(teamInfo));
        } else {
            teamInfoMapper.updateById(teamConverter.toPO(teamInfo));
        }
        return true;
    }

    public Boolean removeInfo(Long id) {
        teamInfoMapper.deleteById(id);
        return true;
    }

}
