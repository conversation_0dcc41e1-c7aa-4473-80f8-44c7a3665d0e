package cn.genn.pf.orch.meeting.domain.schedule.model.entity;

import cn.genn.pf.orch.meeting.infrastructure.repository.po.Catalog;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * ScheduleConfig
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScheduleConfig {

    private String nameTemplate;

    private Long businessMeetingId;

    private String goal;

    private String decision;

    private Catalog dataCatalog;

    private Catalog achievementCatalog;
}
