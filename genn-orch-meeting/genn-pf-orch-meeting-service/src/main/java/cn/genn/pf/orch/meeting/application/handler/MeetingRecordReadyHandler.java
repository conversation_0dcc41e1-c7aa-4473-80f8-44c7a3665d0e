package cn.genn.pf.orch.meeting.application.handler;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.application.dto.card.SendShareMeetingVideoDTO;
import cn.genn.pf.orch.meeting.application.service.action.CardSendActionService;
import cn.genn.pf.orch.meeting.application.service.action.SsoActionService;
import cn.genn.pf.orch.meeting.application.service.action.NewMeetingActionService;
import cn.genn.pf.orch.meeting.application.service.query.MeetingQueryService;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.schedule.service.MeetingDomainService;
import cn.genn.pf.orch.meeting.infrastructure.enums.EventCallbackEnum;
import cn.genn.pf.orch.meeting.infrastructure.utils.DateUtils;
import cn.genn.pf.orch.meeting.interfaces.command.MeetingRecordReadyEventCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.user.UserInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.third.feishu.app.FeishuAppClient;
import cn.genn.third.feishu.app.model.callback.EventCallbackCommand;
import cn.genn.third.feishu.app.model.meeting.MeetingSetPermissionModel;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 会议录制完成事件处理器
 * @date 2025-01-07
 */
@Slf4j
@Component
public class MeetingRecordReadyHandler implements CallbackHandler<MeetingRecordReadyEventCommand> {

    @Resource
    private MeetingQueryService meetingQueryService;
    @Resource
    private MeetingDomainService meetingDomainService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private ScheduleQueryService scheduleQueryService;
    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private SsoActionService ssoActionService;
    @Resource
    private NewMeetingActionService newMeetingActionService;

    @Override
    public void handle(EventCallbackCommand command) {
        String meetingJson = JsonUtils.toJson(command.getEvent());
        MeetingRecordReadyEventCommand fsCommand = JsonUtils.parse(meetingJson, MeetingRecordReadyEventCommand.class);

        // 新会议妙计链接同步
        newMeetingActionService.handleFeishuRecordReadyCallback(
            fsCommand.getMeeting().getId(),
            fsCommand.getUrl()
        );
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.MEETING_RECORD_READY;
    }
}
