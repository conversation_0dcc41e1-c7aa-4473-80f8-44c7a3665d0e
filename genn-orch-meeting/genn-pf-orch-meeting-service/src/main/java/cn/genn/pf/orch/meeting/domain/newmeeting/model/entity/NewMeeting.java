package cn.genn.pf.orch.meeting.domain.newmeeting.model.entity;

import cn.genn.pf.orch.meeting.interfaces.enums.NewMeetingStatusEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.PriorityLevelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 新会议领域实体
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewMeeting {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 会议名称
     */
    private String meetingName;

    /**
     * 会议描述
     */
    private String meetingDescription;

    /**
     * 会议规划ID
     */
    private Long meetingPlanId;

    /**
     * 会议标准ID
     */
    private Long meetingStandardId;

    /**
     * 会议编号
     */
    private String meetingNo;

    /**
     * 会议开始时间
     */
    private LocalDateTime startTime;

    /**
     * 会议结束时间
     */
    private LocalDateTime endTime;

    /**
     * 会议状态
     */
    private NewMeetingStatusEnum status;

    /**
     * 优先级
     */
    private PriorityLevelEnum priorityLevel;

    /**
     * 会议地点
     */
    private String meetingLocation;

    /**
     * 参会人员列表（用户ID列表，与会议规划保持一致）
     */
    private List<String> attendees;

    /**
     * 主持人ID
     */
    private String hostUserId;

    /**
     * 记录员ID
     */
    private String recorderUserId;

    /**
     * 飞书日程事件ID
     */
    private String fsCalendarEventId;

    /**
     * 飞书会议ID
     */
    private String fsMeetingId;

    /**
     * 会议链接
     */
    private String meetingUrl;

    /**
     * 妙计链接
     */
    private String minuteUrl;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 计算会议持续时长（分钟）
     */
    public Integer calculateDuration() {
        if (startTime == null || endTime == null) {
            return null;
        }
        return (int) java.time.Duration.between(startTime, endTime).toMinutes();
    }

    /**
     * 检查会议是否可以修改
     */
    public boolean canUpdate() {
        return status == NewMeetingStatusEnum.NOT_STARTED;
    }

    /**
     * 检查会议是否可以删除
     */
    public boolean canDelete() {
        return status == NewMeetingStatusEnum.NOT_STARTED;
    }
} 