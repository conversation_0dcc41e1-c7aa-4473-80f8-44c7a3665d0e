package cn.genn.pf.orch.meeting.domain.business.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * BusinessMeeting
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessMeeting {

    private String name;

    private Long parentId;
}
