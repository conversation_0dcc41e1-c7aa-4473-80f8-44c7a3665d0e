package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.application.handler.CallbackHandler;
import cn.genn.pf.orch.meeting.application.handler.CallbackHandlerFactory;
import cn.genn.pf.orch.meeting.infrastructure.constant.CacheConstants;
import cn.genn.pf.orch.meeting.infrastructure.enums.EventCallbackEnum;
import cn.genn.third.feishu.app.model.callback.EventCallBackDTO;
import cn.genn.third.feishu.app.model.callback.EventCallbackCommand;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 回调应用层服务
 * @date 2024-12-30
 */
@Slf4j
@Service
public class CallbackActionService {

    @Resource
    private CallbackHandlerFactory callbackHandlerFactory;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    public <T> EventCallBackDTO callback(EventCallbackCommand<T> command) {
        log.info("callback command:{}", JsonUtils.toJson(command));
        //防重逻辑
        if(ObjUtil.isNotNull(command.getHeader()) && ObjUtil.isNotNull(command.getHeader().getEventId())){
            String callbackKey = CacheConstants.getFsCallbackEvent(command.getHeader().getEventId());
            String str = stringRedisTemplate.opsForValue().get(callbackKey);
            if(StrUtil.isNotBlank(str)){
                log.warn("重复回调事件,eventId:{}", command.getHeader().getEventId());
                return EventCallBackDTO.builder().build();
            }
            stringRedisTemplate.opsForValue().set(callbackKey, LocalDateTime.now().toString(), 5, TimeUnit.SECONDS);
        }
        CallbackHandler<T> callbackHandler = callbackHandlerFactory.getCallbackHandler(EventCallbackEnum.of(command.getHeader().getEventType()));
        if (Objects.isNull(callbackHandler)) {
            log.error("未找到对应的回调处理器");
            return EventCallBackDTO.builder().build();
        }
        callbackHandler.handle(command);
        return EventCallBackDTO.builder().challenge(command.getChallenge()).build();
    }
}
