package cn.genn.pf.orch.meeting.application.handler;

import cn.genn.core.context.BaseRequestContext;
import cn.genn.pf.orch.meeting.infrastructure.constant.Constants;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.time.LocalDateTime;

/**
 * 数据库自动写入公共字段:openId,name,time
 */
@Slf4j
public class InsertOrUpdateMetaObjectHandler implements MetaObjectHandler {
    private final String defaultUserName;
    private final String defaultUserId;

    public InsertOrUpdateMetaObjectHandler() {
        this.defaultUserId = "";
        this.defaultUserName = "";
    }

    public void insertFill(MetaObject metaObject) {
        String userId = this.getUserId();
        String username = this.getUserName();
        this.strictInsertFill(metaObject, "createUserName", String.class, username);
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
        this.strictInsertFill(metaObject, "updateUserName", String.class, username);

        // 对于String类型的用户ID字段（如其他表）
        this.strictInsertFill(metaObject, "createUserId", String.class, userId);
        this.strictInsertFill(metaObject, "updateUserId", String.class, userId);
    }

    public void updateFill(MetaObject metaObject) {
        String userId = this.getUserId();
        String username = this.getUserName();
        this.strictUpdateFill(metaObject, "updateUserName", String.class, username);
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());

        // 对于String类型的用户ID字段（如其他表）
        this.strictUpdateFill(metaObject, "updateUserId", String.class, userId);
    }

    private String getUserId() {
        String userId = null;
        BaseRequestContext baseRequestContext = BaseRequestContext.get();
        if (baseRequestContext != null) {
            userId = BaseRequestContext.getAttachment(Constants.OPEN_ID, String.class);
        }
        return userId != null ? userId : this.defaultUserId;
    }

    private String getUserName() {
        String userName = null;
        BaseRequestContext baseRequestContext = BaseRequestContext.get();
        if (baseRequestContext != null) {
            userName = BaseRequestContext.getAttachment(Constants.USER_NAME, String.class);
        }
        return userName != null ? userName : this.defaultUserName;
    }

}
