package cn.genn.pf.orch.meeting.infrastructure.feishu.req;

import com.lark.oapi.service.task.v2.model.Member;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 更新任务请求
 * @date 2025-06-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateTaskRequest {

    /**
     * 任务唯一标识
     */
    private String taskGuid;

    /**
     * 任务标题
     */
    private String summary;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    private LocalDateTime endTime;

    /**
     * 任务完成时间
     */
    private LocalDateTime completedTime;

    /**
     * 扩展信息
     */
    private String extra;

    /**
     * 任务成员列表
     */
    private List<Member> members;

    public String[] getUpdateFields() {
        List<String> fields = new ArrayList<>();
        if (summary != null) fields.add("summary");
        if (description != null) fields.add("description");
        if (startTime != null) fields.add("start");
        if (completedTime != null) fields.add("completed_at");
        if (endTime != null) fields.add("due");
        if (extra != null) fields.add("extra");
        if (members != null && !members.isEmpty()) fields.add("members");
        return fields.toArray(new String[0]);
    }
}
