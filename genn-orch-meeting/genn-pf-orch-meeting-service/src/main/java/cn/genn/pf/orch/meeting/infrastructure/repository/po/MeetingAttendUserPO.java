package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import cn.genn.pf.orch.meeting.interfaces.enums.CheckInStatusEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;


/**
 * MeetingAttendUserPO对象
 *
 * <AUTHOR>
 * @desc 会议参与人表
 */
@Data
@Accessors(chain = true)
@TableName(value = "meeting_attend_user", autoResultMap = true)
public class MeetingAttendUserPO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 会议id
     */
    @TableField("meeting_id")
    private Long meetingId;

    /**
     * 飞书用户id
     */
    @TableField("fs_user_id")
    private String fsUserId;

    /**
     * 第一次入会时间
     */
    @TableField("first_join_time")
    private LocalDateTime firstJoinTime;

    /**
     * 最终离会时间
     */
    @TableField("final_leave_time")
    private LocalDateTime finalLeaveTime;

    /**
     * 累计在会中时间，时间单位：秒
     */
    @TableField("in_meeting_duration")
    private Integer inMeetingDuration;

    /**
     * 签到状态 0-未签到, 1-已签到
     */
    @TableField("check_in_status")
    private CheckInStatusEnum checkInStatus;

    /**
     * 签到地点
     */
    @TableField("check_in_address")
    private String checkInAddress;

    /**
     * 签到时间
     */
    @TableField("check_in_time")
    private LocalDateTime checkInTime;

}

