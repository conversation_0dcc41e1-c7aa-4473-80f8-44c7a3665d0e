package cn.genn.pf.orch.meeting.application.service.query;

import cn.genn.pf.orch.meeting.application.assembler.MeetingStandardAssembler;
import cn.genn.pf.orch.meeting.domain.meetingstandard.model.entity.MeetingStandard;
import cn.genn.pf.orch.meeting.domain.meetingstandard.model.vo.AttendeeStatistics;
import cn.genn.pf.orch.meeting.domain.meetingstandard.repository.IMeetingStandardRepository;
import cn.genn.pf.orch.meeting.domain.meetingstandard.service.MeetingStandardDomainService;
import cn.genn.pf.orch.meeting.infrastructure.converter.MeetingStandardConverter;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingStandardMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingStandardPO;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingstandard.MeetingStandardDTO;
import cn.genn.pf.orch.meeting.interfaces.query.meetingstandard.MeetingStandardQuery;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准查询服务
 * @date 2025-01-24
 */
@Service
@RequiredArgsConstructor
public class MeetingStandardQueryService {

    private final MeetingStandardAssembler meetingStandardAssembler;
    private final IMeetingStandardRepository meetingStandardRepository;
    private final MeetingStandardDomainService meetingStandardDomainService;
    private final MeetingStandardMapper meetingStandardMapper;

    @Resource
    private MeetingStandardConverter meetingStandardConverter;

    /**
     * 查询启用的会议标准列表
     */
    public List<MeetingStandardDTO> listEnabled() {
        List<MeetingStandard> standards = meetingStandardRepository.findAllEnabled();
        return standards.stream()
            .map(meetingStandardAssembler::toDTO)
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据ID查询会议标准详情
     */
    public MeetingStandardDTO getById(Long id) {
        MeetingStandard standard = meetingStandardRepository.findById(id);
        return standard != null ? meetingStandardAssembler.toDTO(standard) : null;
    }

    /**
     * 分页查询会议标准
     */
    public IPage<MeetingStandardDTO> pageQuery(MeetingStandardQuery query) {
        LambdaQueryWrapper<MeetingStandardPO> wrapper = Wrappers.lambdaQuery(MeetingStandardPO.class);

        // 标准名称模糊查询
        if (query.getStandardName() != null && !query.getStandardName().trim().isEmpty()) {
            wrapper.like(MeetingStandardPO::getStandardName, query.getStandardName().trim());
        }

        // 优先级筛选
        if (query.getPriorityLevel() != null) {
            wrapper.eq(MeetingStandardPO::getPriorityLevel, query.getPriorityLevel());
        }

        // 启用状态筛选
        if (query.getIsEnabled() != null) {
            wrapper.eq(MeetingStandardPO::getIsEnabled, query.getIsEnabled());
        }

        // 按创建时间倒序
        wrapper.orderByDesc(MeetingStandardPO::getCreateTime);

        Page<MeetingStandardPO> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<MeetingStandardPO> result = meetingStandardMapper.selectPage(page, wrapper);

        return result.convert(po -> meetingStandardAssembler.toDTO(meetingStandardConverter.toEntity(po)));
    }

    /**
     * 获取会议标准的参会人数统计
     */
    public AttendeeStatistics getAttendeeStatistics(Long standardId) {
        return meetingStandardDomainService.getAttendeeStatistics(standardId);
    }

    /**
     * 获取会议标准的参会人数建议
     */
    public MeetingStandardDomainService.AttendeeSuggestion getAttendeeSuggestion(Long standardId) {
        return meetingStandardDomainService.getAttendeeSuggestion(standardId);
    }
}
