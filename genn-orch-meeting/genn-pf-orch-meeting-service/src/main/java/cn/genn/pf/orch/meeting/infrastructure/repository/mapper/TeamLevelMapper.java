package cn.genn.pf.orch.meeting.infrastructure.repository.mapper;

import cn.genn.core.model.enums.DeletedEnum;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamLevelPO;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TeamLevelMapper extends BaseMapper<TeamLevelPO> {

    /**
     * 模糊查询
     *
     * @param name
     * @return
     */
    List<TeamLevelPO> searchList(String name);

    List<TeamLevelPO> selectRecursionById(@Param("list") List<Long> ids);

    default List<TeamLevelPO> selectByPid(Long pid) {
        LambdaQueryWrapper<TeamLevelPO> wrapper = Wrappers.lambdaQuery(TeamLevelPO.class)
            .eq(TeamLevelPO::getPid, pid)
            .eq(TeamLevelPO::getDeleted, DeletedEnum.NOT_DELETED);
        return selectList(wrapper);
    }

    default List<TeamLevelPO> selectByLevel(Integer level,Long pid) {
        LambdaQueryWrapper<TeamLevelPO> wrapper = Wrappers.lambdaQuery(TeamLevelPO.class)
            .eq(TeamLevelPO::getLevel, level)
            .eq(ObjUtil.isNotNull(pid),TeamLevelPO::getPid, pid)
            .eq(TeamLevelPO::getDeleted, DeletedEnum.NOT_DELETED);
        return selectList(wrapper);
    }
}
