package cn.genn.pf.orch.meeting.infrastructure.converter;

import cn.genn.pf.orch.meeting.domain.meetingstandard.model.entity.MeetingStandard;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingStandardPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description 会议标准转换器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingStandardConverter {

    MeetingStandardPO toPO(MeetingStandard meetingStandard);

    MeetingStandard toEntity(MeetingStandardPO meetingStandardPO);
}
