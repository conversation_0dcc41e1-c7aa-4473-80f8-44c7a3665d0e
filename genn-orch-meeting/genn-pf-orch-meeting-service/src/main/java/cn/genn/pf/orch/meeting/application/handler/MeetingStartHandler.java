package cn.genn.pf.orch.meeting.application.handler;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.application.dto.MeetingStartDTO;
import cn.genn.pf.orch.meeting.application.dto.card.SendUploadMeetingRecordDTO;
import cn.genn.pf.orch.meeting.application.service.action.CardSendActionService;
import cn.genn.pf.orch.meeting.application.service.action.NewMeetingActionService;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.meeting.model.event.SignInCodeEvent;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IMeetingRepository;
import cn.genn.pf.orch.meeting.domain.schedule.service.MeetingDomainService;
import cn.genn.pf.orch.meeting.infrastructure.enums.EventCallbackEnum;
import cn.genn.pf.orch.meeting.infrastructure.properties.MeetingSeverProperties;
import cn.genn.pf.orch.meeting.infrastructure.utils.DateUtils;
import cn.genn.pf.orch.meeting.interfaces.command.MeetingStartEndEventCommand;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import cn.genn.spring.boot.starter.event.spring.component.SpringEventPublish;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.third.feishu.app.model.callback.EventCallbackCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 会议开始回调处理器
 * @date 2024-12-25
 */
@Slf4j
@Component
public class MeetingStartHandler implements CallbackHandler<MeetingStartEndEventCommand> {

    @Resource
    private NewMeetingActionService newMeetingActionService;
    @Resource
    private SpringEventPublish springEventPublish;
    @Resource
    private IMeetingRepository meetingRepository;
    @Resource
    private ScheduleQueryService scheduleQueryService;
    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private MeetingSeverProperties meetingSeverProperties;

    @Override
    public void handle(EventCallbackCommand command) {
        String meetingJson = JsonUtils.toJson(command.getEvent());
        MeetingStartEndEventCommand startCommand = JsonUtils.parse(meetingJson, MeetingStartEndEventCommand.class);
        MeetingStartEndEventCommand.Meeting fsCommand = startCommand.getMeeting();
        // 新会议同步
        newMeetingActionService.handleFeishuMeetingStartCallback(
            fsCommand.getCalendarEventId(),
            fsCommand.getMeetingNo(),
            fsCommand.getId(),
            fsCommand.getStartTime()
        );
    }

    @Override
    public EventCallbackEnum event() {
        return EventCallbackEnum.MEETING_START;
    }
}
