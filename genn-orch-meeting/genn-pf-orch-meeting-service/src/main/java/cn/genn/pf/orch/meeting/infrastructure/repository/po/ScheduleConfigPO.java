package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import cn.genn.database.mybatisplus.typehandler.JacksonTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ScheduleConfigPO
 *
 * <AUTHOR>
 * @desc 日程配置表
 */
@Data
@Accessors(chain = true)
@TableName(value = "schedule_config", autoResultMap = true)
public class ScheduleConfigPO {

    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 业务会议id
     */
    @TableField("business_meeting_id")
    private Long businessMeetingId;

    /**
     * 会议名称模板
     */
    @TableField("name_template")
    private String nameTemplate;

    /**
     * 会议目的
     */
    @TableField("goal")
    private String goal;

    /**
     * 会议决策
     */
    @TableField("decision")
    private String decision;

    /**
     * 资料目录 Json格式, {"names":["name1","name2",...]}
     */
    @TableField(value = "data_catalog", typeHandler = JacksonTypeHandler.class)
    private Catalog dataCatalog;

    /**
     * 成果目录 Json格式, {"names":["name1","name2",...]}
     */
    @TableField(value = "achievement_catalog", typeHandler = JacksonTypeHandler.class)
    private Catalog achievementCatalog;
}
