package cn.genn.pf.orch.meeting.application.assembler;

import cn.genn.core.model.assembler.QueryAssembler;
import cn.genn.pf.orch.meeting.domain.team.model.entity.TeamInfo;
import cn.genn.pf.orch.meeting.domain.team.model.entity.TeamLevel;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamInfoPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamLevelPO;
import cn.genn.pf.orch.meeting.interfaces.command.TeamInfoCommand;
import cn.genn.pf.orch.meeting.interfaces.command.TeamLevelCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamLevelDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamLevelTreeDTO;
import cn.genn.pf.orch.meeting.interfaces.query.team.TeamPageQuery;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TeamAssembler extends QueryAssembler<TeamPageQuery, TeamInfoPO, TeamInfoDTO> {

    TeamLevelTreeDTO toTeamLevelTreeDTO(TeamLevelPO teamLevelPO);

    TeamLevel toTeamLevel(TeamLevelCommand command);

    TeamInfo toTeamInfo(TeamInfoCommand command);

    List<TeamLevelDTO> levelPO2DTO(List<TeamLevelPO> poList);
}
