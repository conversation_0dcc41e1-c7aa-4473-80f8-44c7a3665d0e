package cn.genn.pf.orch.meeting.infrastructure.repository.persistence;

import cn.genn.pf.orch.meeting.domain.task.model.aggregates.TaskAgg;
import cn.genn.pf.orch.meeting.domain.task.repository.ITaskRepository;
import cn.genn.pf.orch.meeting.infrastructure.converter.TaskConverter;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TaskMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TaskPO;
import cn.genn.pf.orch.meeting.interfaces.enums.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Assistant
 * @description 任务仓储实现
 */
@Slf4j
@Repository
public class TaskRepositoryImpl implements ITaskRepository {

    @Resource
    private TaskMapper taskMapper;
    @Resource
    private TaskConverter taskConverter;

    @Override
    public Long insert(TaskAgg taskAgg) {
        TaskPO taskPO = taskConverter.toTaskPO(taskAgg.getInfo());
        taskMapper.insert(taskPO);
        taskAgg.getInfo().setId(taskPO.getId());
        return taskPO.getId();
    }

    @Override
    public void update(TaskAgg taskAgg) {
        TaskPO taskPO = taskConverter.toTaskPO(taskAgg.getInfo());
        taskMapper.updateById(taskPO);
    }

    @Override
    public TaskAgg findById(Long id) {
        TaskPO taskPO = taskMapper.selectById(id);
        if (taskPO == null) {
            return null;
        }
        return TaskAgg.builder()
                .info(taskConverter.toTaskInfo(taskPO))
                .build();
    }

    @Override
    public List<TaskAgg> findByMeetingId(Long meetingId) {
        List<TaskPO> taskPOList = taskMapper.findByMeetingId(meetingId);
        return taskPOList.stream()
                .map(taskPO -> TaskAgg.builder()
                        .info(taskConverter.toTaskInfo(taskPO))
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<TaskAgg> findByStatus(TaskStatusEnum status) {
        List<TaskPO> taskPOList = taskMapper.findByStatus(status);
        return taskPOList.stream()
                .map(taskPO -> TaskAgg.builder()
                        .info(taskConverter.toTaskInfo(taskPO))
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<TaskAgg> findByOwner(String ownerOpenId) {
        List<TaskPO> taskPOList = taskMapper.findByOwner(ownerOpenId);
        return taskPOList.stream()
                .map(taskPO -> TaskAgg.builder()
                        .info(taskConverter.toTaskInfo(taskPO))
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public void delete(TaskAgg taskAgg) {
        TaskPO taskPO = taskConverter.toTaskPO(taskAgg.getInfo());
        taskMapper.updateById(taskPO);
    }

    @Override
    public List<TaskAgg> findOverdueTasks() {
        List<TaskPO> taskPOList = taskMapper.findOverdueTasks(LocalDateTime.now());
        return taskPOList.stream()
                .map(taskPO -> TaskAgg.builder()
                        .info(taskConverter.toTaskInfo(taskPO))
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public TaskAgg findByFeishuTaskId(String feishuTaskId) {
        TaskPO taskPO = taskMapper.findByFeishuTaskId(feishuTaskId);
        if (taskPO == null) {
            return null;
        }
        return TaskAgg.builder()
                .info(taskConverter.toTaskInfo(taskPO))
                .build();
    }
}
