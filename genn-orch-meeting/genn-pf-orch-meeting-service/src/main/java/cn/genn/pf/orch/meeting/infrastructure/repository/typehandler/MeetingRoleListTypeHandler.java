package cn.genn.pf.orch.meeting.infrastructure.repository.typehandler;

import cn.genn.pf.orch.meeting.interfaces.enums.MeetingRoleEnum;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议角色枚举列表类型处理器
 * @date 2025-01-24
 */
@MappedJdbcTypes(JdbcType.VARCHAR)
@MappedTypes(List.class)
public class MeetingRoleListTypeHandler extends BaseTypeHandler<List<MeetingRoleEnum>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<MeetingRoleEnum> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null && !parameter.isEmpty()) {
            String codes = parameter.stream()
                .map(MeetingRoleEnum::getCode)
                .collect(Collectors.joining(","));
            ps.setString(i, codes);
        } else {
            ps.setString(i, null);
        }
    }

    @Override
    public List<MeetingRoleEnum> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return parseRoles(value);
    }

    @Override
    public List<MeetingRoleEnum> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return parseRoles(value);
    }

    @Override
    public List<MeetingRoleEnum> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return parseRoles(value);
    }

    private List<MeetingRoleEnum> parseRoles(String value) {
        if (value == null || value.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        return Arrays.stream(value.split(","))
            .map(String::trim)
            .filter(code -> !code.isEmpty())
            .map(MeetingRoleEnum::getByCode)
            .filter(role -> role != null)
            .collect(Collectors.toList());
    }
} 