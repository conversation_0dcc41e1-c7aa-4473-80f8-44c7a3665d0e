package cn.genn.pf.orch.meeting.application.service.action;

import cn.genn.core.utils.jackson.JsonUtils;
import cn.genn.pf.orch.meeting.application.assembler.UserInfoAssembler;
import cn.genn.pf.orch.meeting.infrastructure.constant.CacheConstants;
import cn.genn.pf.orch.meeting.infrastructure.exception.AuthException;
import cn.genn.pf.orch.meeting.infrastructure.exception.MessageCode;
import cn.genn.pf.orch.meeting.infrastructure.properties.MeetingSeverProperties;
import cn.genn.pf.orch.meeting.interfaces.command.LogoutToolCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.user.UserInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.user.UserTokenDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.ClientTypeEnum;
import cn.genn.pf.orch.meeting.interfaces.query.SsoUserLoginQuery;
import cn.genn.pf.orch.meeting.interfaces.query.SsoUserTokenQuery;
import cn.genn.third.feishu.app.FeishuAppClient;
import cn.genn.third.feishu.app.model.AuthUserAccessTokenModel;
import cn.genn.third.feishu.app.model.ContactSearchUserDTO;
import cn.genn.third.feishu.app.model.ContactSearchUserResponse;
import cn.genn.third.feishu.app.model.UserDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.lark.oapi.service.authen.v1.model.GetUserInfoRespBody;
import com.lark.oapi.service.contact.v3.model.BatchGetIdUserRespBody;
import com.lark.oapi.service.tenant.v2.model.Tenant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SsoActionService {

    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MeetingSeverProperties meetingSeverProperties;
    @Resource
    private UserInfoAssembler userInfoAssembler;

    /**
     * 免登过程-获取token
     */
    public UserTokenDTO getUserToken(SsoUserLoginQuery query) {
        AuthUserAccessTokenModel authUserAccessTokenModel = feishuAppClient.getAuthService().authUserAccessToken(query.getCode(), query.getRedirectUri());
        String uuid = IdUtil.fastSimpleUUID();
        String token = Base64.getUrlEncoder().withoutPadding().encodeToString(uuid.getBytes());
        long timeOut = meetingSeverProperties.getLogin().getTimeOut();
        generateFsUserInfo(token, authUserAccessTokenModel.getUserAccessToken(), query.getClientType(),timeOut);
        return UserTokenDTO.builder()
            .token(token)
            .timeOut(meetingSeverProperties.getLogin().getTimeOut())
            .build();
    }

    public UserInfoDTO getUserInfo(String token) {
        UserInfoDTO userInfoDTO = this.getCacheUserInfo(token);
        String userAccessToken = userInfoDTO.getUserAccessToken();
        AuthUserAccessTokenModel userAccessTokenModel = feishuAppClient.getAuthService().getUserAccessToken(userAccessToken);
        if (!userInfoDTO.getUserAccessToken().equals(userAccessTokenModel.getUserAccessToken())) {
            Long expire = stringRedisTemplate.getExpire(CacheConstants.getAuthToken(token));
            long timeOut = meetingSeverProperties.getLogin().getTimeOut();
            return this.generateFsUserInfo(token, userAccessTokenModel.getUserAccessToken(), userInfoDTO.getClientType(), Optional.ofNullable(expire).orElse(timeOut));
        } else {
            log.info("token:{}无须刷新",token);
        }
        return userInfoDTO;
    }

    public UserInfoDTO getCacheUserInfo(String token) {
        String userInfo = stringRedisTemplate.opsForValue().get(CacheConstants.getAuthToken(token));
        if (StrUtil.isBlank(userInfo)) {
            throw new AuthException(MessageCode.AUTH_FAIL);
        }
        return JsonUtils.parse(userInfo, UserInfoDTO.class);
    }

    private UserInfoDTO generateFsUserInfo(String token, String userToken, ClientTypeEnum clientType,long timeOut) {
        GetUserInfoRespBody respBody = feishuAppClient.getAuthService().getUserInfo(userToken);
        UserInfoDTO userInfoDTO = userInfoAssembler.resp2DTO(respBody);
        userInfoDTO.setToken(token);
        userInfoDTO.setClientType(clientType);
        userInfoDTO.setLastRefreshTime(LocalDateTime.now());
        userInfoDTO.setUserAccessToken(userToken);
        Tenant tenantInfo = feishuAppClient.getTenantService().getTenantInfo();
        if (tenantInfo != null) {
            userInfoDTO.setCompanyName(tenantInfo.getName());
        }
        stringRedisTemplate.opsForValue().set(CacheConstants.getAuthToken(token), JsonUtils.toJson(userInfoDTO), timeOut, TimeUnit.SECONDS);
        stringRedisTemplate.opsForValue().set(CacheConstants.getFeishuOpenId(clientType.getCode(), userInfoDTO.getOpenId()), JsonUtils.toJson(userInfoDTO), timeOut, TimeUnit.SECONDS);
        return userInfoDTO;
    }

    public Boolean refreshToken(SsoUserTokenQuery query) {
        UserInfoDTO cacheUserInfo = this.getCacheUserInfo(query.getToken());
        long timeOut = meetingSeverProperties.getLogin().getTimeOut();
        stringRedisTemplate.expire(CacheConstants.getAuthToken(query.getToken()), timeOut, TimeUnit.SECONDS);
        stringRedisTemplate.expire(CacheConstants.getFeishuOpenId(cacheUserInfo.getClientType().getCode(), cacheUserInfo.getOpenId()), timeOut, TimeUnit.SECONDS);
        return true;
    }


    public Boolean logout(String token) {
        stringRedisTemplate.delete(CacheConstants.getAuthToken(token));
        return true;
    }

    public Boolean logoutTool(LogoutToolCommand command) {
        if (CollUtil.isNotEmpty(command.getOpenIdList())) {
            command.getOpenIdList().forEach(this::logoutByOpenId);
        }
        if (StrUtil.isNotBlank(command.getName())) {
            ContactSearchUserDTO dto = ContactSearchUserDTO.builder().query(command.getName()).build();
            ContactSearchUserResponse resp = feishuAppClient.getContactService().searchUser(dto);
            if (ObjUtil.isNotNull(resp) && !resp.getUsers().isEmpty()) {
                List<String> openIds = resp.getUsers().stream().map(UserDTO::getOpenId).distinct().collect(Collectors.toList());
                log.info("按名称登出用户,name:{},openIds:{}", command.getName(), JsonUtils.toJson(openIds));
                openIds.forEach(this::logoutByOpenId);
            }
        }
        if (StrUtil.isNotBlank(command.getTelephone())) {
            BatchGetIdUserRespBody body = feishuAppClient.getContactService().getOpenId(command.getTelephone());
            if (ObjUtil.isNotNull(body) && body.getUserList().length > 0) {
                String openId = Arrays.asList(body.getUserList()).get(0).getUserId();
                this.logoutByOpenId(openId);
            }
        }
        return true;
    }

    private void logoutByOpenId(String openId) {
        for (ClientTypeEnum value : ClientTypeEnum.values()) {
            String key = CacheConstants.getFeishuOpenId(value.getCode(), openId);
            String userInfo = stringRedisTemplate.opsForValue().get(key);
            if (StrUtil.isNotBlank(userInfo)) {
                String token = JsonUtils.parse(userInfo, UserInfoDTO.class).getToken();
                this.logout(token);
            }
        }

    }

    public UserInfoDTO getUserToken(String openId) {
        for (ClientTypeEnum value : ClientTypeEnum.values()) {
            String key = CacheConstants.getFeishuOpenId(value.getCode(), openId);
            String userInfo = stringRedisTemplate.opsForValue().get(key);
            if (StrUtil.isNotBlank(userInfo)) {
                UserInfoDTO userInfoDTO = JsonUtils.parse(userInfo, UserInfoDTO.class);
                String authToken = CacheConstants.getAuthToken(userInfoDTO.getToken());
                String userInfoStr = stringRedisTemplate.opsForValue().get(authToken);
                if(ObjUtil.isNotNull(userInfoStr)){
                    return userInfoDTO;
                }
            }
        }
        return null;
    }
}
