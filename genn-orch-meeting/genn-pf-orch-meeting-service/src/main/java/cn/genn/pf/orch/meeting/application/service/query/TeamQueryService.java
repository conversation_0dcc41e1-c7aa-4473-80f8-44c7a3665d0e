package cn.genn.pf.orch.meeting.application.service.query;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.application.assembler.TeamAssembler;
import cn.genn.pf.orch.meeting.application.assembler.UserInfoAssembler;
import cn.genn.pf.orch.meeting.infrastructure.config.CurrentUserHolder;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TeamInfoMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TeamLevelMapper;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.BusinessMeetingPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamInfoPO;
import cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamLevelPO;
import cn.genn.pf.orch.meeting.interfaces.dto.DropDownDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamLevelDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamLevelTreeDTO;
import cn.genn.pf.orch.meeting.interfaces.query.team.*;
import cn.genn.third.feishu.app.FeishuAppClient;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lark.oapi.service.contact.v3.model.User;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Service
public class TeamQueryService {

    @Resource
    private TeamLevelMapper teamLevelMapper;
    @Resource
    private TeamInfoMapper teamInfoMapper;
    @Resource
    private TeamAssembler teamAssembler;
    @Resource
    private FeishuAppClient feishuAppClient;
    @Resource
    private UserInfoAssembler userInfoAssembler;
    @Resource
    private BusinessMeetingQueryService businessMeetingQueryService;


    public List<TeamLevelTreeDTO> getTree(String name) {
        List<TeamLevelPO> teamLevelPOS = new ArrayList<>();
        if (StrUtil.isBlank(name)) {
            teamLevelPOS = teamLevelMapper.selectList(null);
        } else {
            teamLevelPOS = teamLevelMapper.searchList(name);
        }
        return getTreeList(teamLevelPOS);
    }


    public PageResultDTO<TeamInfoDTO> page(TeamPageQuery query) {
        PageResultDTO<TeamInfoDTO> pageResult = teamAssembler.toPageResult(teamInfoMapper.TeamPage(new Page<>(query.getPageNo(), query.getPageSize()), query));
        if (CollUtil.isNotEmpty(pageResult.getList())) {
            pageResult.setList(this.suppleUserInfo(pageResult.getList()));
        }
        return pageResult;
    }

    /**
     * 补充内部用户信息
     * @param List
     * @return
     */
    public List<TeamInfoDTO> suppleUserInfo(List<TeamInfoDTO> List) {
        if(CollUtil.isEmpty(List)){
            return List;
        }
        //用户信息补充
        arrangeMemberInfo(List);
        //完成通知人员补充
        arrangeFinishNotifyInfo(List);
        //班组单位和区域补充
        arrangeTeamLevel(List);
        return List;
    }

    private void arrangeTeamLevel(List<TeamInfoDTO> List){
        List<Long> levelIdList = List.stream().map(TeamInfoDTO::getLevelId).distinct().collect(Collectors.toList());
        List<TeamLevelPO> list = teamLevelMapper.selectBatchIds(levelIdList);
        Map<Long,TeamLevelPO> levelPOMap = list.stream().collect(Collectors.toMap(TeamLevelPO::getId, Function.identity()));
        List<Long> PidLevelIdList = list.stream().map(TeamLevelPO::getPid).distinct().collect(Collectors.toList());
        List<TeamLevelPO> pTeamLevelList = teamLevelMapper.selectBatchIds(PidLevelIdList);
        Map<Long,TeamLevelPO> pTeamLevelMap = pTeamLevelList.stream().collect(Collectors.toMap(TeamLevelPO::getId, Function.identity()));

        List.forEach(item -> {
            TeamLevelPO teamLevelPO = levelPOMap.get(item.getLevelId());
            if (teamLevelPO != null) {
                item.setLevel2Name(teamLevelPO.getName());
                TeamLevelPO pTeamLevelPO = pTeamLevelMap.get(teamLevelPO.getPid());
                if (pTeamLevelPO != null) {
                    item.setLevel1Name(pTeamLevelPO.getName());
                }
            }
        });
    }

    private void arrangeMemberInfo(List<TeamInfoDTO> list) {
        List<String> openIds = list.stream()
            .flatMap(dto -> Stream.concat(
                Stream.concat(
                    Optional.ofNullable(dto.getLeader()).map(Stream::of).orElseGet(Stream::empty),
                    Optional.ofNullable(dto.getMembers()).orElse(Collections.emptyList()).stream()
                ),
                Stream.concat(
                    Optional.ofNullable(dto.getReviewers()).orElse(Collections.emptyList()).stream(),
                    Optional.ofNullable(dto.getTechnicians()).orElse(Collections.emptyList()).stream()
                )
            )).distinct().collect(Collectors.toList());
        List<User> users = feishuAppClient.getContactService().userBatch(openIds);
        Map<String, User> userMap = users.stream().collect(Collectors.toMap(User::getOpenId, Function.identity()));
        list.forEach(item -> {
            if(StrUtil.isNotBlank(item.getLeader())){
                User user = userMap.get(item.getLeader());
                if(ObjUtil.isNotNull(user)){
                    item.setLeaderInfo(userInfoAssembler.user2DTO(user));
                }
            }
            if(CollUtil.isNotEmpty(item.getMembers())){
                List<User> members = item.getMembers().stream().map(userMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(members)){
                    item.setMemberInfos(userInfoAssembler.user2DTO(members));
                }
            }
            if(CollUtil.isNotEmpty(item.getTechnicians())){
                List<User> technicians = item.getTechnicians().stream().map(userMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(technicians)) {
                    item.setTechnicianInfos(userInfoAssembler.user2DTO(technicians));
                }
            }
            if(CollUtil.isNotEmpty(item.getReviewers())){
                List<User> reviews = item.getReviewers().stream().map(userMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(reviews)) {
                    item.setReviewerInfos(userInfoAssembler.user2DTO(reviews));
                }
            }
        });
    }

    private void arrangeFinishNotifyInfo(List<TeamInfoDTO> List){
        List<Long> levelIdList = List.stream().map(TeamInfoDTO::getLevelId).distinct().collect(Collectors.toList());
        List<TeamLevelPO> teamLevelPOS = teamLevelMapper.selectBatchIds(levelIdList);
        List<String> notifyOpenIds = teamLevelPOS.stream().flatMap(item -> item.getFinishNotify().stream()).distinct().collect(Collectors.toList());
        List<User> users = feishuAppClient.getContactService().userBatch(notifyOpenIds);
        Map<String, User> userMap = users.stream().collect(Collectors.toMap(User::getOpenId, Function.identity()));

        Map<Long,TeamLevelPO> levelPOMap = teamLevelPOS.stream().collect(Collectors.toMap(TeamLevelPO::getId, Function.identity()));
        List.forEach(item -> {
            TeamLevelPO teamLevelPO = levelPOMap.get(item.getLevelId());
            if(CollUtil.isNotEmpty(teamLevelPO.getFinishNotify())){
                item.setFinishNotify(teamLevelPO.getFinishNotify());
                List<User> members = teamLevelPO.getFinishNotify().stream().map(userMap::get).collect(Collectors.toList());
                item.setFinishNotifyInfos(userInfoAssembler.user2DTO(members));
            }
        });
    }


    public List<String> queryGroupList(TeamGroupQuery query){
        List<TeamInfoPO> teamInfoPOS = teamInfoMapper.selectByLevelId(query.getLevelId());
        return teamInfoPOS.stream().map(TeamInfoPO::getGroupName).distinct().collect(Collectors.toList());
    }

    public TeamInfoDTO queryById(Long id){
        TeamInfoDTO teamInfoDTO = teamAssembler.PO2DTO(teamInfoMapper.selectById(id));
        if (teamInfoDTO == null) {
            return null;
        }
        return suppleUserInfo(Collections.singletonList(teamInfoDTO)).get(0);
    }

    public List<TeamInfoDTO> queryByIds(List<Long> ids){
        List<TeamInfoPO> teamInfoPOS = teamInfoMapper.selectBatchIds(ids);
        return suppleUserInfo(teamAssembler.PO2DTO(teamInfoPOS));
    }

    public List<TeamInfoDTO> queryTeamList(TeamQuery query){
        return teamAssembler.PO2DTO(teamInfoMapper.queryTeamList(query));
    }
    public List<User> queryLeaderList(TeamLeaderQuery query){
        List<TeamInfoPO> teamInfoPOS = teamInfoMapper.selectByLevelId(query.getLevelId());
        List<String> leaderOpenIds = teamInfoPOS.stream().map(TeamInfoPO::getLeader).distinct().collect(Collectors.toList());
        return feishuAppClient.getContactService().userBatch(leaderOpenIds);
    }
    public List<User> queryReviewerList(TeamReviewerQuery query){
        List<TeamInfoPO> teamInfoPOS = teamInfoMapper.selectByLevelId(query.getLevelId());
        List<String> reviewerOpenIds = teamInfoPOS.stream().flatMap(teamInfoPO -> teamInfoPO.getReviewers().stream()).distinct().collect(Collectors.toList());
        return feishuAppClient.getContactService().userBatch(reviewerOpenIds);
    }

    public List<TeamLevelDTO> queryCoalMiningUnit(CoalMiningUnitQuery query){
        return teamAssembler.levelPO2DTO(teamLevelMapper.selectByLevel(2,Optional.of(query.getLevelId()).orElse(null)));
    }

    public List<TeamLevelDTO> queryCoalMiningRegion(){
        return teamAssembler.levelPO2DTO(teamLevelMapper.selectByLevel(1,null));
    }

    public List<DropDownDTO<Long,String>> queryTeamByCurrentOpenId(){
        String openId = CurrentUserHolder.getOpenId();
        List<TeamInfoPO> teamInfoPOS = teamInfoMapper.selectByLeader(openId);
        if(CollUtil.isEmpty(teamInfoPOS)){
            return Collections.emptyList();
        }
        List<Long> levelIds = teamInfoPOS.stream().map(TeamInfoPO::getLevelId).distinct().collect(Collectors.toList());
        List<TeamLevelPO> teamLevelPOS = teamLevelMapper.selectRecursionById(levelIds);
        Map<Long, TeamLevelPO> TeamLevelMap = teamLevelPOS.stream().collect(Collectors.toMap(TeamLevelPO::getId, Function.identity()));
        List<DropDownDTO<Long,String>> nameList = new ArrayList<>();
        for (TeamInfoPO teamInfoPO : teamInfoPOS) {
            TeamLevelPO unitPO = TeamLevelMap.get(teamInfoPO.getLevelId());
            TeamLevelPO regionPO = TeamLevelMap.get(unitPO.getPid());
            String name = regionPO.getName() + "-" + unitPO.getName()+ "-" + teamInfoPO.getGroupName()+ "-" + teamInfoPO.getName();
            nameList.add(new DropDownDTO<>(teamInfoPO.getId(), name));
        }
        return nameList;
    }

    public String getScheduleName(Long teamId, Long businessMeetingId) {
        TeamInfoDTO teamInfoDTO = queryById(teamId);
        BusinessMeetingPO businessMeetingPO = businessMeetingQueryService.get(businessMeetingId);
        return teamInfoDTO.getLevel1Name() + "-" + teamInfoDTO.getLevel2Name() + "-"
            + businessMeetingPO.getName() + "-" + teamInfoDTO.getGroupName() + "-" + teamInfoDTO.getName();
    }

    // 多父树
    private List<TeamLevelTreeDTO> getTreeList(List<TeamLevelPO> list) {
        List<TeamLevelTreeDTO> resultList = new ArrayList<>();
        for (TeamLevelPO region : list) {
            if (region.getPid() == null || region.getPid().equals(0L)) {
                TeamLevelTreeDTO dto = teamAssembler.toTeamLevelTreeDTO(region);
                resultList.add(buildResourceTree(dto, list));
            }
        }
        return resultList;
    }

    // 递归方法
    private TeamLevelTreeDTO buildResourceTree(TeamLevelTreeDTO dto, List<TeamLevelPO> allList) {
        List<TeamLevelTreeDTO> children = new ArrayList<>();
        for (TeamLevelPO region : allList) {
            if (region.getPid().equals(dto.getId())) {
                TeamLevelTreeDTO childrenDTO = teamAssembler.toTeamLevelTreeDTO(region);
                children.add(buildResourceTree(childrenDTO, allList));
            }
        }
        dto.setChildren(children);
        return dto;
    }
}
