package cn.genn.pf.orch.meeting.application.assembler;

import cn.genn.core.model.page.PageResultDTO;
import cn.genn.pf.orch.meeting.domain.meetingplan.model.entity.MeetingPlan;
import cn.genn.pf.orch.meeting.interfaces.command.meetingplan.MeetingPlanCreateCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.MeetingPlanListDTO;
import cn.genn.pf.orch.meeting.interfaces.command.meetingplan.MeetingPlanUpdateCommand;
import cn.genn.pf.orch.meeting.interfaces.dto.meetingplan.MeetingPlanDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description 会议规划组装器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingPlanAssembler {

    MeetingPlan toEntity(MeetingPlanCreateCommand command);

    MeetingPlan toEntity(MeetingPlanUpdateCommand command);

    MeetingPlanDTO toDTO(MeetingPlan meetingPlan);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "list", source = "records")
    @Mapping(target = "totalPages", source = "pages")
    PageResultDTO<MeetingPlanListDTO> toPageResult(Page<MeetingPlanListDTO> page);
}
