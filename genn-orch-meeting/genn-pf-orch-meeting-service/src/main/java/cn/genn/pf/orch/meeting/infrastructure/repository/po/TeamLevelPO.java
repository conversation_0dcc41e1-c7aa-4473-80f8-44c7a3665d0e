package cn.genn.pf.orch.meeting.infrastructure.repository.po;

import cn.genn.database.mybatisplus.typehandler.ListLongTypeHandler;
import cn.genn.database.mybatisplus.typehandler.ListStringTypeHandler;
import com.baomidou.mybatisplus.annotation.*;
import cn.genn.core.model.enums.DeletedEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;


/**
 * TeamLevelPO对象
 *
 * <AUTHOR>
 * @desc
 */
@Data
@Accessors(chain = true)
@TableName(value = "team_level", autoResultMap = true)
public class TeamLevelPO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 上级id（为0表示没有上级）
     */
    @TableField("pid")
    private Long pid;

    /**
     * 当前层级（从1开始）
     */
    @TableField("level")
    private Integer level;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 会议完成后发送人员
     */
    @TableField(value = "finish_notify",typeHandler = ListStringTypeHandler.class)
    private List<String> finishNotify = new ArrayList<>();

    /**
     * 删除标记（0未删除）
     */
    @TableField("deleted")
    @TableLogic
    private DeletedEnum deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 飞书用户openId
     */
    @TableField(value = "create_user_id", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserId;

    /**
     * 飞书用户名
     */
    @TableField(value = "create_user_name", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 飞书用户openId
     */
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private String updateUserId;

    /**
     * 飞书用户名
     */
    @TableField(value = "update_user_name", fill = FieldFill.INSERT_UPDATE)
    private String updateUserName;

}

