package cn.genn.pf.orch.meeting.application.job;

import cn.genn.pf.orch.meeting.application.dto.card.SendMeetingEvaluationDTO;
import cn.genn.pf.orch.meeting.application.service.action.CardSendActionService;
import cn.genn.pf.orch.meeting.application.service.query.ScheduleQueryService;
import cn.genn.pf.orch.meeting.domain.meeting.model.aggregates.MeetingAgg;
import cn.genn.pf.orch.meeting.domain.schedule.repository.IMeetingRepository;
import cn.genn.pf.orch.meeting.infrastructure.properties.MeetingSeverProperties;
import cn.genn.pf.orch.meeting.infrastructure.utils.DateUtils;
import cn.genn.pf.orch.meeting.interfaces.dto.schedule.ScheduleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Slf4j
@Component
public class EvaluateReminderTasks{

    @Resource
    private IMeetingRepository IMeetingRepository;
    @Resource
    private CardSendActionService cardSendActionService;
    @Resource
    private ScheduleQueryService scheduleQueryService;
    @Resource
    private MeetingSeverProperties meetingSeverProperties;

    /**
     * 评价提醒任务
     */
    @Scheduled(fixedRate = 5L * 60 * 1000) //每5分钟执行一次
    public void executeTask() {
        for (MeetingAgg meetingAgg : IMeetingRepository.findEvaluateReminders()) {
            // 会议未结束，暂不发送
            if (meetingAgg.getInfo().getEndTime() == null) {
                continue;
            }

            // 会议已经结束2小时
            if (meetingAgg.getInfo().getEndTime().plusHours(2L).isBefore(LocalDateTime.now())) {
                ScheduleDTO scheduleDTO = scheduleQueryService.getByScheduleId(meetingAgg.getInfo().getScheduleId());
                // 发送提醒
                scheduleDTO.getTeamInfoDTO().getReviewers().forEach(reviewerId -> {
                    String url = meetingSeverProperties.getCardSend().getJumpUrl().getEvaluationUrl()
                        + "?id=" + meetingAgg.getInfo().getId();
                    cardSendActionService.sendMeetingEvaluation(SendMeetingEvaluationDTO.builder()
                        .meetingName(scheduleDTO.getScheduleName())
                        .meetingType(scheduleDTO.getBusinessMeetingName())
                        .meetingTime(DateUtils.getTimeRange(meetingAgg.getInfo().getStartTime(), meetingAgg.getInfo().getEndTime()))
                        .evaluationUrl(url)
                        .openId(reviewerId)
                        .build());
                });

                // 修改评价提醒状态改为已提醒
                IMeetingRepository.evaluateReminder(meetingAgg.getExtend().getMeetingId());
            }
        }
    }
}
