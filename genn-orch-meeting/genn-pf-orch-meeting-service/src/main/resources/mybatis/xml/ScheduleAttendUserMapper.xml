<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.pf.orch.meeting.infrastructure.repository.mapper.ScheduleAttendUserMapper">

    <insert id="batchInsertAttendUser" parameterType="java.util.List">
        INSERT INTO schedule_attend_user(schedule_id,
                                         attend_user_state,
                                         absent_reason,
                                         attend_user_role,
                                         attend_user_id,
                                         attend_user_name)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.scheduleId},
            #{item.attendUserState},
            #{item.absentReason},
            #{item.attendUserRole},
            #{item.attendUserId},
            #{item.attendUserName})
        </foreach>
    </insert>

    <update id="batchUpdateAttendUser">
        UPDATE schedule_attend_user
        SET attend_user_state = CASE
        <foreach collection="list" item="item" index="index">
            WHEN attend_user_id = #{item.attendUserId} THEN #{item.attendUserState}
        </foreach>
        END,
        absent_reason = CASE
        <foreach collection="list" item="item" index="index">
            WHEN attend_user_id = #{item.attendUserId} THEN #{item.absentReason}
        </foreach>
        END,
        attend_user_role = CASE
        <foreach collection="list" item="item" index="index">
            WHEN attend_user_id = #{item.attendUserId} THEN #{item.attendUserRole}
        </foreach>
        END
        WHERE
        schedule_id = #{scheduleId}
        AND attend_user_id IN
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item.attendUserId}
        </foreach>
    </update>

</mapper>
