<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.genn.pf.orch.meeting.infrastructure.repository.mapper.NewMeetingMapper">
    <resultMap id="NewMeetingListDTOResultMap" type="cn.genn.pf.orch.meeting.interfaces.dto.newmeeting.NewMeetingListDTO">
        <id column="id" property="id"/>
        <result column="meeting_name" property="meetingName"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="status" property="status"/>
        <result column="priority_level" property="priorityLevel"/>
        <result column="meeting_location" property="meetingLocation"/>
        <result column="attendees" property="attendees" typeHandler="cn.genn.database.mybatisplus.typehandler.ListStringTypeHandler"/>
        <result column="attendee_count" property="attendeeCount"/>
        <result column="host_user_id" property="hostUserId"/>
        <result column="recorder_user_id" property="recorderUserId"/>
        <result column="meeting_url" property="meetingUrl"/>
        <result column="minute_url" property="minuteUrl"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <select id="queryPage" resultMap="NewMeetingListDTOResultMap">
        SELECT
            id,
            meeting_name,
            start_time,
            end_time,
            status,
            priority_level,
            meeting_location,
            attendees,
            JSON_LENGTH(attendees) as attendee_count,
            host_user_id,
            recorder_user_id,
            meeting_url,
            minute_url,
            create_user_name,
            create_time
        FROM new_meeting
        <where>
            <!-- 逻辑删除条件：只查询未删除的数据 -->
            AND deleted = 0
            <if test="query.meetingName != null and query.meetingName != ''">
                AND meeting_name LIKE CONCAT('%', #{query.meetingName}, '%')
            </if>
            <if test="query.status != null">
                AND status = #{query.status}
            </if>
            <if test="query.priorityLevel != null">
                AND priority_level = #{query.priorityLevel}
            </if>
            <if test="query.meetingPlanId != null">
                AND meeting_plan_id = #{query.meetingPlanId}
            </if>
            <if test="query.meetingStandardId != null">
                AND meeting_standard_id = #{query.meetingStandardId}
            </if>
            <if test="query.startTimeFrom != null">
                AND start_time &gt;= #{query.startTimeFrom}
            </if>
            <if test="query.startTimeTo != null">
                AND start_time &lt;= #{query.startTimeTo}
            </if>
            <if test="query.createUserId != null and query.createUserId != ''">
                AND create_user_id = #{query.createUserId}
            </if>
        </where>
        ORDER BY start_time DESC
    </select>
</mapper>
