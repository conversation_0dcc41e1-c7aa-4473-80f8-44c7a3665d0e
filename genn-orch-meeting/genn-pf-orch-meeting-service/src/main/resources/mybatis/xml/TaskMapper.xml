<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TaskMapper">

    <!-- 查询超期任务 -->
    <select id="findOverdueTasks" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.TaskPO">
        SELECT * FROM tasks
        WHERE deleted = 0
          AND due_date IS NOT NULL
          AND due_date &lt; #{currentTime}
          AND status != 2
        ORDER BY due_date ASC
    </select>

    <!-- 根据会议ID查询任务列表 -->
    <select id="findByMeetingId" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.TaskPO">
        SELECT * FROM tasks
        WHERE deleted = 0
          AND meeting_id = #{meetingId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据状态查询任务列表 -->
    <select id="findByStatus" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.TaskPO">
        SELECT * FROM tasks
        WHERE deleted = 0
          AND status = #{status}
        ORDER BY create_time DESC
    </select>

    <!-- 根据负责人查询任务列表 -->
    <select id="findByOwner" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.TaskPO">
        SELECT * FROM tasks
        WHERE deleted = 0
          AND owner_open_id = #{ownerOpenId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据飞书任务ID查询任务 -->
    <select id="findByFeishuTaskId" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.TaskPO">
        SELECT * FROM tasks
        WHERE deleted = 0
          AND feishu_task_id = #{feishuTaskId}
        LIMIT 1
    </select>

</mapper>
