<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TeamInfoMapper">
    <resultMap id="BaseMap" type="cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamInfoPO">
        <id property="id" column="id"/>
        <result property="groupName" column="group_name"/>
        <result property="name" column="name"/>
        <result property="levelId" column="level_id"/>
        <result property="leader" column="leader"/>
        <result property="technicians" column="technicians" typeHandler="cn.genn.database.mybatisplus.typehandler.ListStringTypeHandler"/>
        <result property="members" column="members" typeHandler="cn.genn.database.mybatisplus.typehandler.ListStringTypeHandler"/>
        <result property="reviewers" column="reviewers" typeHandler="cn.genn.database.mybatisplus.typehandler.ListStringTypeHandler"/>
        <result property="deleted" column="deleted"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="updateUserName" column="update_user_name"/>
    </resultMap>

    <select id="TeamPage" resultMap="BaseMap">
        select * from team_info
        <where>
            <if test="query.levelId != null">
                and level_id = #{query.levelId}
            </if>
            <if test="query.groupName != null and query.groupName != ''">
                and group_name = #{query.groupName}
            </if>
            <if test="query.teamId != null">
                and id = #{query.teamId}
            </if>
            <if test="query.leader != null and query.leader != ''">
                and leader = #{query.leader}
            </if>
            <if test="query.member != null and query.member != ''">
                and members like CONCAT('%',#{query.member},'%')
            </if>
            <if test="query.reviewer != null and query.reviewer != ''">
                and reviewers like CONCAT('%',#{query.reviewer},'%')
            </if>
            and deleted = 0
        </where>
        order by id desc
    </select>

    <select id="hasCreateSchedule" resultType="boolean">
        select count(1) > 0 from team_info
        <where>
            (leader = #{openId}
            or technicians like CONCAT('%',#{openId},'%'))
            and deleted = 0
        </where>
    </select>

    <select id="selectByScheduleId" parameterType="long" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamInfoPO">
        select ti.* from team_info ti
        inner join schedule s on ti.id = s.team_id
        <where>
            s.id = #{scheduleId}
            and ti.deleted=0
        </where>
    </select>

</mapper>
