<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.pf.orch.meeting.infrastructure.repository.mapper.TeamLevelMapper">

    <select id="searchList" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamLevelPO">
        WITH RECURSIVE parent_chain AS (
            SELECT * FROM team_level
            WHERE name LIKE CONCAT('%', #{name}, '%') AND deleted = 0
            UNION ALL
            SELECT t.* FROM team_level t
                                INNER JOIN parent_chain p ON t.id = p.pid
            WHERE t.deleted = 0
        )
        SELECT * FROM parent_chain
        ORDER BY level
    </select>

    <select id="selectRecursionById" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.TeamLevelPO">
        WITH RECURSIVE parent_chain AS (
            SELECT * FROM team_level
            WHERE id in
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
              AND deleted = 0
            UNION ALL
            SELECT t.* FROM team_level t
                                INNER JOIN parent_chain p ON t.id = p.pid
            WHERE t.deleted = 0
        )
        SELECT distinct * FROM parent_chain
        ORDER BY level
    </select>
</mapper>
