<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.pf.orch.meeting.infrastructure.repository.mapper.ScheduleMapper">

    <insert id="insertAndGetId" useGeneratedKeys="true" keyProperty="id" parameterType="cn.genn.pf.orch.meeting.infrastructure.repository.po.SchedulePO">
        INSERT INTO schedule (team_id,
                              business_meeting_id,
                              schedule_name,
                              start_time,
                              end_time,
                              all_day,
                              fs_calendar_event_id,
                              fs_calendar_id,
                              fs_meeting_url,
                              create_user_id)
        VALUES (#{teamId},
                #{businessMeetingId},
                #{scheduleName},
                #{startTime},
                #{endTime},
                #{allDay},
                #{fsCalendarEventId},
                #{fsCalendarId},
                #{fsMeetingUrl},
                #{createUserId})
    </insert>

</mapper>
