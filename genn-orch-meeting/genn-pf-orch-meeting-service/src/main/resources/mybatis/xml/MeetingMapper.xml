<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingMapper">

    <!-- Define a result map for the MiniMeetingListDTO -->
    <resultMap id="MiniMeetingListDTOResultMap" type="cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingListDTO">
        <id column="meeting_id" property="id"/>
        <result column="schedule_id" property="scheduleId"/>
        <result column="name" property="name"/>
        <result column="meeting_url" property="meetingUrl"/>
        <result column="minute_url" property="minuteUrl"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="schedule_start_time" property="scheduleStartTime"/>
        <result column="schedule_end_time" property="scheduleEndTime"/>
        <result column="status" property="status"/>
        <result column="business_meeting_id" property="businessMeetingId"/>
        <result column="business_meeting_name" property="businessMeetingName"/>
        <result column="team_info" property="teamInfoDTO" typeHandler="cn.genn.database.mybatisplus.typehandler.JacksonTypeHandler" />
    </resultMap>

    <!-- Define the select statement for miniPage method -->
    <select id="miniPage" resultMap="MiniMeetingListDTOResultMap">
        SELECT
        m.id AS meeting_id,
        m.schedule_id,
        s.schedule_name AS name,
        m.meeting_url,
        m.minute_url,
        m.start_time AS start_time,
        m.end_time AS end_time,
        s.start_time AS schedule_start_time,
        s.end_time AS schedule_end_time,
        m.status,
        bm.id AS business_meeting_id,
        bm.name AS business_meeting_name,
        me.team_info
        FROM meeting m
        LEFT JOIN schedule s ON m.schedule_id = s.id
        left join team_info ti on s.team_id = ti.id
        LEFT JOIN business_meeting bm ON s.business_meeting_id = bm.id
        LEFT JOIN meeting_attend_user mau ON m.id = mau.meeting_id
        LEFT JOIN schedule_attend_user sau ON s.id = sau.schedule_id
        left join meeting_extend me on m.id = me.meeting_id
        WHERE m.deleted = 0
        <if test="meetingQuery.businessMeetingId != null">
            AND s.business_meeting_id = #{meetingQuery.businessMeetingId}
        </if>
        <if test="meetingQuery.statusList != null and meetingQuery.statusList.size() > 0">
            AND m.status IN
            <foreach item="item" index="index" collection="meetingQuery.statusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="meetingQuery.uploadRecordFlag != null">
            AND me.record
            <choose>
                <when test="meetingQuery.uploadRecordFlag != 0">
                    is not null
                </when>
                <otherwise>
                    is null
                </otherwise>
            </choose>
        </if>
        <if test="meetingQuery.uploadEvaluateFlag != null">
            AND me.evaluate
            <choose>
                <when test="meetingQuery.uploadEvaluateFlag != 0">
                    is not null
                </when>
                <otherwise>
                    is null
                </otherwise>
            </choose>
        </if>
        <if test="meetingQuery.currentUserId != null">
            AND (sau.attend_user_id = #{meetingQuery.currentUserId}
            OR JSON_CONTAINS(ti.reviewers, CONCAT('"', #{meetingQuery.currentUserId}, '"')) = 1)
        </if>
        <if test="meetingQuery.teamId != null">
            AND s.team_id = #{meetingQuery.teamId}
        </if>
        <if test="meetingQuery.levelIds != null and meetingQuery.levelIds.size() > 0">
            AND ti.level_id in
            <foreach collection="meetingQuery.levelIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="meetingQuery.groupName != null and meetingQuery.groupName != ''">
            AND ti.group_name like concat('%',#{meetingQuery.groupName},'%')
        </if>
        <if test="meetingQuery.endTimeLeft != null">
            AND m.end_time &gt;= #{meetingQuery.endTimeLeft}
        </if>
        <if test="meetingQuery.endTimeRight != null">
            AND m.end_time &lt;= #{meetingQuery.endTimeRight}
        </if>
        <if test="meetingQuery.attendUserName != null and meetingQuery.attendUserName != ''">
            AND sau.attend_user_name like concat('%',#{meetingQuery.attendUserName},'%')
        </if>
        GROUP BY m.id
        ORDER BY  m.create_time DESC
    </select>

    <select id="queryValidCheckInNumbers" resultType="java.lang.String">
        select check_in_number from meeting where deleted = 0 and status != 2
    </select>

    <select id="queryNoStartStartMeeting" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPO">
        select m.* from meeting m
        inner join `schedule` s on m.schedule_id = s.id
        <where>
            m.status = 0
            and s.start_time &gt;= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            and s.start_time &lt;= DATE_ADD(NOW(), INTERVAL 30 MINUTE)
            and m.deleted = 0 and s.deleted = 0
        </where>
    </select>

    <select id="queryInProcessMeeting" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingPO">
        select m.* from meeting m
        inner join `schedule` s on m.schedule_id = s.id
        <where>
            m.status = 1
            and m.deleted = 0 and s.deleted = 0
        </where>
    </select>
</mapper>
