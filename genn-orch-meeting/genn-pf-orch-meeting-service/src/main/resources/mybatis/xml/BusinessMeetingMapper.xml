<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.pf.orch.meeting.infrastructure.repository.mapper.BusinessMeetingMapper">

    <insert id="insertAndGetId" useGeneratedKeys="true" keyProperty="id" parameterType="cn.genn.pf.orch.meeting.infrastructure.repository.po.BusinessMeetingPO">
        INSERT INTO business_meeting (name, top_business) VALUES (#{name}, #{topBusiness})
    </insert>
</mapper>
