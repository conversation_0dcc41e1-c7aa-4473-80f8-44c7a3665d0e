<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.genn.pf.orch.meeting.infrastructure.repository.mapper.MeetingEvaluationMapper">

    <select id="selectByMeetingId" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingEvaluationPO">
        SELECT * FROM meeting_evaluation 
        WHERE meeting_id = #{meetingId} AND deleted = 0
        ORDER BY evaluation_time DESC
    </select>

    <select id="selectByMeetingIdAndEvaluator" resultType="cn.genn.pf.orch.meeting.infrastructure.repository.po.MeetingEvaluationPO">
        SELECT * FROM meeting_evaluation 
        WHERE meeting_id = #{meetingId} 
        AND evaluator_open_id = #{evaluatorOpenId} 
        AND deleted = 0
        LIMIT 1
    </select>

</mapper> 