server:
  servlet:
    context-path: /api/meeting
spring:
  profiles:
    active: @spring.profiles.active@
  application:
    name: genn-pf-orch-meeting
  servlet:
    multipart:
      resolve-lazily: true # multipart 懒加载
      max-file-size: 100MB
      max-request-size: 100MB
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  mapper-locations: classpath:mybatis/xml/*Mapper.xml
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

knife4j:
  enable: true
  openapi:
    title: genn-pf-orch-meeting
    description: 会议管理系统
    concat: zhangzhiwei
    version: 1.0.0

genn:
  database:
    mybatis-plus:
      enable-tenant-line: false
  spring:
    web:
      exception:
        # 需要自定义修改
        product-code: "05"
        service-code: "34"
      access-log:
        enabled: true
        global: true
      request-context-type: cn.genn.pf.orch.meeting.infrastructure.config.GennRequestContext
