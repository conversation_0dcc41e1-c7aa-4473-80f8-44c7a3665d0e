server:
  port: 8100

knife4j:
  enable: false
spring:
  redis:
    redisson:
      config: |
        {
          "singleServerConfig": {
            "address": "redis://redis-cnlf9o24nak4zj5lz.redis.ivolces.com:6379",
            "password": "ZJny&2cs&buuro8215",
            "connectionPoolSize": 50,
            "subscriptionConnectionMinimumIdleSize": 10,
            "subscriptionConnectionPoolSize": 50,
            "connectionMinimumIdleSize": 10,
            "idleConnectionTimeout": 10000,
            "connectTimeout": 10000,
            "timeout": 3000,
            "retryAttempts": 3,
            "retryInterval": 1500,
            "database": 21
          }
        }
genn:
  database:
    multi:
      db:
        genn_orch_meeting:
          primary: true
          master:
            jdbcUrl: ***************************************************************************************************************************************************************************
            username: app
            password: ap(23g73T1#12G1
            driverClassName: com.mysql.cj.jdbc.Driver
            connectionTimeout: 10000
            minimumIdle: 2
            maximumPoolSize: 10
  third:
    feishu:
      enabled: true
      mini:
        config:
          - code: pf_meeting
            appId: cli_a705bc328c38901c
            appSecret: ylHEgPrMV9rk7xyfyCHrDburwoQZDuid
  meeting:
    permission:
      excludePatterns:
        - /user/getUserInfo
        - /app/**
    cardSend:
      template:
        signInCodeId: AAqFuE6ZgM1yF
        signInRemindId: AAqFuEuz6wdjU
        uploadMeetingRecordId: AAqFuEfKrht86
        meetingEvaluationId: AAqFuErHXoEQH
        shareMeetingVideoId: AAqFuEYRLokMb
        meetingNotificationId: AAqFuE6ZgM1yF
      jumpUrl:
        signInUrl: https://meeting-mini.gennergy.com/meet/meetingSign
        uploadUrl: https://meeting-mini.gennergy.com/meet/uploadRecord
        evaluationUrl: https://meeting-mini.gennergy.com/meet/uploadEvaluate
        meetingDetailUrl: https://meeting-mini.gennergy.com/meet/meetingDetail
dromara:
  x-file-storage: #文件存储配置
    default-platform: amazon-s3-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg"
    amazon-s3: # 0.0.7 及以前的版本，配置名称是：aws-s3
      - platform: amazon-s3-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: AKLTNDA5ODZkYjcwYTNjNDllNzk2N2FkN2JjMjI5ZGNiZWQ
        secret-key: T0dRM1l6SXpOalV6WkRBNE5HRmhZMkpqWmpVMU1HSXhZMlE1WkdaaE9UUQ==
        end-point: tos-s3-cn-beijing.volces.com
        bucket-name: pro-pf-meeting
        domain: pro-pf-meeting.tos-cn-beijing.volces.com
        base-path: ${spring.application.name}/

