-- 会议评价表
CREATE TABLE `meeting_evaluation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `meeting_id` bigint(20) NOT NULL COMMENT '会议ID',
  `evaluator_open_id` varchar(64) NOT NULL COMMENT '评价人OpenID',
  `evaluator_name` varchar(100) NOT NULL COMMENT '评价人姓名',
  `meeting_score` int(11) NOT NULL COMMENT '会议评分(1-5)',
  `content_score` int(11) NOT NULL COMMENT '内容评分(1-5)',
  `duration_score` int(11) NOT NULL COMMENT '时长评分(1-5)',
  `effectiveness_score` int(11) NOT NULL COMMENT '效果评分(1-5)',
  `suggestions` text COMMENT '改进建议',
  `evaluation_time` datetime NOT NULL COMMENT '评价时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_meeting_evaluator` (`meeting_id`,`evaluator_open_id`),
  KEY `idx_meeting_id` (`meeting_id`),
  KEY `idx_evaluator_open_id` (`evaluator_open_id`),
  KEY `idx_evaluation_time` (`evaluation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议评价表'; 