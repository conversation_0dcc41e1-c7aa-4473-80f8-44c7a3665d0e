-- 新会议管理功能数据表
-- 创建时间：2025-01-24

-- 新会议表
CREATE TABLE `new_meeting` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `meeting_name` VARCHAR(200) NOT NULL COMMENT '会议名称',
  `meeting_description` TEXT COMMENT '会议描述',
  `meeting_plan_id` BIGINT DEFAULT NULL COMMENT '会议规划ID（可为空，临时会议为NULL）',
  `meeting_standard_id` BIGINT DEFAULT NULL COMMENT '会议标准ID（可为空，临时会议为NULL）',
  `meeting_no` VARCHAR(100) DEFAULT NULL COMMENT '会议编号',
  `start_time` DATETIME NOT NULL COMMENT '会议开始时间',
  `end_time` DATETIME NOT NULL COMMENT '会议结束时间',
  `status` TINYINT NOT NULL COMMENT '会议状态 0-未开始 1-进行中 2-已结束 3-已取消',
  `priority_level` TINYINT DEFAULT NULL COMMENT '优先级',
  `meeting_location` VARCHAR(200) DEFAULT NULL COMMENT '会议地点',
  `attendees` JSON DEFAULT NULL COMMENT '参会人员列表（用户ID列表）',
  `host_user_id` VARCHAR(50) DEFAULT NULL COMMENT '主持人ID',
  `recorder_user_id` VARCHAR(50) DEFAULT NULL COMMENT '记录员ID',
  `fs_calendar_event_id` VARCHAR(128) DEFAULT NULL COMMENT '飞书日程事件ID',
  `fs_meeting_id` VARCHAR(128) DEFAULT NULL COMMENT '飞书会议ID',
  `meeting_url` VARCHAR(500) DEFAULT NULL COMMENT '会议链接',
  `minute_url` VARCHAR(500) DEFAULT NULL COMMENT '妙计链接',
  `create_user_id` VARCHAR(50) NOT NULL COMMENT '创建人ID',
  `create_user_name` VARCHAR(100) DEFAULT NULL COMMENT '创建人姓名',
  `create_time` DATETIME NOT NULL COMMENT '创建时间',
  `update_user_id` VARCHAR(50) DEFAULT NULL COMMENT '更新人ID',
  `update_user_name` VARCHAR(100) DEFAULT NULL COMMENT '更新人姓名',
  `update_time` DATETIME NOT NULL COMMENT '更新时间',
  `deleted` TINYINT NOT NULL DEFAULT '0' COMMENT '删除标记',
  PRIMARY KEY (`id`),
  KEY `idx_plan` (`meeting_plan_id`),
  KEY `idx_standard` (`meeting_standard_id`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_create_user` (`create_user_id`),
  KEY `idx_fs_calendar_event` (`fs_calendar_event_id`),
  KEY `idx_fs_meeting` (`fs_meeting_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新会议表'; 