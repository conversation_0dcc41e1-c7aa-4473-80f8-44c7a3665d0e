-- 创建会议标准表
CREATE TABLE `meeting_standard` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `standard_name` varchar(100) NOT NULL COMMENT '标准名称',
  `description` varchar(500) DEFAULT NULL COMMENT '标准描述',
  `default_duration` int DEFAULT NULL COMMENT '默认持续时长(分钟)',
  `advance_notice_value` int DEFAULT NULL COMMENT '默认提前通知数值',
  `advance_notice_unit` varchar(20) DEFAULT NULL COMMENT '默认提前通知时间单位',
  `priority_level` tinyint DEFAULT NULL COMMENT '默认优先级',
  `default_location` varchar(200) DEFAULT NULL COMMENT '默认会议地点',
  `late_tolerance_minutes` int DEFAULT NULL COMMENT '迟到容许时间(分钟)',
  `required_roles` varchar(500) DEFAULT NULL COMMENT '人员必要角色列表(逗号分隔的code)',
  `min_attendees` int DEFAULT NULL COMMENT '最少参会人数',
  `max_attendees` int DEFAULT NULL COMMENT '最多参会人数',
  `meeting_points` json DEFAULT NULL COMMENT '会议要点列表',
  `is_enabled` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用(0-否,1-是)',
  `create_user_id` varchar(50) NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(100) DEFAULT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user_id` varchar(50) DEFAULT NULL COMMENT '更新人ID',
  `update_user_name` varchar(100) DEFAULT NULL COMMENT '更新人姓名',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记(0-未删除,1-已删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_standard_name` (`standard_name`,`deleted`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_create_user` (`create_user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会议标准表';

-- 会议规划表
CREATE TABLE `meeting_plan` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_name` varchar(200) NOT NULL COMMENT '会议规划名称',
  `plan_description` text COMMENT '会议规划描述/备注',
  `planned_start_time` datetime NOT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime NOT NULL COMMENT '计划结束时间',
  `planned_duration` int DEFAULT NULL COMMENT '计划持续时长(分钟)',
  `status` tinyint NOT NULL COMMENT '会议规划状态(0-未开始,1-进行中,2-已完成,3-已逾期)',
  `meeting_standard_id` bigint NOT NULL COMMENT '会议标准ID',
  `priority_level` tinyint DEFAULT NULL COMMENT '优先级',
  `department_id` bigint DEFAULT NULL COMMENT '部门ID',
  `department_name` varchar(100) DEFAULT NULL COMMENT '部门名称',
  `business_meeting_id` bigint DEFAULT NULL COMMENT '业务会议ID',
  `attendees` json DEFAULT NULL COMMENT '参会人员列表',
  `meeting_location` varchar(200) DEFAULT NULL COMMENT '会议地点',
  `create_user_id` varchar(50) NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(100) DEFAULT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_user_id` varchar(50) DEFAULT NULL COMMENT '更新人ID',
  `update_user_name` varchar(100) DEFAULT NULL COMMENT '更新人姓名',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记(0-未删除,1-已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_status_start_time` (`status`, `planned_start_time`),
  KEY `idx_meeting_standard` (`meeting_standard_id`),
  KEY `idx_department` (`department_id`),
  KEY `idx_business_meeting` (`business_meeting_id`),
  KEY `idx_create_user` (`create_user_id`),
  KEY `idx_location_time` (`meeting_location`, `planned_start_time`, `planned_end_time`),
  KEY `idx_conflict_check` (`status`, `planned_start_time`, `planned_end_time`, `deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议规划表';

-- 插入默认会议标准数据
INSERT INTO `meeting_standard` (`standard_name`, `description`, `default_duration`, `advance_notice_value`, `advance_notice_unit`, `priority_level`, `default_location`, `late_tolerance_minutes`, `required_roles`, `min_attendees`, `max_attendees`, `meeting_points`, `is_enabled`, `create_user_id`, `create_user_name`, `create_time`, `update_user_id`, `update_user_name`, `update_time`, `deleted`) VALUES
('部门例会', '每周部门例会标准', 120, 1, 'DAY', 2, '会议室A', 15, 'HOST,RECORDER', 3, 15, '["上周工作总结", "本周工作计划", "问题讨论", "部门通知"]', 1, 'system', '系统管理员', NOW(), 'system', '系统管理员', NOW(), 0),
('项目会议', '项目相关会议标准', 180, 2, 'DAY', 3, '项目室', 10, 'PROJECT_MANAGER,TECH_LEADER,RECORDER', 5, 25, '["项目进度汇报", "技术方案讨论", "风险评估", "下阶段计划", "决策事项"]', 1, 'system', '系统管理员', NOW(), 'system', '系统管理员', NOW(), 0),
('培训会议', '培训相关会议标准', 240, 7, 'DAY', 2, '培训室', 5, 'TRAINER,HOST', 2, 50, '["培训内容介绍", "知识讲解", "互动讨论", "总结回顾"]', 1, 'system', '系统管理员', NOW(), 'system', '系统管理员', NOW(), 0),
('评审会议', '评审相关会议标准', 150, 5, 'DAY', 3, '评审室', 0, 'REVIEWER,HOST,RECORDER', 3, 20, '["评审材料介绍", "专家评审", "问题讨论", "评审结论", "决策事项"]', 1, 'system', '系统管理员', NOW(), 'system', '系统管理员', NOW(), 0),
('临时会议', '临时紧急会议标准', 60, 0, 'MINUTE', 4, '临时会议室', 5, 'HOST', 2, 10, '["紧急事项说明", "问题讨论", "解决方案", "行动项确认"]', 1, 'system', '系统管理员', NOW(), 'system', '系统管理员', NOW(), 0);
