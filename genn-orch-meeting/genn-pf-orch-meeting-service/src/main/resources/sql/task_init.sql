-- 创建任务表
CREATE TABLE `tasks` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `feishu_task_id` varchar(100) DEFAULT NULL COMMENT '对应的飞书任务ID，用于API同步',
  `title` varchar(200) NOT NULL COMMENT '任务标题',
  `description` text COMMENT '任务描述',
  `owner_open_id` varchar(50) NOT NULL COMMENT '负责人open_id',
  `owner_name` varchar(100) NOT NULL COMMENT '负责人名称',
  `priority` tinyint NOT NULL DEFAULT '1' COMMENT '优先级 (0: 低, 1: 中, 2: 高)',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '任务状态（0:未开始；1:进行中；2:已完成；3:已超期）',
  `due_date` datetime DEFAULT NULL COMMENT '截止时间',
  `completed_at` datetime DEFAULT NULL COMMENT '实际完成时间',
  `meeting_id` bigint DEFAULT NULL COMMENT '关联的会议ID (关联 meeting.id)',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记(0-未删除,1-已删除)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user_id` varchar(50) NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(100) DEFAULT NULL COMMENT '创建人姓名',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_user_id` varchar(50) DEFAULT NULL COMMENT '更新人ID',
  `update_user_name` varchar(100) DEFAULT NULL COMMENT '更新人姓名',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_feishu_task_id` (`feishu_task_id`),
  KEY `idx_owner_open_id` (`owner_open_id`),
  KEY `idx_meeting_id` (`meeting_id`),
  KEY `idx_status` (`status`),
  KEY `idx_due_date` (`due_date`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_create_user` (`create_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务信息表'; 