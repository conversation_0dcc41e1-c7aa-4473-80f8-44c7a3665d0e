server:
  port: 8100

knife4j:
  enable: true
spring:
  redis:
    redisson:
      config: |
        {
          "singleServerConfig": {
            "address": "redis://redis-cnlf7qrmokj478dgz.redis.ivolces.com:6379",
            "password": "ZJeb#qw&sit@ops12",
            "connectionPoolSize": 50,
            "subscriptionConnectionMinimumIdleSize": 10,
            "subscriptionConnectionPoolSize": 50,
            "connectionMinimumIdleSize": 10,
            "idleConnectionTimeout": 10000,
            "connectTimeout": 10000,
            "timeout": 3000,
            "retryAttempts": 3,
            "retryInterval": 1500,
            "database": 0
          }
        }
genn:
  database:
    multi:
      db:
        genn_orch_meeting:
          primary: true
          master:
            jdbcUrl: ***************************************************************************************************************************************************************************
            username: zhangzhiwei
            password: sit&102zhangzhiwei!
            driverClassName: com.mysql.cj.jdbc.Driver
            connectionTimeout: 10000
            minimumIdle: 2
            maximumPoolSize: 10
  third:
    feishu:
      enabled: true
      mini:
        config:
          - code: pf_meeting
            appId: cli_a808965153f95013
            appSecret: xrOQJck37DRjTYvjyN5moR7glft5yaXD
  meeting:
#    feishuCalendarId: <EMAIL>
    agent:
      invokeDomain: https://cerebro.gennergy.com/
    permission:
      excludePatterns:
        - /user/getUserInfo
        - /file/**
        - /app/**
    cardSend:
      template:
        signInCodeId: AAqS4YKP6njYf
        signInRemindId: AAqS4w5zDBEvL
        uploadMeetingRecordId: AAqS4Vw1e3Eda
        meetingEvaluationId: AAqS4VtjI0ZrW
        shareMeetingVideoId: AAqS4CJzei6jk
        meetingNotificationId: AAqS4YKP6njYf
      jumpUrl:
        signInUrl: https://meeting-sit-mini.genn.cn/meet/meetingSign
        uploadUrl: https://meeting-sit-mini.genn.cn/meet/uploadRecord
        evaluationUrl: https://meeting-sit-mini.genn.cn/meet/uploadEvaluate
        meetingDetailUrl: https://meeting-sit-mini.genn.cn/meet/meetingDetail
dromara:
  x-file-storage: #文件存储配置
#    default-platform: huawei-obs-1 #默认使用的存储平台
    thumbnail-suffix: ".min.jpg"
#    huawei-obs:
#      - platform: huawei-obs-1 # 存储平台标识
#        enable-storage: true  # 启用存储
#        access-key: ERMZYOVKRBTVCAIBTIJ2
#        secret-key: phJPIFwjjdnFnjWCOG5mihaMhLBsMUnlneQED4mE
#        end-point: obs.cn-north-4.myhuaweicloud.com
#        bucket-name: test-kangjian
#        domain: https://test-kangjian.obs.cn-north-4.myhuaweicloud.com:443/ # 访问域名，注意“/”结尾，例如：http://abc.obs.com/
#        base-path: ${spring.application.name}/
    default-platform: amazon-s3-1
    amazon-s3: # 0.0.7 及以前的版本，配置名称是：aws-s3
      - platform: amazon-s3-1 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: AKLTNDA5ODZkYjcwYTNjNDllNzk2N2FkN2JjMjI5ZGNiZWQ
        secret-key: T0dRM1l6SXpOalV6WkRBNE5HRmhZMkpqWmpVMU1HSXhZMlE1WkdaaE9UUQ==
        end-point: tos-s3-cn-beijing.volces.com
        bucket-name: pro-pf-meeting
        domain: pro-pf-meeting.tos-cn-beijing.volces.com
        base-path: ${spring.application.name}/
