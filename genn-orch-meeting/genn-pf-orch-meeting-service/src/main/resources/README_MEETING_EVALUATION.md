# 会议评价功能使用说明

## 功能概述

会议评价功能允许在会议结束后自动向所有参会人员发送评价调查卡片，收集用户对会议的评价反馈。

## 数据库表

### meeting_evaluation 表
```sql
-- 执行以下SQL创建表
-- 文件位置: src/main/resources/sql/meeting_evaluation_init.sql
```

## 功能特性

1. **自动发送评价卡片**：会议结束后自动向参会人员发送评价调查卡片
2. **5分制评分**：包含4个维度的评分（会议整体、内容、时长、效果）
3. **改进建议**：支持文本输入，最多500字
4. **防重复评价**：每个参会人员只能评价一次
5. **数据统计**：支持评价数据的统计分析

## 评价维度

1. **会议整体评分**：1-5分（非常不满意到非常满意）
2. **会议内容评分**：1-5分（非常不满意到非常满意）
3. **会议时长评分**：1-5分（太短到太长）
4. **会议效果评分**：1-5分（未达到预期到超出预期）
5. **改进建议**：文本输入，最多500字

## 测试接口

### 1. 测试发送评价卡片
```bash
POST /test/evaluation/sendCard
Content-Type: application/json

{
  "meetingId": 123,
  "meetingName": "产品需求评审会议",
  "meetingType": "产品会议",
  "meetingTime": "2025-01-24 14:00-16:00",
  "openId": "ou_123456789",
  "evaluatorName": "张三"
}
```

### 2. 测试提交评价
```bash
POST /test/evaluation/submitEvaluation
Content-Type: application/json

{
  "meetingId": 123,
  "evaluatorOpenId": "ou_123456789",
  "evaluatorName": "张三",
  "evaluationDTO": {
    "meetingScore": 4,
    "contentScore": 5,
    "durationScore": 3,
    "effectivenessScore": 4,
    "suggestions": "会议内容很充实，但时间安排可以更紧凑一些。建议下次会议可以提前准备更详细的议程。"
  }
}
```

## 测试数据

测试用的JSON数据文件位置：`src/main/resources/test-data/meeting_evaluation_test.json`

## 卡片模板

飞书卡片模板包含：
- 会议基本信息（名称、类型、时间、评价人）
- 4个评分维度（下拉选择）
- 改进建议（文本输入）
- 提交按钮

## 注意事项

1. 需要先执行数据库表创建脚本
2. 测试时需要替换真实的飞书OpenID
3. 会议结束后发送评价卡片的逻辑已集成到MeetingEndHandler中
4. 评价提交通过飞书回调处理

## 文件结构

```
src/main/java/cn/genn/pf/orch/meeting/
├── application/
│   ├── dto/
│   │   ├── MeetingEvaluationDTO.java
│   │   ├── MeetingEvaluationSubmitCommand.java
│   │   └── card/
│   │       └── SendMeetingEvaluationSurveyDTO.java
│   └── service/
│       └── action/
│           └── MeetingEvaluationActionService.java
├── domain/
│   └── meeting/
│       ├── model/
│       │   ├── entity/
│       │   │   └── MeetingEvaluationInfo.java
│       │   └── aggregates/
│       │       └── MeetingEvaluationAgg.java
│       ├── repository/
│       │   └── IMeetingEvaluationRepository.java
│       └── service/
│           ├── MeetingEvaluationDomainService.java
│           └── impl/
│               └── MeetingEvaluationDomainServiceImpl.java
├── infrastructure/
│   └── repository/
│       ├── mapper/
│       │   └── MeetingEvaluationMapper.java
│       ├── po/
│       │   └── MeetingEvaluationPO.java
│       └── persistence/
│           └── MeetingEvaluationRepositoryImpl.java
└── interfaces/
    ├── command/
    │   └── MeetingEvaluationSubmitEventCommand.java
    ├── dto/
    │   └── meeting/
    │       └── MeetingEvaluationStatisticsDTO.java
    ├── handler/
    │   └── MeetingEvaluationSubmitHandler.java
    └── MeetingEvaluationController.java
```

## 配置说明

1. 确保飞书应用已配置消息接收权限
2. 确保回调地址已正确配置
3. 确保数据库连接正常

## 扩展功能

后续可以扩展的功能：
1. 评价数据统计分析
2. 评价报告生成
3. 评价趋势分析
4. 会议质量评估 