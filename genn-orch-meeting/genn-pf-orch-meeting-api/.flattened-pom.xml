<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.genn.app</groupId>
    <artifactId>genn-pf-orch-meeting</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>cn.genn.app</groupId>
  <artifactId>genn-pf-orch-meeting-api</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <genn-pf-orch-meeting-api.version>1.0.0-SNAPSHOT</genn-pf-orch-meeting-api.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>cn.genn</groupId>
      <artifactId>genn-core</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>cn.genn.boot</groupId>
      <artifactId>genn-spring-boot-starter-web</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <optional>true</optional>
    </dependency>
  </dependencies>
  <profiles>
    <profile>
      <id>pro</id>
      <properties>
        <genn-pf-orch-meeting-api.version>1.0.2-RELEASE</genn-pf-orch-meeting-api.version>
      </properties>
    </profile>
    <profile>
      <id>dev</id>
      <properties>
        <genn-pf-orch-meeting-api.version>${genn-service-api.version}</genn-pf-orch-meeting-api.version>
      </properties>
    </profile>
    <profile>
      <id>test</id>
      <properties>
        <genn-pf-orch-meeting-api.version>${genn-service-api.version}</genn-pf-orch-meeting-api.version>
      </properties>
    </profile>
  </profiles>
</project>
