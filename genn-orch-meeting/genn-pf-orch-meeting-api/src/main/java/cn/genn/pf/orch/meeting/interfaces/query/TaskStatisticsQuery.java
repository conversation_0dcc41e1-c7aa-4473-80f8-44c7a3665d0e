package cn.genn.pf.orch.meeting.interfaces.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @description 任务统计查询对象
 * @date 2025-01-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskStatisticsQuery {

    @ApiModelProperty(value = "用户ID（可选，不填则查询所有用户的数据）")
    private String userId;
}
