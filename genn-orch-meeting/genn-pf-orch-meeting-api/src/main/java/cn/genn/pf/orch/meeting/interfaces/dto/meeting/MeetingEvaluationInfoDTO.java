package cn.genn.pf.orch.meeting.interfaces.dto.meeting;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议评价信息DTO
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingEvaluationInfoDTO {
    
    @ApiModelProperty("评价ID")
    private Long id;
    
    @ApiModelProperty("会议ID")
    private Long meetingId;
    
    @ApiModelProperty("评价人OpenID")
    private String evaluatorOpenId;
    
    @ApiModelProperty("评价人姓名")
    private String evaluatorName;
    
    @ApiModelProperty("会议整体评分(1-5分)")
    private Integer meetingScore;
    
    @ApiModelProperty("会议内容评分(1-5分)")
    private Integer contentScore;
    
    @ApiModelProperty("会议时长评分(1-5分)")
    private Integer durationScore;
    
    @ApiModelProperty("会议效果评分(1-5分)")
    private Integer effectivenessScore;
    
    @ApiModelProperty("建议")
    private String suggestions;
    
    @ApiModelProperty("评价时间")
    private LocalDateTime evaluationTime;
    
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
} 