package cn.genn.pf.orch.meeting.interfaces.dto.meeting;

import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.CheckInStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 会议参会人对象
 * @date 2025-01-03
 */
@Data
public class MeetingAttendUserDTO {

    @ApiModelProperty(value = "参会人员角色, 0:CREATOR-创建人, 1:NORMAL-普通, 2:RECORDER-记录员 3:EVALUATOR-评价员")
    private AttendUserRoleEnum attendUserRole;

    @ApiModelProperty(value = "参会人员状态, 1:EXPECT-预期参会, 2:ABSENT-请假缺席")
    private AttendUserStateEnum attendUserState;

    @ApiModelProperty(value = "请假原因，仅当参会人员类别为ABSENT时有效")
    private String absentReason;

    @ApiModelProperty(value = "参会人id")
    private String attendUserId;

    @ApiModelProperty(value = "参会人姓名")
    private String attendUserName;

    @ApiModelProperty(value = "第一次入会时间")
    private LocalDateTime firstJoinTime;

    @ApiModelProperty(value = "最终离会时间")
    private LocalDateTime finalLeaveTime;

    @ApiModelProperty(value = "累计在会中时间")
    private String inMeetingDuration;

    @ApiModelProperty(value = "签到状态 0-未签到, 1-已签到")
    private CheckInStatusEnum checkInStatus;

    @ApiModelProperty(value = "签到地点")
    private String checkInAddress;

    @ApiModelProperty(value = "签到时间")
    private LocalDateTime checkInTime;
}
