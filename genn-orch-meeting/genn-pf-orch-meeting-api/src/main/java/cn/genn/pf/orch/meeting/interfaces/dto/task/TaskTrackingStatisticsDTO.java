package cn.genn.pf.orch.meeting.interfaces.dto.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 任务追踪统计数据传输对象
 * @date 2025-01-28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("任务追踪统计数据")
public class TaskTrackingStatisticsDTO {

    @ApiModelProperty("未开始任务数")
    private Integer notStartedCount;

    @ApiModelProperty("进行中任务数")
    private Integer inProgressCount;

    @ApiModelProperty("已完成任务数")
    private Integer completedCount;

    @ApiModelProperty("已超期任务数")
    private Integer overdueCount;

    @ApiModelProperty("总任务数")
    private Integer totalCount;
}
