package cn.genn.pf.orch.meeting.interfaces.dto.team;

import cn.genn.core.model.enums.DeletedEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeamLevelTreeDTO{

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "上级id（为0表示没有上级）")
    private Long pid;

    @ApiModelProperty(value = "当前层级（从1开始）")
    private Integer level;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "会议完成后发送人员")
    private List<String> finishNotify;

    @ApiModelProperty(value = "删除标记（0未删除）")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "飞书用户openId")
    private String createUserId;

    @ApiModelProperty(value = "飞书用户名")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "飞书用户openId")
    private String updateUserId;

    @ApiModelProperty(value = "飞书用户名")
    private String updateUserName;


    private List<TeamLevelTreeDTO> children;
}
