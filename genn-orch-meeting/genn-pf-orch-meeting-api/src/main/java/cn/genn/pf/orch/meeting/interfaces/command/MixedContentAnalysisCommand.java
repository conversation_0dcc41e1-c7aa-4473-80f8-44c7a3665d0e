package cn.genn.pf.orch.meeting.interfaces.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 混合内容分析命令（支持文本+图片+文件的组合分析）
 */
@Data
@ApiModel("混合内容分析命令")
public class MixedContentAnalysisCommand {

    @ApiModelProperty(value = "文本消息")
    private String textMessage;

    @ApiModelProperty(value = "图片链接列表")
    private List<String> imageUrls;

    @ApiModelProperty(value = "文件链接")
    private String fileUrl;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "智能体应用ID")
    private String appId;
}
