package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 新会议状态枚举
 * @date 2025-01-24
 */
@Getter
@AllArgsConstructor
public enum NewMeetingStatusEnum {

    NOT_STARTED(0, "未开始"),
    IN_PROCESS(1, "进行中"),
    ENDED(2, "已结束"),
    CANCELLED(3, "已取消"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, NewMeetingStatusEnum> VALUES = new HashMap<>();

    static {
        for (final NewMeetingStatusEnum item : NewMeetingStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static NewMeetingStatusEnum of(int code) {
        return VALUES.get(code);
    }
} 