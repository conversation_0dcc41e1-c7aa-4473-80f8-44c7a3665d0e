package cn.genn.pf.orch.meeting.interfaces.dto.task;

import cn.genn.pf.orch.meeting.interfaces.enums.TaskPriorityEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.TaskStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务DTO
 */
@Data
@ApiModel("任务信息")
public class TaskDTO {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("飞书任务ID")
    private String feishuTaskId;

    @ApiModelProperty("任务标题")
    private String title;

    @ApiModelProperty("任务描述")
    private String description;

    @ApiModelProperty("负责人OpenID")
    private String ownerOpenId;

    @ApiModelProperty("负责人名称")
    private String ownerName;

    @ApiModelProperty("负责人头像")
    private String ownerAvatarUrl;

    @ApiModelProperty("优先级")
    private TaskPriorityEnum priority;

    @ApiModelProperty("任务状态")
    private TaskStatusEnum status;

    @ApiModelProperty("截止时间")
    private LocalDateTime dueDate;

    @ApiModelProperty("实际完成时间")
    private LocalDateTime completedAt;

    @ApiModelProperty("关联的会议ID")
    private Long meetingId;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("创建用户ID")
    private String createUserId;

    @ApiModelProperty("创建用户名")
    private String createUserName;

    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("更新用户ID")
    private String updateUserId;

    @ApiModelProperty("更新用户名")
    private String updateUserName;
}
