package cn.genn.pf.orch.meeting.interfaces.command;

import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * AttendUser
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendUserCommand {

    @ApiModelProperty(value = "参会人id")
    @NotNull(message = "参会人id不能为空")
    private String attendUserId;

    @ApiModelProperty(value = "参会人姓名")
    @NotNull(message = "参会人姓名不能为空")
    private String attendUserName;

    @ApiModelProperty(value = "参会人员状态, 1:EXPECT-预期参会, 2:ABSENT-请假缺席")
    @NotNull(message = "参会人员状态不能为空")
    private AttendUserStateEnum attendUserState;

    @ApiModelProperty(value = "请假原因，仅当参会人员类别为ABSENT时有效")
    private String absentReason;

    @ApiModelProperty(value = "参会人员角色, 1:NORMAL-普通, 2:RECORDER-记录员")
    @NotNull(message = "参会人员角色不能为空")
    private AttendUserRoleEnum attendUserRole;
}
