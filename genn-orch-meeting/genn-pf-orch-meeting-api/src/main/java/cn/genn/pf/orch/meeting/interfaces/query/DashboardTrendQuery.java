package cn.genn.pf.orch.meeting.interfaces.query;

import cn.genn.pf.orch.meeting.interfaces.enums.TimeUnitEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @description 仪表板趋势分析查询对象
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DashboardTrendQuery {

    @ApiModelProperty("时间单位: WEEK|MONTH|YEAR")
    @NotNull(message = "时间单位不能为空")
    private TimeUnitEnum timeUnit;

    @ApiModelProperty("开始日期")
    private LocalDate startDate;

    @ApiModelProperty("结束日期")
    private LocalDate endDate;
}
