package cn.genn.pf.orch.meeting.interfaces.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ScheduleCommand
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleCommand {

    @ApiModelProperty(value = "班组id")
    @NotNull(message = "班组id不能为空")
    private Long teamId;

    @ApiModelProperty(value = "业务会议id")
    @NotNull(message = "业务会议id不能为空")
    private Long businessMeetingId;

    @ApiModelProperty(value = "日程名称前缀")
    private String scheduleNamePrefix;

    @ApiModelProperty(value = "会议开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "会议结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
    @ApiModelProperty(value = "会议全天")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime allDay;

    @ApiModelProperty(value = "参会人列表")
    @NotNull(message = "参会人列表不能为空")
    private List<AttendUserCommand> attendUsers;
}
