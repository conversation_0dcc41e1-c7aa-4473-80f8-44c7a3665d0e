package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 参会状态枚举
 * @date 2024-12-31
 */
@Getter
@AllArgsConstructor
public enum AttendStatusEnum {

    UN_ATTEND(10, "未参加"),
    ATTEND(20, "已参加"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String desc;

    private static final Map<Integer, AttendStatusEnum> VALUES = new HashMap<>();

    static {
        for (final AttendStatusEnum item : AttendStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static AttendStatusEnum of(int code) {
        return VALUES.get(code);
    }
}
