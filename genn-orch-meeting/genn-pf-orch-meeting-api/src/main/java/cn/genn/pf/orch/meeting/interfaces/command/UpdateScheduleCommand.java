package cn.genn.pf.orch.meeting.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * UpdateScheduleCommand
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateScheduleCommand extends ScheduleCommand {

    @ApiModelProperty(value = "日程id")
    @NotNull(message = "日程id不能为空")
    private Long scheduleId;
}
