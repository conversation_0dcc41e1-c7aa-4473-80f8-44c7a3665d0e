package cn.genn.pf.orch.meeting.interfaces.dto.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * BusinessMeetingDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SingleBusinessMeetingDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "业务名")
    private String name;
}
