package cn.genn.pf.orch.meeting.interfaces.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description 会议角色枚举
 * @date 2025-01-24
 */
@Getter
@AllArgsConstructor
public enum MeetingRoleEnum {

    HOST("HOST", "主持人"),
    RECORDER("RECORDER", "记录员"),
    PROJECT_MANAGER("PROJECT_MANAGER", "项目经理"),
    TECH_LEADER("TECH_LEADER", "技术负责人"),
    TRAINER("TRAINER", "培训师"),
    REVIEWER("REVIEWER", "评审专家"),
    DEPARTMENT_LEADER("DEPARTMENT_LEADER", "部门负责人"),
    PARTICIPANT("PARTICIPANT", "参会人员");

    private final String code;
    private final String name;

    /**
     * 根据code获取枚举
     */
    public static MeetingRoleEnum getByCode(String code) {
        for (MeetingRoleEnum role : values()) {
            if (role.getCode().equals(code)) {
                return role;
            }
        }
        return null;
    }

    /**
     * 根据name获取枚举
     */
    public static MeetingRoleEnum getByName(String name) {
        for (MeetingRoleEnum role : values()) {
            if (role.getName().equals(name)) {
                return role;
            }
        }
        return null;
    }
} 