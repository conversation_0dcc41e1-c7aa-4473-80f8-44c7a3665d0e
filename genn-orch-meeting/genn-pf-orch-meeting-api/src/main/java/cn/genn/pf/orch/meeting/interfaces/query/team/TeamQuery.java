package cn.genn.pf.orch.meeting.interfaces.query.team;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class TeamQuery {

    @ApiModelProperty(value = "层级id")
    @NotNull(message = "层级id不能为空")
    private Long levelId;

    @ApiModelProperty(value = "队组名称")
    @NotBlank(message = "队组名称不能为空")
    private String groupName;
}
