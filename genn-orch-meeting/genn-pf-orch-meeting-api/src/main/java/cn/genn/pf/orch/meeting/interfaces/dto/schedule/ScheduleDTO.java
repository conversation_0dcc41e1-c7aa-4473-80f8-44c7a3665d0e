package cn.genn.pf.orch.meeting.interfaces.dto.schedule;

import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ScheduleDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleDTO {

    @ApiModelProperty(value = "日程id")
    private Long id;

    @ApiModelProperty(value = "班组信息")
    private TeamInfoDTO teamInfoDTO;

    @ApiModelProperty(value = "业务会议ID")
    private Long businessMeetingId;
    @ApiModelProperty(value = "业务会议名称")
    private String businessMeetingName;

    @ApiModelProperty(value = "日程名称")
    private String scheduleName;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;
    @ApiModelProperty(value = "全天")
    private LocalDateTime allDay;

    @ApiModelProperty(value = "日程配置")
    private ScheduleConfigDTO scheduleConfigDTO;

    @ApiModelProperty(value = "参会人列表")
    private List<AttendUserDTO> attendUsers;

    @ApiModelProperty(value = "创建人Id")
    private String createUserId;

    @ApiModelProperty(value = "飞书会议Url")
    private String fsMeetingUrl;
}
