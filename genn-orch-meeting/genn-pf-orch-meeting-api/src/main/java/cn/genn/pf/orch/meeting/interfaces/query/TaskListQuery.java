package cn.genn.pf.orch.meeting.interfaces.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.pf.orch.meeting.interfaces.enums.TaskStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 任务列表查询对象
 * @date 2025-01-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskListQuery extends PageSortQuery implements Serializable {

    @ApiModelProperty(value = "任务状态", required = true)
    @NotNull(message = "任务状态不能为空")
    private TaskStatusEnum status;

    @ApiModelProperty(value = "用户ID（可选，不填则查询所有用户的数据）")
    private String userId;
}
