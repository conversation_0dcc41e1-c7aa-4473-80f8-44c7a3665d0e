package cn.genn.pf.orch.meeting.interfaces.dto.meeting;

import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 手机端会议详情对象
 * @date 2025-01-03
 */
@Data
public class MeetingDetailDTO {

    @ApiModelProperty(value = "会议id")
    private Long id;

    @ApiModelProperty(value = "日程id")
    private Long scheduleId;

    @ApiModelProperty(value = "会议名称")
    private String name;

    @ApiModelProperty(value = "班组信息")
    private TeamInfoDTO teamInfoDTO;

    @ApiModelProperty(value = "会议地址")
    private String meetingUrl;

    @ApiModelProperty(value = "会议妙计地址")
    private String minuteUrl;

    @ApiModelProperty(value = "会议开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "会议结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "日程开始时间")
    private LocalDateTime scheduleStartTime;

    @ApiModelProperty(value = "日程结束时间")
    private LocalDateTime scheduleEndTime;

    @ApiModelProperty("应参会人数")
    private Integer expectUserCount;

    @ApiModelProperty("请假人数")
    private Integer absentUserCount;

    @ApiModelProperty("实际参会人数")
    private Integer actualUserCount;

    @ApiModelProperty("应签到人数")
    private Integer expectCheckInUserCount;

    @ApiModelProperty("已签到人数")
    private Integer checkInUserCount;

    @ApiModelProperty(value = "会议记录人")
    private List<String> recorders;

    // todo：会议评价人

    @ApiModelProperty(value = "参会人列表")
    private List<MeetingAttendUserDTO> attendUserList;

    @ApiModelProperty(value = "会前准备资料")
    private List<MeetingAttachmentDTO> dataCatalog;

    @ApiModelProperty(value = "会议成果资料")
    private List<MeetingAttachmentDTO> achievementCatalog;

    @ApiModelProperty(value = "会议记录")
    private String record;

    @ApiModelProperty(value = "会议评价")
    private String evaluate;

    @ApiModelProperty(value = "会议记录是否是草稿 1：是，0：不是")
    private Integer isRecordDraft;

    @ApiModelProperty(value = "会议评价是否是草稿 1：是，0：不是")
    private Integer isEvaluateDraft;
}
