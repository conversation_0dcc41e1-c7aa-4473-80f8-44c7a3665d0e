package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 请假状态枚举
 * @date 2024-12-31
 */
@Getter
@AllArgsConstructor
public enum AbsentStatusEnum {

    /**
     * 未请假
     */
    NOT_ABSENT(0, "未请假"),
    /**
     * 已请假
     */
    ABSENT(1, "已请假"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String desc;

    private static final Map<Integer, AbsentStatusEnum> VALUES = new HashMap<>();

    static {
        for (final AbsentStatusEnum item : AbsentStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static AbsentStatusEnum of(int code) {
        return VALUES.get(code);
    }
}
