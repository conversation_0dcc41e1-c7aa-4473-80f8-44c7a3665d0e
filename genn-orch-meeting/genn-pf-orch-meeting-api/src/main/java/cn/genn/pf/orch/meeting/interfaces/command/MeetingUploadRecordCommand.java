package cn.genn.pf.orch.meeting.interfaces.command;

import cn.genn.pf.orch.meeting.interfaces.dto.meeting.MeetingAttachmentDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 上传会议记录命令
 * @date 2025-01-03
 */
@Data
public class MeetingUploadRecordCommand {

    @ApiModelProperty(value = "会议id")
    private Long id;

    @ApiModelProperty(value = "会议内容")
    private String record;

    @ApiModelProperty(value = "会前准备资料")
    private List<MeetingAttachmentDTO> dataCatalog;

    @ApiModelProperty(value = "会议成果资料")
    private List<MeetingAttachmentDTO> achievementCatalog;
}
