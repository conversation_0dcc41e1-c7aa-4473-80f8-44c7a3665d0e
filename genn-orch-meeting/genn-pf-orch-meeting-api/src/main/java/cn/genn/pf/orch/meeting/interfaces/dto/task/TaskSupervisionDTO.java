package cn.genn.pf.orch.meeting.interfaces.dto.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务督办DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("任务督办信息")
public class TaskSupervisionDTO {

    @ApiModelProperty("任务ID")
    private Long taskId;

    @ApiModelProperty("任务标题")
    private String title;

    @ApiModelProperty("任务描述")
    private String description;

    @ApiModelProperty("负责人OpenId")
    private String ownerOpenId;

    @ApiModelProperty("负责人姓名")
    private String ownerName;

    @ApiModelProperty("优先级")
    private Integer priority;

    @ApiModelProperty("优先级名称")
    private String priorityName;

    @ApiModelProperty("任务状态")
    private Integer status;

    @ApiModelProperty("任务状态名称")
    private String statusName;

    @ApiModelProperty("截止时间")
    private LocalDateTime dueDate;

    @ApiModelProperty("剩余时间（小时）")
    private Long remainingHours;

    @ApiModelProperty("是否紧急（距离截止时间3小时内）")
    private Boolean isUrgent;

    @ApiModelProperty("关联会议ID")
    private Long meetingId;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
