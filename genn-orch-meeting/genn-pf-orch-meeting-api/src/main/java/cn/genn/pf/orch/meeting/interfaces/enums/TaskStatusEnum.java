package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 任务状态枚举
 *
 * <AUTHOR> Assistant
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {

    /**
     * 未开始
     */
    NOT_STARTED(0, "未开始"),
    /**
     * 进行中
     */
    IN_PROGRESS(1, "进行中"),
    /**
     * 已完成
     */
    COMPLETED(2, "已完成"),
    /**
     * 已超期
     */
    OVERDUE(3, "已超期"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, TaskStatusEnum> VALUES = new HashMap<>();

    static {
        for (final TaskStatusEnum item : TaskStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static TaskStatusEnum of(int code) {
        return VALUES.get(code);
    }

    /**
     * 根据code获取枚举
     */
    public static TaskStatusEnum getByCode(Integer code) {
        return VALUES.get(code);
    }
}
