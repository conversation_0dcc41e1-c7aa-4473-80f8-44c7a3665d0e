package cn.genn.pf.orch.meeting.interfaces.query.team;

import cn.genn.core.model.page.PageSortQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class TeamPageQuery extends PageSortQuery implements Serializable {

    @ApiModelProperty(value = "层级id")
    @NotNull(message = "层级id不能为空")
    private Long levelId;

    @ApiModelProperty(value = "队组名称")
    private String groupName;

    @ApiModelProperty(value = "班组id")
    private Long teamId;

    @ApiModelProperty(value = "班组长")
    private String leader;

    @ApiModelProperty(value = "班组成员")
    private String member;

    @ApiModelProperty(value = "评价人员")
    private String reviewer;
}
