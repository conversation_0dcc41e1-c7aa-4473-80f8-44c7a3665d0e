package cn.genn.pf.orch.meeting.interfaces.dto.team;

import cn.genn.pf.orch.meeting.interfaces.dto.user.FSUserInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import cn.genn.core.model.enums.DeletedEnum;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * TeamInfoDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeamInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "班组id")
    private Long id;

    @ApiModelProperty(value = "队组名称")
    private String groupName;

    @ApiModelProperty(value = "班组名称")
    private String name;

    @ApiModelProperty(value = "班组层级id")
    private Long levelId;

    @ApiModelProperty(value = "所属区域")
    private String level1Name;

    @ApiModelProperty(value = "煤矿单位")
    private String level2Name;

    @ApiModelProperty(value = "班组长")
    private String leader;

    @ApiModelProperty(value = "班组长")
    private FSUserInfoDTO leaderInfo;

    @ApiModelProperty(value = "技术员")
    private List<String> technicians;

    @ApiModelProperty(value = "技术员")
    private List<FSUserInfoDTO> technicianInfos;

    @ApiModelProperty(value = "班组成员")
    private List<String> members;

    @ApiModelProperty(value = "班组成员")
    private List<FSUserInfoDTO> memberInfos;

    @ApiModelProperty(value = "评价人员")
    private List<String> reviewers;

    @ApiModelProperty(value = "评价人员")
    private List<FSUserInfoDTO> reviewerInfos;

    @ApiModelProperty(value = "完成通知人员")
    private List<String> finishNotify;

    @ApiModelProperty(value = "完成通知人员")
    private List<FSUserInfoDTO> finishNotifyInfos;

    @ApiModelProperty(value = "删除标记（0未删除）")
    private DeletedEnum deleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人id")
    private String createUserId;

    @ApiModelProperty(value = "创建人")
    private String createUserName;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "更新人id")
    private String updateUserId;

    @ApiModelProperty(value = "更新人")
    private String updateUserName;


}

