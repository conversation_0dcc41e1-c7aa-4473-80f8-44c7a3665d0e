package cn.genn.pf.orch.meeting.interfaces.dto.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * BusinessMeetingDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessMeetingDTO {

    @ApiModelProperty(value = "层级Id 父Id-子Id")
    private String levelId;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "业务名")
    private String name;

    @ApiModelProperty(value = "子业务会议类")
    private List<BusinessMeetingDTO> sonBusinessMeeting;
}
