package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 参会人员状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AttendUserStateEnum {
    EXPECT(1, "预期参会"),
    ABSENT(2, "请假缺席"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    AttendUserStateEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, AttendUserStateEnum> VALUES = new HashMap<>();

    static {
        for (final AttendUserStateEnum item : AttendUserStateEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static AttendUserStateEnum of(int code) {
        return VALUES.get(code);
    }
}
