package cn.genn.pf.orch.meeting.interfaces.dto.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * BusinessMeetingLevelDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessMeetingLevelDTO {

    @ApiModelProperty(value = "业务会议层级")
    private List<BusinessMeetingDTO> businessMeetingLevel;
}
