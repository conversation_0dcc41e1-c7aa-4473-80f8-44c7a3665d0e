package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 签到状态枚举
 * @date 2024-12-31
 */
@Getter
@AllArgsConstructor
public enum CheckInStatusEnum {

    WAIT(0, "待签到"),
    CHECKED(1, "已签到"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, CheckInStatusEnum> VALUES = new HashMap<>();

    static {
        for (final CheckInStatusEnum item : CheckInStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static CheckInStatusEnum of(int code) {
        return VALUES.get(code);
    }
}
