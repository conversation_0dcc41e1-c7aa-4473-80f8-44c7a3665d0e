package cn.genn.pf.orch.meeting.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * AddBusinessMeetingCommand
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddBusinessMeetingCommand {

    @ApiModelProperty(value = "业务会议名称")
    @NotNull(message = "业务会议名称不能为空")
    private String name;

    @ApiModelProperty(value = "父层级Id")
    private Long parentId;
}
