package cn.genn.pf.orch.meeting.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class TeamLevelCommand {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "上级id（为0表示没有上级）")
    @NotNull(message = "上级id不能为空")
    private Long pid;

    @ApiModelProperty(value = "当前层级（从1开始）")
    @NotNull(message = "当前层级不能为空")
    private Integer level;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    private String name;

}
