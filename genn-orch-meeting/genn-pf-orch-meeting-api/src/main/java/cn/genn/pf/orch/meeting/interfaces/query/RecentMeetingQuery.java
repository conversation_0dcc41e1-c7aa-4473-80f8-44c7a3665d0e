package cn.genn.pf.orch.meeting.interfaces.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingStatusFilterEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 近期会议查询对象
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RecentMeetingQuery extends PageSortQuery implements Serializable {

    @ApiModelProperty("状态筛选: ALL|ONGOING|COMPLETED")
    private MeetingStatusFilterEnum status;
}
