package cn.genn.pf.orch.meeting.interfaces.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * ScheduleConfigQuery
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleConfigQuery {

    @ApiModelProperty(value = "业务会议id")
    @NotNull(message = "业务会议id不能为空")
    private Long businessMeetingId;
}
