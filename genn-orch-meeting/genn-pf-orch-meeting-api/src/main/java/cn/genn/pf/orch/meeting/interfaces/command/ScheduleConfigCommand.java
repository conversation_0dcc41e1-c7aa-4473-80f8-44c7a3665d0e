package cn.genn.pf.orch.meeting.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * AddScheduleConfigCommand
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleConfigCommand {

    @ApiModelProperty(value = "业务会议id")
    @NotNull(message = "业务会议id不能为空")
    private Long businessMeetingId;

    @ApiModelProperty(value = "会议目的")
    private String goal;

    @ApiModelProperty(value = "会议决策")
    private String decision;

    @ApiModelProperty(value = "资料目录")
    private List<String> dataCatalog;

    @ApiModelProperty(value = "成果目录")
    private List<String> achievementCatalog;
}
