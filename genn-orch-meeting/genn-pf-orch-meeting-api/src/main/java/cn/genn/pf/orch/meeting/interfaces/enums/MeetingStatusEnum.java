package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 会议状态枚举
 * @date 2024-12-31
 */
@Getter
@AllArgsConstructor
public enum MeetingStatusEnum {

    WAIT_START(0, "未开始"),
    IN_PROCESS(1, "进行中"),
    END(2, "已结束"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, MeetingStatusEnum> VALUES = new HashMap<>();

    static {
        for (final MeetingStatusEnum item : MeetingStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static MeetingStatusEnum of(int code) {
        return VALUES.get(code);
    }
}
