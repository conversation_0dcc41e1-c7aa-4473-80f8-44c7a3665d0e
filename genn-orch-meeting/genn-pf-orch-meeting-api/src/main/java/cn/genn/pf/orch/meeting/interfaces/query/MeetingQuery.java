package cn.genn.pf.orch.meeting.interfaces.query;

import cn.genn.core.model.page.PageSortQuery;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 手机端会议查询参数
 * @date 2025-01-03
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MeetingQuery extends PageSortQuery {

    @ApiModelProperty(value = "业务会议id")
    @NotNull(message = "业务会议id不能为空")
    private Long businessMeetingId;

    @ApiModelProperty(value = "会议状态")
    private List<MeetingStatusEnum> statusList;

    @ApiModelProperty(value = "是否上传会议记录 0-未上传 1-已上传")
    private Integer uploadRecordFlag;

    @ApiModelProperty(value = "是否上传会议评价 0-未上传 1-已上传")
    private Integer uploadEvaluateFlag;

    @ApiModelProperty(value = "当前用户id")
    private String currentUserId;

    @ApiModelProperty("煤矿区域id")
    private Long coalMiningRegionId;

    @ApiModelProperty("煤矿单位id")
    private List<Long> levelIds;

    @ApiModelProperty("队组名称")
    private String groupName;

    @ApiModelProperty("班组id")
    private Long teamId;

    @ApiModelProperty("会议结束时间-左")
    private LocalDateTime endTimeLeft;

    @ApiModelProperty("会议结束时间-右")
    private LocalDateTime endTimeRight;

    @ApiModelProperty("参会人员姓名")
    private String attendUserName;



}
