package cn.genn.pf.orch.meeting.interfaces.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> Assistant
 * @description 知识库文件DTO
 */
@Data
public class KbRepoFileDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long repoId;

    private Long collectionId;

    private String filePlatform;

    private String externalFileId;

    private String externalFileUrl;

    private Integer length;

    private String fileName;

    private String contentType;

    private String metadata;
}
