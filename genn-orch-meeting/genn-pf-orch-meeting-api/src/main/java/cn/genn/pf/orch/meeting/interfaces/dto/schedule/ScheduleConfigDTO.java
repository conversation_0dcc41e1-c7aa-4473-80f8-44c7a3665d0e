package cn.genn.pf.orch.meeting.interfaces.dto.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * ScheduleConfigDTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleConfigDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "业务会议id")
    private Long businessMeetingId;

    @ApiModelProperty(value = "会议目的")
    private String goal;

    @ApiModelProperty(value = "会议决策")
    private String decision;

    @ApiModelProperty(value = "资料目录")
    private List<String> dataCatalog;

    @ApiModelProperty(value = "成果目录")
    private List<String> achievementCatalog;
}
