package cn.genn.pf.orch.meeting.interfaces.command;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 会议录制完成事件
 * @date 2025-01-07
 */
@Data
public class MeetingRecordReadyEventCommand {

    private String url;

    private String duration;

    private Meeting meeting;

    @Data
    public static class Meeting {

        private String id;
        private String topic;
        @JsonProperty("meeting_no")
        private String meetingNo;
    }
}
