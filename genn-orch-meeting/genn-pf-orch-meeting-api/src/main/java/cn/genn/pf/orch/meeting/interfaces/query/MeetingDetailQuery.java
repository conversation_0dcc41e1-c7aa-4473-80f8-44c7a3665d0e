package cn.genn.pf.orch.meeting.interfaces.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 会议详情查询参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingDetailQuery {

    @ApiModelProperty("会议id")
    private Long id;

    @ApiModelProperty("是否获取草稿")
    private boolean isDraft = false;
}
