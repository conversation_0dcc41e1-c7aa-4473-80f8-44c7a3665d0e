package cn.genn.pf.orch.meeting.interfaces.dto.user;

import cn.genn.pf.orch.meeting.interfaces.dto.AuthConfigDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.ClientTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;


@Data
public class UserInfoDTO {

    private String token;

    private String userAccessToken;

    private ClientTypeEnum clientType;

    /**
     * 姓名
     */
    private String name;

    private String openId;

    // private String userId;

    private String mobile;

    /**
     * 工号
     */
    private String employeeNo;

    /**
     * 英文名称
     */
    private String enName;
    /**
     * 用户头像
     * <p> 示例值：www.feishu.cn/avatar/icon
     */
    private String avatarUrl;
    /**
     * 用户头像 72x72
     * <p> 示例值：www.feishu.cn/avatar/icon
     */
    private String avatarThumb;
    /**
     * 用户头像 240x240
     * <p> 示例值：www.feishu.cn/avatar/icon
     */
    private String avatarMiddle;
    /**
     * 用户头像 640x640
     * <p> 示例值：www.feishu.cn/avatar/icon
     */
    private String avatarBig;

    /**
     * 用户数据权限,上下文中未缓存
     */
    private AuthConfigDTO authConfig;

    private String companyName;

    private LocalDateTime lastRefreshTime;

}
