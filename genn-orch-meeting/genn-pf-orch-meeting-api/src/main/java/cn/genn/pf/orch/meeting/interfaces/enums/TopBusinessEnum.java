package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 顶层业务类枚举
 *
 * <AUTHOR>
 */
@Getter
public enum TopBusinessEnum {
    TOP_BUSINESS(1, "是顶层业务"),
    NOT_TOP_BUSINESS(0, "不是顶层业务"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    TopBusinessEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, TopBusinessEnum> VALUES = new HashMap<>();

    static {
        for (final TopBusinessEnum item : TopBusinessEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static TopBusinessEnum of(int code) {
        return VALUES.get(code);
    }
}
