package cn.genn.pf.orch.meeting.interfaces.dto.task;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 任务统计DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("任务统计信息")
public class TaskStatisticsDTO {

    @ApiModelProperty("总任务数")
    private Long totalTasks;

    @ApiModelProperty("未开始任务数")
    private Long notStartedTasks;

    @ApiModelProperty("进行中任务数")
    private Long inProgressTasks;

    @ApiModelProperty("已完成任务数")
    private Long completedTasks;

    @ApiModelProperty("已超期任务数")
    private Long overdueTasks;

    @ApiModelProperty("今日到期任务数")
    private Long todayDueTasks;

    @ApiModelProperty("本周到期任务数")
    private Long weekDueTasks;

    @ApiModelProperty("高优先级任务数")
    private Long highPriorityTasks;

    @ApiModelProperty("任务完成率（百分比）")
    private Double completionRate;

    @ApiModelProperty("任务超期率（百分比）")
    private Double overdueRate;

    @ApiModelProperty("各状态任务统计详情")
    private List<TaskStatusStatistics> statusStatistics;

    @ApiModelProperty("各优先级任务统计详情")
    private List<TaskPriorityStatistics> priorityStatistics;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("任务状态统计")
    public static class TaskStatusStatistics {
        @ApiModelProperty("状态代码")
        private Integer statusCode;

        @ApiModelProperty("状态名称")
        private String statusName;

        @ApiModelProperty("任务数量")
        private Long count;

        @ApiModelProperty("占比（百分比）")
        private Double percentage;

        @ApiModelProperty("颜色代码（用于图表显示）")
        private String color;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("任务优先级统计")
    public static class TaskPriorityStatistics {
        @ApiModelProperty("优先级代码")
        private Integer priorityCode;

        @ApiModelProperty("优先级名称")
        private String priorityName;

        @ApiModelProperty("任务数量")
        private Long count;

        @ApiModelProperty("占比（百分比）")
        private Double percentage;
    }
}
