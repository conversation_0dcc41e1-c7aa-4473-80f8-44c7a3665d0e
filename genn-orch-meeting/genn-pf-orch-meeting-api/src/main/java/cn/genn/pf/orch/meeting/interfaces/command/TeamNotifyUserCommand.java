package cn.genn.pf.orch.meeting.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class TeamNotifyUserCommand {

    @ApiModelProperty("手机号")
    @NotEmpty(message = "手机号不能为空")
    private List<String> telephone;

    /**
     * 仅限2级数据;
     * 该字段为空所有单位都会增加此用户
     */
    @ApiModelProperty("对应team_level.id")
    private List<Long> levelIds;
}
