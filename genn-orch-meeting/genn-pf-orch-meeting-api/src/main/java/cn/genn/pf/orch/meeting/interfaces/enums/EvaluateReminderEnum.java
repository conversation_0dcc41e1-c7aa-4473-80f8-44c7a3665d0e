package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 评价提醒状态枚举
 */
@Getter
@AllArgsConstructor
public enum EvaluateReminderEnum {

    NOT_REMINDED(0, "未提醒"),
    REMINDED(1, "已提醒"),
    ;

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, EvaluateReminderEnum> VALUES = new HashMap<>();

    static {
        for (final EvaluateReminderEnum item : EvaluateReminderEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static EvaluateReminderEnum of(int code) {
        return VALUES.get(code);
    }
}
