package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum TimeUnitEnum {
    MINUTE("MINUTE", "分钟"),
    HOUR("HOUR", "小时"),
    DAY("DAY", "日"),
    WEEK("WEEK", "周"),
    MONTH("MONTH", "月"),
    YEAR("YEAR", "年");

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    private static final Map<String, TimeUnitEnum> VALUES = new HashMap<>();

    static {
        for (final TimeUnitEnum item : TimeUnitEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static TimeUnitEnum of(String code) {
        return VALUES.get(code);
    }
}
