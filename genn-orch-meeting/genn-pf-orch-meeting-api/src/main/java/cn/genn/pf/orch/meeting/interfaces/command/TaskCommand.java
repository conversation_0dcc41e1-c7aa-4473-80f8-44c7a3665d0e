package cn.genn.pf.orch.meeting.interfaces.command;

import cn.genn.pf.orch.meeting.interfaces.enums.TaskPriorityEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR> Assistant
 * @description 任务操作命令
 */
@Data
@ApiModel("任务操作命令")
public class TaskCommand {

    @ApiModelProperty("任务ID（更新时必填）")
    private Long id;

    @ApiModelProperty("飞书任务ID")
    private String feishuTaskId;

    @ApiModelProperty(value = "任务标题", required = true)
    @NotBlank(message = "任务标题不能为空")
    private String title;

    @ApiModelProperty("任务描述")
    private String description;

    @ApiModelProperty(value = "负责人OpenID", required = true)
    @NotBlank(message = "负责人OpenID不能为空")
    private String ownerOpenId;

    @ApiModelProperty(value = "负责人名称", required = true)
    @NotBlank(message = "负责人名称不能为空")
    private String ownerName;

    @ApiModelProperty(value = "优先级", required = true)
    @NotNull(message = "优先级不能为空")
    private TaskPriorityEnum priority;

    @ApiModelProperty("截止时间")
    private LocalDateTime dueDate;

    @ApiModelProperty("关联的会议ID")
    private Long meetingId;
}
