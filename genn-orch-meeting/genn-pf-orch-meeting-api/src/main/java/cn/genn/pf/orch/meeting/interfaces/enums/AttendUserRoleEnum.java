package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 参会人员角色枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AttendUserRoleEnum {
    NORMAL(1, "普通"),
    RECORDER(2, "记录员"),
    EVALUATOR(3, "评价员"),
    ;

    @EnumValue
    @JsonValue
    private final int code;

    private final String description;

    AttendUserRoleEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, AttendUserRoleEnum> VALUES = new HashMap<>();

    static {
        for (final AttendUserRoleEnum item : AttendUserRoleEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static AttendUserRoleEnum of(int code) {
        return VALUES.get(code);
    }
}
