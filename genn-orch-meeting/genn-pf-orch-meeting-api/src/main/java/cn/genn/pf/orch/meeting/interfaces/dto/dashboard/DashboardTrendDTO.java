package cn.genn.pf.orch.meeting.interfaces.dto.dashboard;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 仪表板趋势分析数据传输对象
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DashboardTrendDTO {
    
    @ApiModelProperty("时间标签")
    private List<String> timeLabels;
    
    @ApiModelProperty("数据系列")
    private List<TrendSeriesDTO> series;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendSeriesDTO {
        @ApiModelProperty("系列名称")
        private String name;
        
        @ApiModelProperty("数据值")
        private List<Integer> data;
    }
} 