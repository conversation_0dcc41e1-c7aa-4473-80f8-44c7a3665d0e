package cn.genn.pf.orch.meeting.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class TeamInfoCommand {

    @ApiModelProperty(value = "班组id")
    private Long id;

    @ApiModelProperty(value = "队组名称")
    @NotBlank(message = "队组名称不能为空")
    private String groupName;

    @ApiModelProperty(value = "班组名称")
    @NotBlank(message = "队组名称不能为空")
    private String name;

    @ApiModelProperty(value = "班组层级id")
    @NotNull(message = "班组层级id不能为空")
    private Long levelId;

    @ApiModelProperty(value = "班组长")
    @NotBlank(message = "班组长不能为空")
    private String leader;

    @ApiModelProperty(value = "技术员")
    @NotEmpty(message = "技术员不能为空")
    private List<String> technicians;

    @ApiModelProperty(value = "班组成员")
    @NotEmpty(message = "班组成员不能为空")
    private List<String> members;

    @ApiModelProperty(value = "评价人员")
    @NotEmpty(message = "评价人员不能为空")
    private List<String> reviewers;
}
