package cn.genn.pf.orch.meeting.interfaces.dto.meeting;

import cn.genn.pf.orch.meeting.interfaces.dto.team.TeamInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.dto.user.FSUserInfoDTO;
import cn.genn.pf.orch.meeting.interfaces.enums.MeetingStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 手机端会议列表对象
 * @date 2025-01-03
 */
@Data
public class MeetingListDTO {

    @ApiModelProperty(value = "会议id")
    private Long id;

    @ApiModelProperty(value = "班组信息")
    private TeamInfoDTO teamInfoDTO;

    @ApiModelProperty(value = "日程id")
    private Long scheduleId;

    @ApiModelProperty(value = "会议名称")
    private String name;

    @ApiModelProperty(value = "会议地址")
    private String meetingUrl;

    @ApiModelProperty(value = "会议妙计地址")
    private String minuteUrl;

    @ApiModelProperty(value = "会议开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "会议结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "会议时长")
    private String duration;

    @ApiModelProperty(value = "日程开始时间")
    private LocalDateTime scheduleStartTime;

    @ApiModelProperty(value = "日程开始时间(星期)")
    private String scheduleStartTimeDayOfWeek;

    @ApiModelProperty(value = "日程结束时间")
    private LocalDateTime scheduleEndTime;

    @ApiModelProperty(value = "日程时长")
    private String scheduleDuration;

    @ApiModelProperty(value = "会议状态")
    private MeetingStatusEnum status;

    @ApiModelProperty(value = "会议类型id")
    private Long businessMeetingId;

    @ApiModelProperty(value = "会议类型")
    private String businessMeetingName;

    @ApiModelProperty("应参会人数")
    private Integer expectUserCount = 0;

    @ApiModelProperty("请假人数")
    private Integer absentUserCount = 0;

    @ApiModelProperty("实际参会人数")
    private Integer actualUserCount = 0;

    @ApiModelProperty("应签到人数")
    private Integer expectCheckInUserCount = 0;

    @ApiModelProperty("已签到人数")
    private Integer checkInUserCount = 0;

    @ApiModelProperty(value = "会议记录人")
    private List<FSUserInfoDTO> recorders;

    // todo：会议评价人

    @ApiModelProperty(value = "参会人列表")
    private List<MeetingAttendUserDTO> attendUserList;

    @ApiModelProperty(value = "是否是会议创建者 0-否 1-是，只有会议创建者，展示发起、编辑、删除会议按钮")
    private Integer isOwner;

    @ApiModelProperty(value = "是否是会议记录者 0-否 1-是，只有会议记录者，展示上传会议记录按钮")
    private Integer isRecorder;

    @ApiModelProperty(value = "是否是会议评价者 0-否 1-是，只有会议评价者，展示上传会议评价按钮")
    private Integer isEvaluator;

    @ApiModelProperty(value = "是否允许签到 0-否 1-是")
    private Integer allowCheckIn;

    @ApiModelProperty(value = "是否允许上传会议记录 0-否 1-是")
    private Integer allowUpload;
}
