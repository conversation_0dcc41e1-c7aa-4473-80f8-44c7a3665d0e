package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum MeetingStatusFilterEnum {
    ALL("ALL", "全部"),
    ONGOING("ONGOING", "进行中"),
    COMPLETED("COMPLETED", "已完成");

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    private static final Map<String, MeetingStatusFilterEnum> VALUES = new HashMap<>();

    static {
        for (final MeetingStatusFilterEnum item : MeetingStatusFilterEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static MeetingStatusFilterEnum of(String code) {
        return VALUES.get(code);
    }
} 