package cn.genn.pf.orch.meeting.interfaces.dto.meetingplan;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 冲突信息DTO
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConflictInfo {

    @ApiModelProperty(value = "冲突的会议规划ID")
    private Long conflictPlanId;

    @ApiModelProperty(value = "冲突的会议规划名称")
    private String conflictPlanName;

    @ApiModelProperty(value = "冲突的会议时间")
    private LocalDateTime conflictStartTime;

    @ApiModelProperty(value = "冲突的会议结束时间")
    private LocalDateTime conflictEndTime;

    @ApiModelProperty(value = "是否会议室冲突")
    private Boolean hasLocationConflict;

    @ApiModelProperty(value = "冲突的会议室")
    private String conflictLocation;

    @ApiModelProperty(value = "是否人员冲突")
    private Boolean hasAttendeeConflict;

    @ApiModelProperty(value = "冲突的人员列表")
    private List<String> conflictAttendees;

    @ApiModelProperty(value = "冲突描述")
    private String conflictMessage;
}
