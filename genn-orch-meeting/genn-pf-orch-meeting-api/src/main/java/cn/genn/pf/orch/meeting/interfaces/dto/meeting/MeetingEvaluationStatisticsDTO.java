package cn.genn.pf.orch.meeting.interfaces.dto.meeting;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议评价统计DTO
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingEvaluationStatisticsDTO {
    
    @ApiModelProperty("会议ID")
    private Long meetingId;
    
    @ApiModelProperty("总评价数")
    private Integer totalEvaluations;
    
    @ApiModelProperty("会议整体平均分")
    private Double avgMeetingScore;
    
    @ApiModelProperty("会议内容平均分")
    private Double avgContentScore;
    
    @ApiModelProperty("会议时长平均分")
    private Double avgDurationScore;
    
    @ApiModelProperty("会议效果平均分")
    private Double avgEffectivenessScore;
    
    @ApiModelProperty("评价列表")
    private List<MeetingEvaluationInfoDTO> evaluations;
} 