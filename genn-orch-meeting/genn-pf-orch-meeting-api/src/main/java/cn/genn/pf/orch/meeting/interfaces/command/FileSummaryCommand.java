package cn.genn.pf.orch.meeting.interfaces.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR> Assistant
 * @description 文件汇总提炼命令
 */
@Data
@ApiModel("文件汇总提炼命令")
public class FileSummaryCommand {

    @ApiModelProperty(value = "文件链接", required = true)
    @NotBlank(message = "文件链接不能为空")
    private String fileUrl;

    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @ApiModelProperty(value = "智能体应用ID")
    private String appId;

    @ApiModelProperty(value = "自定义提示词")
    private String customPrompt;

    @ApiModelProperty(value = "图片链接列表")
    private List<String> imageUrls;
}
