package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum GrowthTypeEnum {
    INCREASE("INCREASE", "增长"),
    DECREASE("DECREASE", "下降"),
    EQUAL("EQUAL", "持平");

    @EnumValue
    @JsonValue
    private final String code;

    private final String description;

    private static final Map<String, GrowthTypeEnum> VALUES = new HashMap<>();

    static {
        for (final GrowthTypeEnum item : GrowthTypeEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static GrowthTypeEnum of(String code) {
        return VALUES.get(code);
    }
} 