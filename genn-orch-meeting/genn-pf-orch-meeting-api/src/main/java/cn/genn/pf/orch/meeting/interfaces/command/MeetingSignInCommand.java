package cn.genn.pf.orch.meeting.interfaces.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 签到
 */
@Data
public class MeetingSignInCommand {

    @ApiModelProperty("会议id")
    @NotNull(message = "会议编码不能为空")
    private Long meetingId;

    @ApiModelProperty("签到人")
    // @NotBlank(message = "签到人信息不能为空")
    private String openId;

    @ApiModelProperty("签到码")
    @NotBlank(message = "签到码不能为空")
    private String signInCode;

    @ApiModelProperty("签到地址")
    @NotBlank(message = "签到地址不能为空")
    private String checkInAddress;
}
