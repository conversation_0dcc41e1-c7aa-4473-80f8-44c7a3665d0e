package cn.genn.pf.orch.meeting.interfaces.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 优先级枚举
 * @date 2025-01-24
 */
@Getter
@AllArgsConstructor
public enum PriorityLevelEnum {

    LOW(1, "低"),
    MEDIUM(2, "中"),
    HIGH(3, "高"),
    URGENT(4, "紧急");

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, PriorityLevelEnum> VALUES = new HashMap<>();

    static {
        for (final PriorityLevelEnum item : PriorityLevelEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static PriorityLevelEnum of(int code) {
        return VALUES.get(code);
    }
}
