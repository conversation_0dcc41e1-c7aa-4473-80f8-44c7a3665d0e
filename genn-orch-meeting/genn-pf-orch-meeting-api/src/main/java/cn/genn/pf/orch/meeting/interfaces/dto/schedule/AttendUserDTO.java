package cn.genn.pf.orch.meeting.interfaces.dto.schedule;

import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserRoleEnum;
import cn.genn.pf.orch.meeting.interfaces.enums.AttendUserStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AttendUser
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendUserDTO {

    @ApiModelProperty(value = "参会人id")
    private String attendUserId;

    @ApiModelProperty(value = "参会人姓名")
    private String attendUserName;

    @ApiModelProperty(value = "参会人头像")
    private String avatarUrl;

    @ApiModelProperty(value = "参会人员状态, 1:EXPECT-预期参会, 2:ABSENT-请假缺席")
    private AttendUserStateEnum attendUserState;

    @ApiModelProperty(value = "请假原因，仅当参会人员类别为ABSENT时有效")
    private String absentReason;

    @ApiModelProperty(value = "参会人员角色, 1:NORMAL-普通, 2:RECORDER-记录员")
    private AttendUserRoleEnum attendUserRole;
}
