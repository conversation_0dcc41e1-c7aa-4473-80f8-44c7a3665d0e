<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.july.app</groupId>
        <artifactId>july-orch-meeting</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>july-orch-meeting-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <minio.version>8.5.2</minio.version>
        <esdk-obs-java.version>3.22.3.1</esdk-obs-java.version>
        <easyexcel.version>3.3.3</easyexcel.version>
        <aws-java-sdk-s3.version>1.12.429</aws-java-sdk-s3.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.july.boot</groupId>
            <artifactId>july-spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.july.boot</groupId>
            <artifactId>july-spring-boot-starter-database</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.july.boot</groupId>
            <artifactId>july-spring-boot-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.july.boot</groupId>
            <artifactId>july-spring-boot-starter-event-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.july.boot</groupId>
            <artifactId>july-spring-boot-starter-cache</artifactId>
        </dependency>

        <!-- ======================= 其他三方依赖   =======================      -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- 对象存储接入 -->
        <dependency>
            <groupId>org.dromara.x-file-storage</groupId>
            <artifactId>x-file-storage-spring</artifactId>
            <version>2.2.1</version>
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>${minio.version}</version>
        </dependency>
        <!-- <dependency> -->
        <!--     <groupId>com.huaweicloud</groupId> -->
        <!--     <artifactId>esdk-obs-java</artifactId> -->
        <!--     <version>${esdk-obs-java.version}</version> -->
        <!-- </dependency> -->
        <!-- <dependency> -->
        <!--     <groupId>com.amazonaws</groupId> -->
        <!--     <artifactId>aws-java-sdk-s3</artifactId> -->
        <!--     <version>${aws-java-sdk-s3.version}</version> -->
        <!-- </dependency> -->

<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>easyexcel</artifactId>-->
<!--            <version>${easyexcel.version}</version>-->
<!--        </dependency>-->

        <!-- july-feishu模块 -->
        <dependency>
            <groupId>cn.july.app</groupId>
            <artifactId>july-feishu</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>pf-orch-meiye</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
        </resources>
    </build>

    <profiles>
        <!--  自身和外部api包管理,每次依赖一个新的外部api,在这里定义版本号,dev和test保持 为 july-service-api.version     -->
        <profile>
            <id>local</id>
            <properties>
                <july-pf-orch-meeting-api.version>1.0.0-SNAPSHOT</july-pf-orch-meeting-api.version>
                <spring.profiles.active>local</spring.profiles.active>
            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                        <excludes>
                            <exclude>*-dev.yml</exclude>
                            <exclude>*-test.yml</exclude>
                            <exclude>*-pro.yml</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <july-service-api.version>${july-service-api.version}</july-service-api.version>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                        <excludes>
                            <exclude>*-local.yml</exclude>
                            <exclude>*-test.yml</exclude>
                            <exclude>*-pro.yml</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <july-service-api.version>${july-service-api.version}</july-service-api.version>
                <spring.profiles.active>test</spring.profiles.active>
            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                        <excludes>
                            <exclude>*-dev.yml</exclude>
                            <exclude>*-local.yml</exclude>
                            <exclude>*-pro.yml</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>pro</id>
            <properties>
                <july-service-api.version>1.0.0-RELEASE</july-service-api.version>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                        <excludes>
                            <exclude>*-dev.yml</exclude>
                            <exclude>*-test.yml</exclude>
                            <exclude>*-local.yml</exclude>
                        </excludes>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>

</project>