<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.july.app</groupId>
    <artifactId>july-orch-meeting</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>cn.july.app</groupId>
  <artifactId>july-orch-meeting-service</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <properties>
    <easyexcel.version>3.3.3</easyexcel.version>
    <esdk-obs-java.version>3.22.3.1</esdk-obs-java.version>
    <minio.version>8.5.2</minio.version>
    <aws-java-sdk-s3.version>1.12.429</aws-java-sdk-s3.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
  </properties>
  <dependencies>
    <dependency>
      <groupId>cn.july.boot</groupId>
      <artifactId>july-spring-boot-starter-web</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
          <groupId>com.alibaba.cloud</groupId>
        </exclusion>
        <exclusion>
          <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
          <groupId>com.alibaba.cloud</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>cn.july.boot</groupId>
      <artifactId>july-spring-boot-starter-database</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.july.boot</groupId>
      <artifactId>july-spring-boot-starter-swagger</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.july.boot</groupId>
      <artifactId>july-spring-boot-starter-event-spring</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.july.boot</groupId>
      <artifactId>july-spring-boot-starter-cache</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.dromara.x-file-storage</groupId>
      <artifactId>x-file-storage-spring</artifactId>
      <version>2.2.1</version>
    </dependency>
    <dependency>
      <groupId>io.minio</groupId>
      <artifactId>minio</artifactId>
      <version>${minio.version}</version>
    </dependency>
    <dependency>
      <groupId>cn.july.app</groupId>
      <artifactId>july-feishu</artifactId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>
  </dependencies>
  <build>
    <resources>
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.xml</include>
        </includes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <includes>
          <include>**/*.xml</include>
          <include>**/*.properties</include>
        </includes>
      </resource>
    </resources>
    <finalName>pf-orch-meiye</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <includeSystemScope>true</includeSystemScope>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>local</id>
      <build>
        <resources>
          <resource>
            <filtering>true</filtering>
            <directory>/Users/<USER>/IdeaProjects/july/july-orch-meeting/july-orch-meeting-service/src/main/resources</directory>
            <excludes>
              <exclude>*-dev.yml</exclude>
              <exclude>*-test.yml</exclude>
              <exclude>*-pro.yml</exclude>
            </excludes>
          </resource>
        </resources>
      </build>
      <properties>
        <july-pf-orch-meeting-api.version>1.0.0-SNAPSHOT</july-pf-orch-meeting-api.version>
        <spring.profiles.active>local</spring.profiles.active>
      </properties>
    </profile>
    <profile>
      <id>dev</id>
      <build>
        <resources>
          <resource>
            <filtering>true</filtering>
            <directory>src/main/resources</directory>
            <excludes>
              <exclude>*-local.yml</exclude>
              <exclude>*-test.yml</exclude>
              <exclude>*-pro.yml</exclude>
            </excludes>
          </resource>
        </resources>
      </build>
      <properties>
        <spring.profiles.active>dev</spring.profiles.active>
        <july-service-api.version>${july-service-api.version}</july-service-api.version>
      </properties>
    </profile>
    <profile>
      <id>test</id>
      <build>
        <resources>
          <resource>
            <filtering>true</filtering>
            <directory>src/main/resources</directory>
            <excludes>
              <exclude>*-dev.yml</exclude>
              <exclude>*-local.yml</exclude>
              <exclude>*-pro.yml</exclude>
            </excludes>
          </resource>
        </resources>
      </build>
      <properties>
        <spring.profiles.active>test</spring.profiles.active>
        <july-service-api.version>${july-service-api.version}</july-service-api.version>
      </properties>
    </profile>
    <profile>
      <id>pro</id>
      <build>
        <resources>
          <resource>
            <filtering>true</filtering>
            <directory>src/main/resources</directory>
            <excludes>
              <exclude>*-dev.yml</exclude>
              <exclude>*-test.yml</exclude>
              <exclude>*-local.yml</exclude>
            </excludes>
          </resource>
        </resources>
      </build>
      <properties>
        <spring.profiles.active>prod</spring.profiles.active>
        <july-service-api.version>1.0.0-RELEASE</july-service-api.version>
      </properties>
    </profile>
  </profiles>
</project>
