package cn.july.orch.meeting.service;

import cn.july.orch.meeting.assembler.MeetingStandardAssembler;
import cn.july.orch.meeting.domain.command.MeetingStandardCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingStandardUpdateCommand;
import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.dto.AttendeeStatisticsDTO;
import cn.july.orch.meeting.domain.dto.AttendeeSuggestionDTO;
import cn.july.orch.meeting.domain.dto.MeetingStandardDTO;
import cn.july.orch.meeting.domain.dto.RoleInfoDTO;
import cn.july.orch.meeting.domain.entity.MeetingStandard;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import cn.july.orch.meeting.domain.query.MeetingStandardQuery;
import cn.july.orch.meeting.enums.MeetingRoleEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.TimeUnitEnum;
import cn.july.orch.meeting.mapper.MeetingPlanMapper;
import cn.july.orch.meeting.mapper.MeetingStandardMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 会议标准服务
 * @date 2025-01-24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingStandardService {

    private final MeetingStandardMapper meetingStandardMapper;
    private final MeetingPlanMapper meetingPlanMapper;
    private final MeetingStandardAssembler meetingStandardAssembler;

    /**
     * 查询启用的会议标准列表
     */
    public List<MeetingStandardDTO> listEnabled() {
        LambdaQueryWrapper<MeetingStandardPO> wrapper = Wrappers.lambdaQuery(MeetingStandardPO.class);
        wrapper.eq(MeetingStandardPO::getIsEnabled, 1);
        wrapper.orderByDesc(MeetingStandardPO::getCreateTime);
        
        List<MeetingStandardPO> pos = meetingStandardMapper.selectList(wrapper);
        return pos.stream()
            .map(po -> meetingStandardAssembler.toDTO(meetingStandardAssembler.toEntity(po)))
            .collect(Collectors.toList());
    }

    /**
     * 根据ID查询会议标准详情
     */
    public MeetingStandardDTO getById(Long id) {
        MeetingStandardPO po = meetingStandardMapper.selectById(id);
        if (po == null) {
            return null;
        }
        MeetingStandard entity = meetingStandardAssembler.toEntity(po);
        return meetingStandardAssembler.toDTO(entity);
    }

    /**
     * 分页查询会议标准
     */
    public PageResultDTO<MeetingStandardDTO> pageQuery(MeetingStandardQuery query) {
        LambdaQueryWrapper<MeetingStandardPO> wrapper = Wrappers.lambdaQuery(MeetingStandardPO.class);

        // 标准名称模糊查询
        if (query.getStandardName() != null && !query.getStandardName().trim().isEmpty()) {
            wrapper.like(MeetingStandardPO::getStandardName, query.getStandardName().trim());
        }

        // 优先级筛选
        if (query.getPriorityLevel() != null) {
            wrapper.eq(MeetingStandardPO::getPriorityLevel, query.getPriorityLevel());
        }

        // 启用状态筛选
        if (query.getIsEnabled() != null) {
            wrapper.eq(MeetingStandardPO::getIsEnabled, query.getIsEnabled());
        }

        // 按创建时间倒序
        wrapper.orderByDesc(MeetingStandardPO::getCreateTime);

        Page<MeetingStandardPO> page = new Page<>(query.getPageNum(), query.getPageSize());
        IPage<MeetingStandardPO> result = meetingStandardMapper.selectPage(page, wrapper);

        // 转换为 PageResultDTO
        List<MeetingStandardDTO> dtoList = result.getRecords().stream()
                .map(po -> meetingStandardAssembler.toDTO(meetingStandardAssembler.toEntity(po)))
                .collect(Collectors.toList());

        PageResultDTO<MeetingStandardDTO> pageResult = new PageResultDTO<>();
        pageResult.setPageNo(Math.toIntExact(result.getCurrent()));
        pageResult.setPageSize(Math.toIntExact(result.getSize()));
        pageResult.setTotal(result.getTotal());
        pageResult.setTotalPages(result.getPages());
        pageResult.setList(dtoList);

        return pageResult;
    }

    /**
     * 创建会议标准
     */
    @Transactional(rollbackFor = Exception.class)
    public void createMeetingStandard(MeetingStandardCreateCommand command) {
        // 1. 设置默认值
        setDefaultValues(command);
        
        // 2. 转换为领域对象
        MeetingStandard meetingStandard = meetingStandardAssembler.toEntity(command);
        
        // 3. 基础验证
        validateMeetingStandard(meetingStandard);
        
        // 4. 检查名称唯一性
        if (existsByName(meetingStandard.getStandardName(), null)) {
            throw new RuntimeException("标准名称已存在");
        }
        
        // 5. 保存
        MeetingStandardPO po = meetingStandardAssembler.toPO(meetingStandard);
        meetingStandardMapper.insert(po);
    }

    /**
     * 更新会议标准
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMeetingStandard(MeetingStandardUpdateCommand command) {
        // 1. 检查是否存在
        MeetingStandard existing = meetingStandardAssembler.toEntity(meetingStandardMapper.selectById(command.getId()));
        if (existing == null) {
            throw new RuntimeException("会议标准不存在");
        }
        
        // 2. 转换为领域对象
        MeetingStandard meetingStandard = meetingStandardAssembler.toEntity(command);
        
        // 3. 基础验证
        validateMeetingStandard(meetingStandard);
        
        // 4. 检查名称唯一性（排除自己）
        if (existsByName(meetingStandard.getStandardName(), meetingStandard.getId())) {
            throw new RuntimeException("标准名称已存在");
        }
        
        // 5. 更新
        MeetingStandardPO po = meetingStandardAssembler.toPO(meetingStandard);
        meetingStandardMapper.updateById(po);
    }

    /**
     * 删除会议标准
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteMeetingStandard(Long id) {
        // 1. 检查是否存在
        MeetingStandard existing = meetingStandardAssembler.toEntity(meetingStandardMapper.selectById(id));
        if (existing == null) {
            throw new RuntimeException("会议标准不存在");
        }
        
        // 2. 检查是否被使用
        if (isUsedByMeetingPlan(id)) {
            throw new RuntimeException("该会议标准已被会议规划使用，无法删除");
        }
        
        // 3. 删除
        meetingStandardMapper.deleteById(id);
    }

    /**
     * 获取所有会议角色
     */
    public List<RoleInfoDTO> getRoles() {
        return java.util.Arrays.stream(cn.july.orch.meeting.enums.MeetingRoleEnum.values())
            .map(role -> RoleInfoDTO.builder()
                .code(role.getCode())
                .name(role.getName())
                .build())
            .collect(Collectors.toList());
    }

    /**
     * 获取会议标准的参会人数统计
     */
    public AttendeeStatisticsDTO getAttendeeStatistics(Long standardId) {
        // 查询使用该标准的会议规划
        LambdaQueryWrapper<MeetingPlanPO> wrapper = Wrappers.lambdaQuery(MeetingPlanPO.class)
            .eq(MeetingPlanPO::getMeetingStandardId, standardId)
            .select(MeetingPlanPO::getAttendees);
        
        List<MeetingPlanPO> pos = meetingPlanMapper.selectList(wrapper);
        
        if (pos.isEmpty()) {
            return AttendeeStatisticsDTO.builder()
                .totalPlans(0)
                .minAttendees(0)
                .maxAttendees(0)
                .avgAttendees(0.0)
                .distribution(AttendeeStatisticsDTO.AttendeeDistribution.builder()
                    .smallMeetings(0)
                    .mediumMeetings(0)
                    .largeMeetings(0)
                    .extraLargeMeetings(0)
                    .build())
                .build();
        }
        
        // 统计参会人数
        List<Integer> attendeeCounts = pos.stream()
            .map(po -> po.getAttendees() != null ? po.getAttendees().size() : 0)
            .filter(count -> count > 0)
            .collect(Collectors.toList());
        
        if (attendeeCounts.isEmpty()) {
            return AttendeeStatisticsDTO.builder()
                .totalPlans(pos.size())
                .minAttendees(0)
                .maxAttendees(0)
                .avgAttendees(0.0)
                .distribution(AttendeeStatisticsDTO.AttendeeDistribution.builder()
                    .smallMeetings(0)
                    .mediumMeetings(0)
                    .largeMeetings(0)
                    .extraLargeMeetings(0)
                    .build())
                .build();
        }
        
        // 计算统计数据
        int minAttendees = attendeeCounts.stream().mapToInt(Integer::intValue).min().orElse(0);
        int maxAttendees = attendeeCounts.stream().mapToInt(Integer::intValue).max().orElse(0);
        double avgAttendees = attendeeCounts.stream().mapToInt(Integer::intValue).average().orElse(0.0);
        
        // 统计分布
        int smallMeetings = (int) attendeeCounts.stream().filter(count -> count >= 1 && count <= 5).count();
        int mediumMeetings = (int) attendeeCounts.stream().filter(count -> count >= 6 && count <= 15).count();
        int largeMeetings = (int) attendeeCounts.stream().filter(count -> count >= 16 && count <= 30).count();
        int extraLargeMeetings = (int) attendeeCounts.stream().filter(count -> count > 30).count();
        
        return AttendeeStatisticsDTO.builder()
            .totalPlans(pos.size())
            .minAttendees(minAttendees)
            .maxAttendees(maxAttendees)
            .avgAttendees(avgAttendees)
            .distribution(AttendeeStatisticsDTO.AttendeeDistribution.builder()
                .smallMeetings(smallMeetings)
                .mediumMeetings(mediumMeetings)
                .largeMeetings(largeMeetings)
                .extraLargeMeetings(extraLargeMeetings)
                .build())
            .build();
    }

    /**
     * 获取会议标准的参会人数建议
     */
    public AttendeeSuggestionDTO getAttendeeSuggestion(Long standardId) {
        MeetingStandard standard = meetingStandardAssembler.toEntity(meetingStandardMapper.selectById(standardId));
        if (standard == null) {
            throw new RuntimeException("会议标准不存在");
        }
        
        AttendeeStatisticsDTO statistics = getAttendeeStatistics(standardId);
        
        return AttendeeSuggestionDTO.builder()
            .standardId(standardId)
            .standardName(standard.getStandardName())
            .currentMinAttendees(standard.getMinAttendees())
            .currentMaxAttendees(standard.getMaxAttendees())
            .statistics(statistics)
            .suggestedMinAttendees(statistics.getMinAttendees() > 0 ? statistics.getMinAttendees() : 1)
            .suggestedMaxAttendees(statistics.getMaxAttendees() > 0 ? statistics.getMaxAttendees() : 50)
            .build();
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(MeetingStandardCreateCommand command) {
        // 设置默认时间单位为DAY
        if (command.getAdvanceNoticeUnit() == null) {
            command.setAdvanceNoticeUnit(TimeUnitEnum.DAY);
        }
        
        // 设置默认优先级为高
        if (command.getPriorityLevel() == null) {
            command.setPriorityLevel(PriorityLevelEnum.HIGH);
        }
    }

    /**
     * 验证会议标准基础数据
     */
    private void validateMeetingStandard(MeetingStandard meetingStandard) {
        // 1. 标准名称验证
        if (meetingStandard.getStandardName() == null || meetingStandard.getStandardName().trim().isEmpty()) {
            throw new RuntimeException("标准名称不能为空");
        }
        
        // 2. 持续时长验证（只在有值的情况下验证）
        if (meetingStandard.getDefaultDuration() != null && meetingStandard.getDefaultDuration() <= 0) {
            throw new RuntimeException("默认持续时长必须大于0");
        }
        
        // 3. 提前通知时间验证
        if (meetingStandard.getAdvanceNoticeValue() != null && meetingStandard.getAdvanceNoticeValue() < 0) {
            throw new RuntimeException("提前通知时间不能为负数");
        }
        
        // 4. 迟到容许时间验证
        if (meetingStandard.getLateToleranceMinutes() != null && meetingStandard.getLateToleranceMinutes() < 0) {
            throw new RuntimeException("迟到容许时间不能为负数");
        }
        
        // 5. 参会人数验证（只在有值的情况下验证）
        if (meetingStandard.getMinAttendees() != null && meetingStandard.getMinAttendees() <= 0) {
            throw new RuntimeException("最少参会人数必须大于0");
        }
        
        if (meetingStandard.getMaxAttendees() != null && meetingStandard.getMaxAttendees() <= 0) {
            throw new RuntimeException("最多参会人数必须大于0");
        }
        
        if (meetingStandard.getMinAttendees() != null && meetingStandard.getMaxAttendees() != null) {
            if (meetingStandard.getMinAttendees() > meetingStandard.getMaxAttendees()) {
                throw new RuntimeException("最少参会人数不能大于最多参会人数");
            }
        }
        
        // 6. 必要角色验证
        if (meetingStandard.getRequiredRoles() != null && !meetingStandard.getRequiredRoles().isEmpty()) {
            for (MeetingRoleEnum role : meetingStandard.getRequiredRoles()) {
                if (role == null) {
                    throw new RuntimeException("必要角色不能为空");
                }
            }
        }
    }

    /**
     * 检查标准名称是否存在
     */
    private boolean existsByName(String standardName, Long excludeId) {
        return meetingStandardMapper.countByName(standardName, excludeId) > 0;
    }

    /**
     * 检查是否被会议规划使用
     */
    private boolean isUsedByMeetingPlan(Long standardId) {
        return meetingStandardMapper.countUsedByMeetingPlan(standardId) > 0;
    }

}
