package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.dto.PageResultDTO;
import cn.july.orch.meeting.domain.command.MeetingPlanCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingPlanUpdateCommand;
import cn.july.orch.meeting.domain.dto.MeetingPlanCalendarDTO;
import cn.july.orch.meeting.domain.dto.MeetingPlanDTO;
import cn.july.orch.meeting.domain.dto.MeetingPlanListDTO;
import cn.july.orch.meeting.domain.entity.MeetingPlan;
import cn.july.orch.meeting.domain.po.MeetingPlanPO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description 会议规划组装器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingPlanAssembler {

    MeetingPlan toEntity(MeetingPlanCreateCommand command);

    MeetingPlan toEntity(MeetingPlanUpdateCommand command);

    MeetingPlanDTO toDTO(MeetingPlan meetingPlan);

    MeetingPlanListDTO toListDTO(MeetingPlan meetingPlan);

    MeetingPlanCalendarDTO toCalendarDTO(MeetingPlan meetingPlan);

    MeetingPlan toEntity(MeetingPlanPO po);

    MeetingPlanPO toPO(MeetingPlan meetingPlan);

    @Mapping(target = "pageNo", source = "current")
    @Mapping(target = "pageSize", source = "size")
    @Mapping(target = "records", source = "records")
    @Mapping(target = "total", source = "total")
    PageResultDTO<MeetingPlanListDTO> toPageResult(Page<MeetingPlanListDTO> page);
}
