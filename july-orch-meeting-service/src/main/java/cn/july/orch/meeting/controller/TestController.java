package cn.july.orch.meeting.controller;

import cn.july.feishu.FeishuAppClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private FeishuAppClient feishuAppClient;

    @PostMapping("/hello")
    public Object hello() {
        return feishuAppClient.getContactService().getOpenId("18435186300");
    }
}
