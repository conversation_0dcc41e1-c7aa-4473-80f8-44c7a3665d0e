package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.dto.FileInfoDTO;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface FileAssembler {


    List<FileInfoDTO> PO2DTO(List<FileDetailPO> pos);

    @Mapping(source = "id", target = "fileKey")
    FileInfoDTO PO2DTO(FileDetailPO pos);
}
