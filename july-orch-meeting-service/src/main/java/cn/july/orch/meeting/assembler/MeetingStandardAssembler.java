package cn.july.orch.meeting.assembler;

import cn.july.orch.meeting.domain.command.MeetingStandardCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingStandardUpdateCommand;
import cn.july.orch.meeting.domain.dto.MeetingStandardDTO;
import cn.july.orch.meeting.domain.entity.MeetingStandard;
import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准转换器
 * @date 2025-01-24
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MeetingStandardAssembler {

    MeetingStandardPO toPO(MeetingStandard meetingStandard);

    MeetingStandard toEntity(MeetingStandardPO meetingStandardPO);

    MeetingStandardDTO toDTO(MeetingStandard meetingStandard);

    List<MeetingStandardDTO> toDTOList(List<MeetingStandard> meetingStandards);

    MeetingStandard toEntity(MeetingStandardCreateCommand command);

    MeetingStandard toEntity(MeetingStandardUpdateCommand command);
}
