package cn.july.orch.meeting.domain.po;

import cn.july.orch.meeting.enums.MeetingRoleEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准持久化对象
 * @date 2025-01-24
 */
@Data
@TableName("meeting_standard")
public class MeetingStandardPO {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String standardName;

    private String standardDescription;

    private Integer minAttendees;

    private Integer maxAttendees;

    private Integer maxDuration;

    private PriorityLevelEnum defaultPriorityLevel;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<MeetingRoleEnum> requiredRoles;

    private String createUserId;

    private String createUserName;

    @TableField(fill = FieldFill.INSERT)
    private java.time.LocalDateTime createTime;

    private String updateUserId;

    private String updateUserName;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private java.time.LocalDateTime updateTime;
}
