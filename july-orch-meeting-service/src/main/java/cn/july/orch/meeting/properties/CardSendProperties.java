package cn.july.orch.meeting.properties;

import lombok.Data;

@Data
public class CardSendProperties {

    private template template;

    private JumpUrl jumpUrl;


    @Data
    public static class template {
        // 签到码模板id
        private String signInCodeId;
        // 签到提醒
        private String signInRemindId;
        // 会议记录上传
        private String uploadMeetingRecordId;
        // 会议评价
        private String meetingEvaluationId;
        // 会议妙记分享
        private String shareMeetingVideoId;
        // 会议提前通知
        private String meetingNotificationId;
    }

    @Data
    public static class JumpUrl {
        //签到页链接
        private String signInUrl;

        //会议记录上传链接
        private String uploadUrl;

        //会议评价链接
        private String evaluationUrl;

        //会议详情链接
        private String meetingDetailUrl;
    }

}
