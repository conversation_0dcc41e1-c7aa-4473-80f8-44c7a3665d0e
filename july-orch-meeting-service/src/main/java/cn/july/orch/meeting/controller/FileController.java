package cn.july.orch.meeting.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.july.core.exception.BusinessException;
import cn.july.orch.meeting.assembler.FileAssembler;
import cn.july.orch.meeting.domain.command.FileKeyCommand;
import cn.july.orch.meeting.domain.dto.FileInfoDTO;
import cn.july.orch.meeting.domain.po.FileDetailPO;
import cn.july.orch.meeting.exception.MessageCode;
import cn.july.orch.meeting.service.FileDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

@Slf4j
@Api(tags = "上传下载")
@RestController
@RequestMapping("/file")
public class FileController {

    @Resource
    private FileStorageService fileStorageService;
    @Resource
    private FileDetailService fileDetailService;
    @Resource
    private FileAssembler fileAssembler;

    @PostMapping("/upload")
    @ApiOperation(value = "上传文件")
    public FileInfoDTO uploadFile(@RequestParam("file") MultipartFile file) {
        FileInfo fileInfo = fileStorageService.of(file).upload();
        if (fileInfo == null) {
            throw new BusinessException(MessageCode.UPLOAD_FILE_ERROR);
        }
        String presignedUrl = fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetMinute(new Date(), 60));
        return FileInfoDTO.builder().fileKey(fileInfo.getId()).url(presignedUrl).build();
    }

    @PostMapping("/getFileUrl")
    @ApiOperation(value = "获取文件链接")
    public String getFileUrl(@RequestParam("fileKey") String fileKey) {
        FileDetailPO fileDetailPO = fileDetailService.getById(fileKey);
        FileInfo fileInfo = fileStorageService.getFileInfoByUrl(fileDetailPO.getUrl());
        String presignedUrl = fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetMinute(new Date(), 60));
        return ObjUtil.isNull(fileDetailPO) ? null : presignedUrl;
    }

    @PostMapping("/batchFileUrl")
    @ApiOperation(value = "获取文件链接")
    public List<FileInfoDTO> getFileUrl(@Validated  @RequestBody FileKeyCommand command) {
        List<FileInfoDTO> fileInfoDTOList = CollUtil.newArrayList();
        List<FileDetailPO> poList = fileDetailService.listByIds(command.getFileKeys());
        for (FileDetailPO fileDetailPO : poList) {
            FileInfo fileInfo = fileStorageService.getFileInfoByUrl(fileDetailPO.getUrl());
            String presignedUrl = fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetMinute(new Date(), 60));
            FileInfoDTO fileInfoDTO = fileAssembler.PO2DTO(fileDetailPO);
            fileInfoDTO.setUrl(presignedUrl);
            fileInfoDTOList.add(fileInfoDTO);
        }
        return fileInfoDTOList;
    }

    @PostMapping("/download")
    @ApiOperation(value = "下载文件")
    public void exportFile(@RequestParam("fileKey") String fileKey, HttpServletResponse response) throws IOException {
        FileDetailPO fileDetailPO = fileDetailService.getById(fileKey);
        if (ObjUtil.isNull(fileDetailPO)) {
            throw new BusinessException(MessageCode.FILE_NOT_EXIST);
        }
        String filePath = fileDetailPO.getUrl();
        response.setContentType("application/octet-stream");
        String fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
        response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(fileName, "UTF-8") + "\"");
        OutputStream out = response.getOutputStream();
        FileInfo fileInfo = fileStorageService.getFileInfoByUrl(filePath);
        fileStorageService.download(fileInfo).outputStream(out);
    }
}
