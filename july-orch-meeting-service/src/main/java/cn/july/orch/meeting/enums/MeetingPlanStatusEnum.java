package cn.july.orch.meeting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 会议规划状态枚举
 * @date 2025-01-24
 */
@Getter
@AllArgsConstructor
public enum MeetingPlanStatusEnum {

    NOT_STARTED(0, "未开始"),
    IN_PROGRESS(1, "进行中"),
    COMPLETED(2, "已完成"),
    OVERDUE(3, "已逾期");

    @EnumValue
    @JsonValue
    private final Integer code;

    private final String desc;

    private static final Map<Integer, MeetingPlanStatusEnum> VALUES = new HashMap<>();

    static {
        for (final MeetingPlanStatusEnum item : MeetingPlanStatusEnum.values()) {
            VALUES.put(item.getCode(), item);
        }
    }

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static MeetingPlanStatusEnum of(int code) {
        return VALUES.get(code);
    }
}
