package cn.july.orch.meeting.domain.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * FilePartDetailPO对象
 *
 * <AUTHOR>
 * @desc 文件分片信息表，仅在手动分片上传时使用
 */
@Data
@Accessors(chain = true)
@TableName(value = "file_part_detail", autoResultMap = true)
public class FilePartDetailPO {

    /**
     * 分片id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 存储平台
     */
    @TableField("platform")
    private String platform;

    /**
     * 上传ID，仅在手动分片上传时使用
     */
    @TableField("upload_id")
    private String uploadId;

    /**
     * 分片 ETag
     */
    @TableField("e_tag")
    private String eTag;

    /**
     * 分片号。每一个上传的分片都有一个分片号，一般情况下取值范围是1~10000
     */
    @TableField("part_number")
    private Integer partNumber;

    /**
     * 文件大小，单位字节
     */
    @TableField("part_size")
    private Long partSize;

    /**
     * 哈希信息
     */
    @TableField("hash_info")
    private String hashInfo;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", updateStrategy = FieldStrategy.NEVER, fill = FieldFill.INSERT)
    private LocalDateTime createTime;

}
