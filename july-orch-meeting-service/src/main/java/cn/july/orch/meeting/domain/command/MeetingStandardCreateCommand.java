package cn.july.orch.meeting.domain.command;

import cn.july.orch.meeting.enums.MeetingRoleEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.TimeUnitEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准创建Command
 * @date 2025-01-24
 */
@Data
@ApiModel("会议标准创建请求")
public class MeetingStandardCreateCommand {

    @ApiModelProperty(value = "标准名称", required = true)
    @NotBlank(message = "标准名称不能为空")
    private String standardName;

    @ApiModelProperty("标准描述")
    private String description;

    @ApiModelProperty("默认持续时长(分钟)")
    private Integer defaultDuration;

    @ApiModelProperty("默认提前通知数值")
    private Integer advanceNoticeValue;

    @ApiModelProperty("默认提前通知时间单位")
    private TimeUnitEnum advanceNoticeUnit;

    @ApiModelProperty("默认优先级")
    private PriorityLevelEnum priorityLevel;

    @ApiModelProperty("默认会议地点")
    private String defaultLocation;

    @ApiModelProperty("迟到容许时间(分钟)")
    private Integer lateToleranceMinutes;

    @ApiModelProperty("人员必要角色列表")
    private List<MeetingRoleEnum> requiredRoles;

    @ApiModelProperty("最少参会人数")
    private Integer minAttendees;

    @ApiModelProperty("最多参会人数")
    private Integer maxAttendees;

    @ApiModelProperty("会议要点列表")
    private List<String> meetingPoints;

    @ApiModelProperty(value = "是否启用(0-否,1-是)", required = true)
    @NotNull(message = "是否启用不能为空")
    private Integer isEnabled;
}
