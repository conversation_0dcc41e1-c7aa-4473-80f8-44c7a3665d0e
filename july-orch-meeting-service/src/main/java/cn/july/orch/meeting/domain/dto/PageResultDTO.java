package cn.july.orch.meeting.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 分页结果DTO
 * @date 2025-01-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResultDTO<T> {

    /**
     * 当前页码
     */
    private Integer pageNo;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 数据列表
     */
    private List<T> records;

    /**
     * 创建空的分页结果
     */
    public static <T> PageResultDTO<T> empty(Integer pageNo, Integer pageSize) {
        return new PageResultDTO<>(pageNo, pageSize, 0L, null);
    }

    /**
     * 创建空的分页结果
     */
    public static <T> PageResultDTO<T> empty() {
        return new PageResultDTO<>(1, 10, 0L, null);
    }
}
