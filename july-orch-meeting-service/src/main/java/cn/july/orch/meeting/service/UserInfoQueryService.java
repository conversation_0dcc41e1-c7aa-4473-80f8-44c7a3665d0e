package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.dto.FSUserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 用户信息查询服务
 * @date 2025-01-24
 */
@Slf4j
@Service
public class UserInfoQueryService {

    /**
     * 根据openId获取用户基本信息（包含头像）
     * 注意：这是一个简化版本，实际项目中需要集成飞书API
     *
     * @param openId 用户openId
     * @return 用户基本信息
     */
    public FSUserInfoDTO getUserInfo(String openId) {
        if (openId == null || openId.trim().isEmpty()) {
            return null;
        }

        try {
            // TODO: 这里应该调用飞书API获取用户信息
            // 目前返回模拟数据
            return FSUserInfoDTO.builder()
                    .openId(openId)
                    .name("用户" + openId.substring(Math.max(0, openId.length() - 4)))
                    .avatarUrl("https://example.com/avatar/" + openId)
                    .build();

        } catch (Exception e) {
            log.warn("获取用户信息失败，openId: {}", openId, e);
        }

        return null;
    }

    /**
     * 批量获取用户基本信息（包含头像）
     *
     * @param openIds 用户openId列表
     * @return openId -> 用户基本信息的映射
     */
    public Map<String, FSUserInfoDTO> getUserInfos(List<String> openIds) {
        if (openIds == null || openIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            // TODO: 这里应该调用飞书API批量获取用户信息
            // 目前返回模拟数据
            return openIds.stream()
                    .filter(openId -> openId != null && !openId.trim().isEmpty())
                    .collect(Collectors.toMap(
                            openId -> openId,
                            openId -> FSUserInfoDTO.builder()
                                    .openId(openId)
                                    .name("用户" + openId.substring(Math.max(0, openId.length() - 4)))
                                    .avatarUrl("https://example.com/avatar/" + openId)
                                    .build(),
                            (existing, replacement) -> existing
                    ));

        } catch (Exception e) {
            log.warn("批量获取用户信息失败，openIds: {}", openIds, e);
        }

        return Collections.emptyMap();
    }
}
