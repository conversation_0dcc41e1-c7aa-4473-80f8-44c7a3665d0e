package cn.july.orch.meeting.domain.entity;

import cn.july.orch.meeting.enums.MeetingRoleEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.enums.TimeUnitEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准实体
 * @date 2025-01-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MeetingStandard {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 标准名称
     */
    private String standardName;

    /**
     * 标准描述
     */
    private String description;

    /**
     * 默认持续时长(分钟)
     */
    private Integer defaultDuration;

    /**
     * 默认提前通知数值
     */
    private Integer advanceNoticeValue;

    /**
     * 默认提前通知时间单位
     */
    private TimeUnitEnum advanceNoticeUnit;

    /**
     * 默认优先级
     */
    private PriorityLevelEnum priorityLevel;

    /**
     * 默认会议地点
     */
    private String defaultLocation;

    /**
     * 迟到容许时间(分钟)
     */
    private Integer lateToleranceMinutes;

    /**
     * 人员必要角色列表
     */
    private List<MeetingRoleEnum> requiredRoles;

    /**
     * 最少参会人数
     */
    private Integer minAttendees;

    /**
     * 最多参会人数
     */
    private Integer maxAttendees;

    /**
     * 会议要点列表
     */
    private List<String> meetingPoints;

    /**
     * 是否启用(0-否,1-是)
     */
    private Integer isEnabled;

    /**
     * 创建人ID
     */
    private String createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private String updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
