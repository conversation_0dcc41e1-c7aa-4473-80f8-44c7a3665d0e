package cn.july.orch.meeting.config;

import cn.july.orch.meeting.domain.dto.AuthConfigDTO;
import cn.july.orch.meeting.domain.dto.MyResourceTreeDTO;
import cn.july.orch.meeting.domain.dto.MyUserRoleRelDTO;
import cn.july.orch.meeting.enums.ClientTypeEnum;
import com.lark.oapi.service.vc.v1.enums.AuthTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;


@Data
public class UserInfoDTO {

    private String token;

    private String userAccessToken;

    private ClientTypeEnum clientType;

    /**
     * 权限层级:集团,区域公司,煤矿
     */
    private AuthTypeEnum authType;

    /**
     * 姓名
     */
    private String name;

    private String openId;

    private String mobile;

    /**
     * 工号
     */
    private String employeeNo;

    /**
     * 英文名称
     */
    private String enName;
    /**
     * 用户头像
     * <p> 示例值：www.feishu.cn/avatar/icon
     */
    private String avatarUrl;

    /**
     * 公司id
     */
    private Long companyId;

    private String companyName;

    //公司对应飞书部门id
    private String companyDepartmentId;

    /**
     * 角色
     */
    private List<MyUserRoleRelDTO> role;

    private LocalDateTime lastRefreshTime;

    /**
     * 用户数据权限,上下文中未缓存
     */
    private AuthConfigDTO authConfig;

    /**
     * 系统角色资源清单,上下文中未缓存
     */
    private List<MyResourceTreeDTO> resourceTree;

    private Set<String> permissions;


}
