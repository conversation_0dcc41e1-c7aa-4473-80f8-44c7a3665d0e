package cn.july.orch.meeting.mapper;

import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @description 会议标准Mapper
 * @date 2025-01-24
 */
@Mapper
public interface MeetingStandardMapper extends BaseMapper<MeetingStandardPO> {

    /**
     * 根据标准名称统计数量（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM meeting_standard WHERE standard_name = #{standardName} AND id != #{excludeId}")
    int countByName(@Param("standardName") String standardName, @Param("excludeId") Long excludeId);

    /**
     * 统计会议标准被会议规划使用的次数
     */
    @Select("SELECT COUNT(*) FROM meeting_plan WHERE meeting_standard_id = #{standardId}")
    int countUsedByMeetingPlan(@Param("standardId") Long standardId);
}
