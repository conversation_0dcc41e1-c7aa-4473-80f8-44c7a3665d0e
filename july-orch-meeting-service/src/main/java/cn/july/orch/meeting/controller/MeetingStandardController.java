package cn.july.orch.meeting.controller;

import cn.july.core.model.page.PageResultDTO;
import cn.july.orch.meeting.domain.command.MeetingStandardCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingStandardUpdateCommand;
import cn.july.orch.meeting.domain.dto.AttendeeStatisticsDTO;
import cn.july.orch.meeting.domain.dto.AttendeeSuggestionDTO;
import cn.july.orch.meeting.domain.dto.MeetingStandardDTO;
import cn.july.orch.meeting.domain.dto.RoleInfoDTO;
import cn.july.orch.meeting.domain.query.MeetingStandardQuery;
import cn.july.orch.meeting.service.MeetingStandardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议标准控制器
 * @date 2025-01-24
 */
@Api(tags = "会议标准")
@RestController
@RequestMapping("/meeting-standard")
@RequiredArgsConstructor
public class MeetingStandardController {

    private final MeetingStandardService meetingStandardService;

    @PostMapping("/list")
    @ApiOperation("查询启用的会议标准列表")
    public List<MeetingStandardDTO> list() {
        return meetingStandardService.listEnabled();
    }

    @GetMapping("/detail")
    @ApiOperation("查询会议标准详情")
    public MeetingStandardDTO detail(@RequestParam("id") Long id) {
        return meetingStandardService.getById(id);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询会议标准")
    public PageResultDTO<MeetingStandardDTO> pageQuery(@RequestBody MeetingStandardQuery query) {
        return meetingStandardService.pageQuery(query);
    }

    @PostMapping("/create")
    @ApiOperation("创建会议标准")
    public void create(@Valid @RequestBody MeetingStandardCreateCommand command) {
        meetingStandardService.createMeetingStandard(command);
    }

    @PostMapping("/update")
    @ApiOperation("更新会议标准")
    public void update(@Valid @RequestBody MeetingStandardUpdateCommand command) {
        meetingStandardService.updateMeetingStandard(command);
    }

    @PostMapping("/delete/{id}")
    @ApiOperation("删除会议标准")
    public void delete(@PathVariable("id") Long id) {
        meetingStandardService.deleteMeetingStandard(id);
    }

    @GetMapping("/roles")
    @ApiOperation("获取所有会议角色")
    public List<RoleInfoDTO> getRoles() {
        return meetingStandardService.getRoles();
    }

    @GetMapping("/attendee-statistics/{id}")
    @ApiOperation("获取会议标准的参会人数统计")
    public AttendeeStatisticsDTO getAttendeeStatistics(@PathVariable("id") Long id) {
        return meetingStandardService.getAttendeeStatistics(id);
    }

    @GetMapping("/attendee-suggestion/{id}")
    @ApiOperation("获取会议标准的参会人数建议")
    public AttendeeSuggestionDTO getAttendeeSuggestion(@PathVariable("id") Long id) {
        return meetingStandardService.getAttendeeSuggestion(id);
    }
}
