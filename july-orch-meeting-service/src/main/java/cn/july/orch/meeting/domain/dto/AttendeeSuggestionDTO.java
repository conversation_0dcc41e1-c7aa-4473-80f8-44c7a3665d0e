package cn.july.orch.meeting.domain.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 参会人数建议DTO
 * @date 2025-01-24
 */
@Data
public class AttendeeSuggestionDTO {

    private Long standardId;
    private String standardName;
    private Integer currentMinAttendees;
    private Integer currentMaxAttendees;
    private AttendeeStatisticsDTO statistics;
    private Integer suggestedMinAttendees;
    private Integer suggestedMaxAttendees;
    
    // 使用Builder模式
    public static AttendeeSuggestionDTOBuilder builder() {
        return new AttendeeSuggestionDTOBuilder();
    }
    
    public static class AttendeeSuggestionDTOBuilder {
        private Long standardId;
        private String standardName;
        private Integer currentMinAttendees;
        private Integer currentMaxAttendees;
        private AttendeeStatisticsDTO statistics;
        private Integer suggestedMinAttendees;
        private Integer suggestedMaxAttendees;
        
        public AttendeeSuggestionDTOBuilder standardId(Long standardId) {
            this.standardId = standardId;
            return this;
        }
        
        public AttendeeSuggestionDTOBuilder standardName(String standardName) {
            this.standardName = standardName;
            return this;
        }
        
        public AttendeeSuggestionDTOBuilder currentMinAttendees(Integer currentMinAttendees) {
            this.currentMinAttendees = currentMinAttendees;
            return this;
        }
        
        public AttendeeSuggestionDTOBuilder currentMaxAttendees(Integer currentMaxAttendees) {
            this.currentMaxAttendees = currentMaxAttendees;
            return this;
        }
        
        public AttendeeSuggestionDTOBuilder statistics(AttendeeStatisticsDTO statistics) {
            this.statistics = statistics;
            return this;
        }
        
        public AttendeeSuggestionDTOBuilder suggestedMinAttendees(Integer suggestedMinAttendees) {
            this.suggestedMinAttendees = suggestedMinAttendees;
            return this;
        }
        
        public AttendeeSuggestionDTOBuilder suggestedMaxAttendees(Integer suggestedMaxAttendees) {
            this.suggestedMaxAttendees = suggestedMaxAttendees;
            return this;
        }
        
        public AttendeeSuggestionDTO build() {
            AttendeeSuggestionDTO suggestion = new AttendeeSuggestionDTO();
            suggestion.standardId = this.standardId;
            suggestion.standardName = this.standardName;
            suggestion.currentMinAttendees = this.currentMinAttendees;
            suggestion.currentMaxAttendees = this.currentMaxAttendees;
            suggestion.statistics = this.statistics;
            suggestion.suggestedMinAttendees = this.suggestedMinAttendees;
            suggestion.suggestedMaxAttendees = this.suggestedMaxAttendees;
            return suggestion;
        }
    }
}
