package cn.july.orch.meeting.domain.query;

import lombok.Data;

/**
 * <AUTHOR>
 * @description 分页排序查询基类
 * @date 2025-01-24
 */
@Data
public class PageSortQuery {

    /**
     * 当前页码，从1开始
     */
    private Integer pageNo = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    private String sortField;

    /**
     * 排序方向：asc, desc
     */
    private String sortOrder = "desc";
}
