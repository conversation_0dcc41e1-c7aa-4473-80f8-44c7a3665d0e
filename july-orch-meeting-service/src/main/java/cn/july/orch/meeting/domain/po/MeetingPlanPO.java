package cn.july.orch.meeting.domain.po;

import cn.july.orch.meeting.enums.MeetingPlanStatusEnum;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划持久化对象
 * @date 2025-01-24
 */
@Data
@TableName("meeting_plan")
public class MeetingPlanPO {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String planName;

    private String planDescription;

    private LocalDateTime plannedStartTime;

    private LocalDateTime plannedEndTime;

    private Integer plannedDuration;

    private MeetingPlanStatusEnum status;

    private Long meetingStandardId;

    private PriorityLevelEnum priorityLevel;

    private Long departmentId;

    private String departmentName;

    private Long businessMeetingId;

    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attendees;

    private String meetingLocation;

    private Integer advanceNoticeSent;

    private String createUserId;

    private String createUserName;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    private String updateUserId;

    private String updateUserName;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
