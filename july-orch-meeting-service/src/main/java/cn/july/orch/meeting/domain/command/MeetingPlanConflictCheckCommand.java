package cn.july.orch.meeting.domain.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 会议规划冲突检查Command
 * @date 2025-01-24
 */
@Data
public class MeetingPlanConflictCheckCommand {

    @ApiModelProperty(value = "计划开始时间", required = true)
    @NotNull(message = "计划开始时间不能为空")
    private LocalDateTime plannedStartTime;

    @ApiModelProperty(value = "计划结束时间", required = true)
    @NotNull(message = "计划结束时间不能为空")
    private LocalDateTime plannedEndTime;

    @ApiModelProperty(value = "会议地点")
    private String meetingLocation;

    @ApiModelProperty(value = "参会人员列表")
    private List<String> attendees;

    @ApiModelProperty(value = "排除的会议规划ID（更新时使用）")
    private Long excludeId;
}
