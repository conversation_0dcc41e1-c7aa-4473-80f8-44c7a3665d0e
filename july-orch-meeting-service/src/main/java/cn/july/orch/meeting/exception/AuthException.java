package cn.july.orch.meeting.exception;

import cn.july.core.exception.BaseException;
import cn.july.core.exception.CommonCode;
import cn.july.core.exception.MessageCodeWrap;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AuthException extends BaseException {
    private static final long serialVersionUID = -3702323089829534951L;

    public AuthException() {
        super(CommonCode.FAIL);
    }

    public AuthException(String message) {
        super(CommonCode.FAIL.buildCode(), message);
    }

    public AuthException(String code, String message) {
        super(code, message);
    }

    public AuthException(String code, String message, Throwable throwable) {
        super(code, message, throwable);
    }

    public AuthException(MessageCodeWrap messageCode, Object... args) {
        super(messageCode, args);
    }
}
