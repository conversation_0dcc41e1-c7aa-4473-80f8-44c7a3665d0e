<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.MeetingPlanMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="cn.july.orch.meeting.domain.po.MeetingPlanPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="plan_name" property="planName" jdbcType="VARCHAR"/>
        <result column="plan_description" property="planDescription" jdbcType="VARCHAR"/>
        <result column="planned_start_time" property="plannedStartTime" jdbcType="TIMESTAMP"/>
        <result column="planned_end_time" property="plannedEndTime" jdbcType="TIMESTAMP"/>
        <result column="planned_duration" property="plannedDuration" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="meeting_standard_id" property="meetingStandardId" jdbcType="BIGINT"/>
        <result column="priority_level" property="priorityLevel" jdbcType="VARCHAR"/>
        <result column="department_id" property="departmentId" jdbcType="BIGINT"/>
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
        <result column="business_meeting_id" property="businessMeetingId" jdbcType="BIGINT"/>
        <result column="attendees" property="attendees" jdbcType="VARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="meeting_location" property="meetingLocation" jdbcType="VARCHAR"/>
        <result column="advance_notice_sent" property="advanceNoticeSent" jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, plan_name, plan_description, planned_start_time, planned_end_time, planned_duration,
        status, meeting_standard_id, priority_level, department_id, department_name, business_meeting_id,
        attendees, meeting_location, advance_notice_sent, create_user_id, create_user_name, create_time,
        update_user_id, update_user_name, update_time
    </sql>

</mapper>
