<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.july.orch.meeting.mapper.MeetingStandardMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="cn.july.orch.meeting.domain.po.MeetingStandardPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="standard_name" property="standardName" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="default_duration" property="defaultDuration" jdbcType="INTEGER"/>
        <result column="advance_notice_value" property="advanceNoticeValue" jdbcType="INTEGER"/>
        <result column="advance_notice_unit" property="advanceNoticeUnit" jdbcType="VARCHAR"/>
        <result column="priority_level" property="priorityLevel" jdbcType="INTEGER"/>
        <result column="default_location" property="defaultLocation" jdbcType="VARCHAR"/>
        <result column="late_tolerance_minutes" property="lateToleranceMinutes" jdbcType="INTEGER"/>
        <result column="required_roles" property="requiredRolesJson" jdbcType="VARCHAR"/>
        <result column="min_attendees" property="minAttendees" jdbcType="INTEGER"/>
        <result column="max_attendees" property="maxAttendees" jdbcType="INTEGER"/>
        <result column="meeting_points" property="meetingPointsJson" jdbcType="VARCHAR"/>
        <result column="is_enabled" property="isEnabled" jdbcType="INTEGER"/>
        <result column="create_user_id" property="createUserId" jdbcType="VARCHAR"/>
        <result column="create_user_name" property="createUserName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user_id" property="updateUserId" jdbcType="VARCHAR"/>
        <result column="update_user_name" property="updateUserName" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, standard_name, description, default_duration, advance_notice_value, advance_notice_unit,
        priority_level, default_location, late_tolerance_minutes, required_roles, min_attendees,
        max_attendees, meeting_points, is_enabled, create_user_id, create_user_name, create_time,
        update_user_id, update_user_name, update_time, is_deleted
    </sql>

    <!-- 检查标准名称是否存在 -->
    <select id="countByName" resultType="int">
        SELECT COUNT(1)
        FROM meeting_standard
        WHERE is_deleted = 0
        AND standard_name = #{standardName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查是否被会议规划使用 -->
    <select id="countUsedByMeetingPlan" resultType="int">
        SELECT COUNT(1)
        FROM meeting_plan
        WHERE is_deleted = 0
        AND meeting_standard_id = #{standardId}
    </select>

</mapper>
