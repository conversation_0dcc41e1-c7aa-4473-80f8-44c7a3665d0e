-- 文件记录表
CREATE TABLE IF NOT EXISTS `file_detail` (
  `id` varchar(32) NOT NULL COMMENT '文件id',
  `url` varchar(500) DEFAULT NULL COMMENT '文件访问地址',
  `size` bigint DEFAULT NULL COMMENT '文件大小，单位字节',
  `filename` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `original_filename` varchar(255) DEFAULT NULL COMMENT '原始文件名',
  `base_path` varchar(255) DEFAULT NULL COMMENT '基础存储路径',
  `path` varchar(255) DEFAULT NULL COMMENT '存储路径',
  `ext` varchar(20) DEFAULT NULL COMMENT '文件扩展名',
  `content_type` varchar(255) DEFAULT NULL COMMENT 'MIME类型',
  `platform` varchar(50) DEFAULT NULL COMMENT '存储平台',
  `th_url` varchar(500) DEFAULT NULL COMMENT '缩略图访问路径',
  `th_filename` varchar(255) DEFAULT NULL COMMENT '缩略图名称',
  `th_size` bigint DEFAULT NULL COMMENT '缩略图大小，单位字节',
  `th_content_type` varchar(255) DEFAULT NULL COMMENT '缩略图MIME类型',
  `object_id` varchar(32) DEFAULT NULL COMMENT '文件所属对象id',
  `object_type` varchar(50) DEFAULT NULL COMMENT '文件所属对象类型，例如用户头像，评价图片',
  `metadata` text COMMENT '文件元数据',
  `user_metadata` text COMMENT '文件用户元数据',
  `th_metadata` text COMMENT '缩略图元数据',
  `th_user_metadata` text COMMENT '缩略图用户元数据',
  `attr` text COMMENT '附加属性',
  `file_acl` varchar(50) DEFAULT NULL COMMENT '文件ACL',
  `th_file_acl` varchar(50) DEFAULT NULL COMMENT '缩略图文件ACL',
  `hash_info` text COMMENT '哈希信息',
  `upload_id` varchar(100) DEFAULT NULL COMMENT '上传ID，仅在手动分片上传时使用',
  `upload_status` int DEFAULT NULL COMMENT '上传状态，仅在手动分片上传时使用，1：初始化完成，2：上传完成',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_url` (`url`),
  KEY `idx_object_id` (`object_id`),
  KEY `idx_upload_id` (`upload_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件记录表';

-- 文件分片信息表
CREATE TABLE IF NOT EXISTS `file_part_detail` (
  `id` varchar(32) NOT NULL COMMENT '分片id',
  `platform` varchar(50) DEFAULT NULL COMMENT '存储平台',
  `upload_id` varchar(100) DEFAULT NULL COMMENT '上传ID，仅在手动分片上传时使用',
  `e_tag` varchar(100) DEFAULT NULL COMMENT '分片 ETag',
  `part_number` int DEFAULT NULL COMMENT '分片号。每一个上传的分片都有一个分片号，一般情况下取值范围是1~10000',
  `part_size` bigint DEFAULT NULL COMMENT '文件大小，单位字节',
  `hash_info` text COMMENT '哈希信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_upload_id` (`upload_id`),
  KEY `idx_platform` (`platform`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件分片信息表，仅在手动分片上传时使用';
