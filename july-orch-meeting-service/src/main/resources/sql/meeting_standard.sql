-- 会议标准表
CREATE TABLE `meeting_standard` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `standard_name` varchar(255) NOT NULL COMMENT '标准名称',
  `standard_description` text COMMENT '标准描述',
  `min_attendees` int(11) COMMENT '最少参会人数',
  `max_attendees` int(11) COMMENT '最多参会人数',
  `max_duration` int(11) COMMENT '最大会议时长(分钟)',
  `default_priority_level` varchar(50) COMMENT '默认优先级',
  `required_roles` json COMMENT '必要角色列表(JSON格式)',
  `create_user_id` varchar(100) NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` varchar(100) COMMENT '更新人ID',
  `update_user_name` varchar(100) COMMENT '更新人姓名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_standard_name` (`standard_name`),
  KEY `idx_create_user_id` (`create_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议标准表';

-- 插入示例数据
INSERT INTO `meeting_standard` (
    `standard_name`, 
    `standard_description`, 
    `min_attendees`, 
    `max_attendees`, 
    `max_duration`, 
    `default_priority_level`, 
    `required_roles`, 
    `create_user_id`, 
    `create_user_name`
) VALUES 
(
    '标准会议',
    '适用于一般性会议的标准配置',
    3,
    20,
    120,
    'NORMAL',
    '["CHAIRPERSON", "RECORDER", "PARTICIPANT"]',
    'admin',
    '系统管理员'
),
(
    '重要会议',
    '适用于重要决策和评审会议的标准配置',
    5,
    15,
    180,
    'HIGH',
    '["CHAIRPERSON", "RECORDER", "PARTICIPANT", "DECISION_MAKER"]',
    'admin',
    '系统管理员'
),
(
    '培训会议',
    '适用于培训和知识分享会议的标准配置',
    2,
    50,
    240,
    'LOW',
    '["TRAINER", "PARTICIPANT"]',
    'admin',
    '系统管理员'
);
