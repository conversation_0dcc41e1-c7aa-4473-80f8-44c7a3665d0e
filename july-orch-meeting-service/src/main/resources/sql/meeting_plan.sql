-- 会议规划表
CREATE TABLE `meeting_plan` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_name` varchar(255) NOT NULL COMMENT '会议规划名称',
  `plan_description` text COMMENT '会议规划描述/备注',
  `planned_start_time` datetime NOT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime NOT NULL COMMENT '计划结束时间',
  `planned_duration` int(11) COMMENT '计划持续时长(分钟)',
  `status` varchar(50) NOT NULL DEFAULT 'NOT_STARTED' COMMENT '会议规划状态',
  `meeting_standard_id` bigint(20) NOT NULL COMMENT '会议标准ID',
  `priority_level` varchar(50) COMMENT '优先级',
  `department_id` bigint(20) COMMENT '部门ID',
  `department_name` varchar(255) COMMENT '部门名称',
  `business_meeting_id` bigint(20) COMMENT '业务会议ID',
  `attendees` json COMMENT '参会人员列表(JSON格式)',
  `meeting_location` varchar(255) COMMENT '会议地点',
  `advance_notice_sent` tinyint(1) NOT NULL DEFAULT '0' COMMENT '提前通知发送标记(0-未发送,1-已发送)',
  `create_user_id` varchar(100) NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` varchar(100) COMMENT '更新人ID',
  `update_user_name` varchar(100) COMMENT '更新人姓名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_meeting_standard_id` (`meeting_standard_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_business_meeting_id` (`business_meeting_id`),
  KEY `idx_planned_start_time` (`planned_start_time`),
  KEY `idx_status` (`status`),
  KEY `idx_create_user_id` (`create_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会议规划表';

-- 插入示例数据
INSERT INTO `meeting_plan` (
    `plan_name`, 
    `plan_description`, 
    `planned_start_time`, 
    `planned_end_time`, 
    `planned_duration`, 
    `status`, 
    `meeting_standard_id`, 
    `priority_level`, 
    `department_id`, 
    `department_name`, 
    `business_meeting_id`, 
    `attendees`, 
    `meeting_location`, 
    `advance_notice_sent`, 
    `create_user_id`, 
    `create_user_name`
) VALUES 
(
    '产品需求评审会议',
    '讨论新版本产品功能需求，确定开发优先级',
    '2025-01-25 09:00:00',
    '2025-01-25 11:00:00',
    120,
    'NOT_STARTED',
    1,
    'HIGH',
    1,
    '产品部',
    NULL,
    '["user001", "user002", "user003"]',
    '会议室A',
    0,
    'admin',
    '系统管理员'
),
(
    '技术架构讨论会议',
    '讨论系统架构设计，确定技术选型',
    '2025-01-26 14:00:00',
    '2025-01-26 16:00:00',
    120,
    'NOT_STARTED',
    1,
    'MEDIUM',
    2,
    '技术部',
    NULL,
    '["user004", "user005", "user006"]',
    '会议室B',
    0,
    'admin',
    '系统管理员'
),
(
    '项目进度汇报会议',
    '各项目组汇报当前进度，讨论遇到的问题',
    '2025-01-27 10:00:00',
    '2025-01-27 11:30:00',
    90,
    'NOT_STARTED',
    2,
    'NORMAL',
    3,
    '项目管理部',
    NULL,
    '["user007", "user008", "user009", "user010"]',
    '会议室C',
    0,
    'admin',
    '系统管理员'
);
