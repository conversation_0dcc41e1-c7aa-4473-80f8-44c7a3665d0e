package cn.july.orch.meeting.controller;

import cn.july.orch.meeting.domain.command.MeetingPlanCreateCommand;
import cn.july.orch.meeting.domain.command.MeetingPlanUpdateCommand;
import cn.july.orch.meeting.domain.command.MeetingPlanConflictCheckCommand;
import cn.july.orch.meeting.domain.query.MeetingPlanQuery;
import cn.july.orch.meeting.domain.query.MeetingPlanCalendarQuery;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * <AUTHOR>
 * @description MeetingPlanController 测试类
 * @date 2025-01-24
 */
@WebMvcTest(MeetingPlanController.class)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class MeetingPlanControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testCreateMeetingPlan() throws Exception {
        MeetingPlanCreateCommand command = new MeetingPlanCreateCommand();
        command.setPlanName("测试会议规划");
        command.setPlanDescription("这是一个测试会议规划");
        command.setPlannedStartTime(LocalDateTime.now().plusDays(1));
        command.setPlannedEndTime(LocalDateTime.now().plusDays(1).plusHours(2));
        command.setMeetingStandardId(1L);
        command.setPriorityLevel(PriorityLevelEnum.HIGH);
        command.setMeetingLocation("会议室A");
        command.setAttendees(Arrays.asList("user001", "user002"));

        mockMvc.perform(post("/api/meeting-plan")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(command)))
                .andExpect(status().isOk());
    }

    @Test
    public void testUpdateMeetingPlan() throws Exception {
        MeetingPlanUpdateCommand command = new MeetingPlanUpdateCommand();
        command.setId(1L);
        command.setPlanName("更新后的会议规划");
        command.setPlanDescription("这是一个更新后的会议规划");
        command.setPlannedStartTime(LocalDateTime.now().plusDays(2));
        command.setPlannedEndTime(LocalDateTime.now().plusDays(2).plusHours(2));
        command.setMeetingStandardId(1L);
        command.setPriorityLevel(PriorityLevelEnum.MEDIUM);
        command.setMeetingLocation("会议室B");
        command.setAttendees(Arrays.asList("user001", "user003"));

        mockMvc.perform(post("/api/meeting-plan/update")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(command)))
                .andExpect(status().isOk());
    }

    @Test
    public void testDeleteMeetingPlan() throws Exception {
        mockMvc.perform(post("/api/meeting-plan/delete/1"))
                .andExpect(status().isOk());
    }

    @Test
    public void testQueryPage() throws Exception {
        mockMvc.perform(get("/api/meeting-plan/page")
                .param("pageNo", "1")
                .param("pageSize", "10")
                .param("planName", "测试"))
                .andExpect(status().isOk());
    }

    @Test
    public void testGetById() throws Exception {
        mockMvc.perform(get("/api/meeting-plan/1"))
                .andExpect(status().isOk());
    }

    @Test
    public void testQueryCalendar() throws Exception {
        mockMvc.perform(get("/api/meeting-plan/calendar")
                .param("startDate", "2025-01-01")
                .param("endDate", "2025-01-31"))
                .andExpect(status().isOk());
    }

    @Test
    public void testCheckConflicts() throws Exception {
        MeetingPlanConflictCheckCommand command = new MeetingPlanConflictCheckCommand();
        command.setPlannedStartTime(LocalDateTime.now().plusDays(1));
        command.setPlannedEndTime(LocalDateTime.now().plusDays(1).plusHours(2));
        command.setMeetingLocation("会议室A");
        command.setAttendees(Arrays.asList("user001", "user002"));

        mockMvc.perform(post("/api/meeting-plan/check-conflicts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(command)))
                .andExpect(status().isOk());
    }
}
