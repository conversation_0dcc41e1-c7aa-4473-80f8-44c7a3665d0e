package cn.july.orch.meeting.service;

import cn.july.orch.meeting.domain.po.MeetingStandardPO;
import cn.july.orch.meeting.domain.query.MeetingStandardQuery;
import cn.july.orch.meeting.enums.PriorityLevelEnum;
import cn.july.orch.meeting.mapper.MeetingStandardMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

/**
 * <AUTHOR>
 * @description 测试 MeetingStandardService 字段引用是否正确
 * @date 2025-01-24
 */
@SpringBootTest
public class MeetingStandardServiceFieldTest {

    @MockBean
    private MeetingStandardMapper meetingStandardMapper;

    @Test
    public void testFieldReferencesCompile() {
        // 测试字段引用是否能正常编译
        assertDoesNotThrow(() -> {
            LambdaQueryWrapper<MeetingStandardPO> wrapper = Wrappers.lambdaQuery(MeetingStandardPO.class);
            
            // 测试 priorityLevel 字段
            wrapper.eq(MeetingStandardPO::getPriorityLevel, PriorityLevelEnum.HIGH);
            
            // 测试 isEnabled 字段
            wrapper.eq(MeetingStandardPO::getIsEnabled, 1);
            
            // 测试其他新增字段
            wrapper.eq(MeetingStandardPO::getDefaultDuration, 60);
            wrapper.eq(MeetingStandardPO::getAdvanceNoticeValue, 30);
            wrapper.eq(MeetingStandardPO::getDefaultLocation, "会议室A");
            wrapper.eq(MeetingStandardPO::getLateToleranceMinutes, 15);
            wrapper.eq(MeetingStandardPO::getMinAttendees, 3);
            wrapper.eq(MeetingStandardPO::getMaxAttendees, 10);
            
            // 测试查询对象
            MeetingStandardQuery query = new MeetingStandardQuery();
            query.setPriorityLevel(PriorityLevelEnum.HIGH);
            query.setIsEnabled(1);
            
            System.out.println("所有字段引用测试通过！");
        });
    }

    @Test
    public void testPOFieldsExist() {
        // 测试 PO 类的字段是否存在
        assertDoesNotThrow(() -> {
            MeetingStandardPO po = new MeetingStandardPO();
            
            // 测试所有字段的 getter/setter
            po.setId(1L);
            po.setStandardName("测试标准");
            po.setDescription("测试描述");
            po.setDefaultDuration(60);
            po.setAdvanceNoticeValue(30);
            po.setPriorityLevel(PriorityLevelEnum.HIGH);
            po.setDefaultLocation("会议室A");
            po.setLateToleranceMinutes(15);
            po.setMinAttendees(3);
            po.setMaxAttendees(10);
            po.setIsEnabled(1);
            
            // 验证 getter 方法
            assert po.getId().equals(1L);
            assert po.getStandardName().equals("测试标准");
            assert po.getDescription().equals("测试描述");
            assert po.getDefaultDuration().equals(60);
            assert po.getAdvanceNoticeValue().equals(30);
            assert po.getPriorityLevel().equals(PriorityLevelEnum.HIGH);
            assert po.getDefaultLocation().equals("会议室A");
            assert po.getLateToleranceMinutes().equals(15);
            assert po.getMinAttendees().equals(3);
            assert po.getMaxAttendees().equals(10);
            assert po.getIsEnabled().equals(1);
            
            System.out.println("PO 类字段测试通过！");
        });
    }
}
